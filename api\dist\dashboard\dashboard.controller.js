"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardController = void 0;
const common_1 = require("@nestjs/common");
const dashboard_service_1 = require("./dashboard.service");
const swagger_1 = require("@nestjs/swagger");
let DashboardController = class DashboardController {
    constructor(dashboardService) {
        this.dashboardService = dashboardService;
    }
    async getDashboardMetrics(timeRange = 'all', agentType = 'all') {
        return this.dashboardService.getDashboardMetrics(timeRange, agentType);
    }
    async refreshStats() {
        return this.dashboardService.clearCachedStats();
    }
};
exports.DashboardController = DashboardController;
__decorate([
    (0, common_1.Get)('metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get dashboard metrics and statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Dashboard metrics retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, description: 'Time range in days (e.g., 7, 30, all)' }),
    (0, swagger_1.ApiQuery)({ name: 'agentType', required: false, description: 'Filter by agent type/role' }),
    __param(0, (0, common_1.Query)('timeRange')),
    __param(1, (0, common_1.Query)('agentType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getDashboardMetrics", null);
__decorate([
    (0, common_1.Post)('refresh-stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Clear cached stats and force refresh' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Stats cache cleared successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "refreshStats", null);
exports.DashboardController = DashboardController = __decorate([
    (0, swagger_1.ApiTags)('Dashboard'),
    (0, common_1.Controller)('dashboard'),
    __metadata("design:paramtypes", [dashboard_service_1.DashboardService])
], DashboardController);
//# sourceMappingURL=dashboard.controller.js.map