
import { Document } from 'mongoose';

export interface TransferPlan {
  mode: string;
}

export interface FallbackDestination {
  type: string;
  number: string;
  callerId: string;
  description: string;
  extension: string;
  message: string;
  numberE164CheckEnabled: boolean;
  transferPlan: TransferPlan;
}

export interface HookAction {
  type: string;
}

export interface Hook {
  on: string;
  do: HookAction[];
}

export interface BackoffPlan {
  maxRetries: number;
  type: any;
  baseDelaySeconds: number;
}

export interface Server {
  url: string;
  timeoutSeconds: number;
  secret: string;
  headers: any;
  backoffPlan: BackoffPlan;
}

export interface PhoneNumber {
  id: string;
  provider: string;
  createdAt: Date;
  updatedAt: Date;
  orgId: string;
  credentialId: string;
  assistantId: string;
  name: string;
  number: string;
  numberE164CheckEnabled: boolean;
  status: string;
  squadId: string;
  workflowId: string;
  fallbackDestination: FallbackDestination;
  hooks: Hook[];
  server: Server;
}

export type PhoneNumberDocument = PhoneNumber & Document;