import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PhoneNumberService } from './phone-number.service';
import { PhoneNumberController } from './phone-number.controller';
import { PhoneNumberSchema } from './schemas/phone-number.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'PhoneNumber', schema: PhoneNumberSchema }]),
  ],
  providers: [PhoneNumberService],
  controllers: [PhoneNumberController],
  exports: [PhoneNumberService],
})

export class PhoneNumberModule {}