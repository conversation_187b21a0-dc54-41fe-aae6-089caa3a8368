"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhoneNumberService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let PhoneNumberService = class PhoneNumberService {
    constructor(phoneNumberModel) {
        this.phoneNumberModel = phoneNumberModel;
        this.VAPI_API_TOKEN = process.env.VAPI_API_TOKEN || '';
    }
    async findAll() {
        try {
            const response = await fetch('https://api.vapi.ai/phone-number', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }
            const vapiPhoneNumbers = await response.json();
            const existingPhoneNumbers = await this.phoneNumberModel.find().exec();
            const vapiPhoneNumberIds = new Set(vapiPhoneNumbers.map(phoneNumber => phoneNumber.id));
            for (const dbPhoneNumber of existingPhoneNumbers) {
                if (!vapiPhoneNumberIds.has(dbPhoneNumber.id)) {
                    await this.phoneNumberModel.findByIdAndDelete(dbPhoneNumber._id).exec();
                }
            }
            for (const vapiPhoneNumber of vapiPhoneNumbers) {
                const existingPhoneNumber = await this.phoneNumberModel.findOne({ id: vapiPhoneNumber.id }).exec();
                if (!existingPhoneNumber) {
                    const newPhoneNumber = new this.phoneNumberModel({
                        ...vapiPhoneNumber,
                    });
                    await newPhoneNumber.save();
                }
                else {
                    await this.phoneNumberModel.findOneAndUpdate({ id: vapiPhoneNumber.id }, {
                        ...vapiPhoneNumber,
                        status: vapiPhoneNumber.status || existingPhoneNumber.status || 'active'
                    }, { new: true }).exec();
                }
            }
            return this.phoneNumberModel.find().exec();
        }
        catch (error) {
            console.error('Error syncing phone numbers:', error.message);
            return this.phoneNumberModel.find().exec();
        }
    }
    async findById(id) {
        let phoneNumber;
        try {
            phoneNumber = await this.phoneNumberModel.findById(id).exec();
        }
        catch (error) {
            phoneNumber = await this.phoneNumberModel.findOne({ id: id }).exec();
        }
        if (!phoneNumber) {
            throw new common_1.NotFoundException('Phone number not found');
        }
        return phoneNumber;
    }
};
exports.PhoneNumberService = PhoneNumberService;
exports.PhoneNumberService = PhoneNumberService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('PhoneNumber')),
    __metadata("design:paramtypes", [mongoose_2.Model])
], PhoneNumberService);
//# sourceMappingURL=phone-number.service.js.map