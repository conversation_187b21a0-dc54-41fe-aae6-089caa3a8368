import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { UsersService } from 'src/users/users.service';
import { AutoRechargeService } from './auto-recharge.service';
import { OrganizationsService } from 'src/organizations/organizations.service';
interface RequestWithUser extends Request {
    user?: {
        userId: string;
        email: string;
        role?: string;
        fullName?: string;
    };
}
export declare class CreditCheckMiddleware implements NestMiddleware {
    private readonly usersService;
    private readonly autoRechargeService;
    private readonly organizationsService;
    private readonly logger;
    constructor(usersService: UsersService, autoRechargeService: AutoRechargeService, organizationsService: OrganizationsService);
    use(req: RequestWithUser, res: Response, next: NextFunction): Promise<void | Response<any, Record<string, any>>>;
}
export {};
