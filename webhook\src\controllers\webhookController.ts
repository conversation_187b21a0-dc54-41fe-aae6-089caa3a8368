import { Request, Response } from "express";
import axios from "axios";

export class WebhookController {
  private readonly logsApiUrl = "http://nestjs:4000/api/logs";
  private readonly forwardUrl = "http://nestjs:4000/api/vapi/webhook";

  async receiveWebhook(req: Request, res: Response): Promise<void> {
    try {
      const webhookData = req.body;
      console.log("Received webhook data:", webhookData);

      await axios.post(this.logsApiUrl, {
        level: "INFO",
        message: `Webhook received`,
      });

      await this.forwardWebhook(webhookData);
      res.status(200).send({ message: "Webhook received, logged, and forwarded" });
    } catch (error: any) {
      console.error("Error processing webhook:", error);

      await axios.post(this.logsApiUrl, {
        level: "ERROR",
        message: `Error processing webhook: ${error.message}`,
        trace: error.stack,
      });

      res.status(500).send({ message: "Error processing webhook" });
    }
  }

  private async forwardWebhook(data: any): Promise<void> {
    try {
      await axios.post(this.forwardUrl, data);
      await axios.post(this.logsApiUrl, {
        level: "INFO",
        message: `Webhook data forwarded successfully`,
      });
      console.log("Webhook data forwarded successfully");
    } catch (error: any) {
      console.error("Error forwarding webhook data:", error);

      await axios.post(this.logsApiUrl, {
        level: "ERROR",
        message: `Error forwarding webhook data: ${error.message}`,
        trace: error.stack,
      });
    }
  }
}
