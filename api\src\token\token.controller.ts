import { Controller, Get, UseGuards } from '@nestjs/common';
import { TokenService } from './token.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('Token')
@Controller('token')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class TokenController {
  constructor(private readonly tokenService: TokenService) {}

  @Get('credits')
  @ApiOperation({ summary: 'Get Twilio account credits' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns the current Twilio account balance and currency',
    schema: {
      type: 'object',
      properties: {
        balance: { type: 'string' },
        currency: { type: 'string' }
      }
    }
  })
  async getTwilioCredits() {
    return this.tokenService.getTwilioCredits();
  }
}