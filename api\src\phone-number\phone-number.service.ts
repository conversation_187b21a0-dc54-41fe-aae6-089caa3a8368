import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PhoneNumberDocument } from './interfaces/phone-number.interface';

@Injectable()
export class PhoneNumberService {
  private readonly VAPI_API_TOKEN = process.env.VAPI_API_TOKEN || '';
  
  constructor(
    @InjectModel('PhoneNumber') private readonly phoneNumberModel: Model<PhoneNumberDocument>,
  ) {}

  async findAll(): Promise<PhoneNumberDocument[]> {
    try {
      // Get phone numbers from VAPI API
      const response = await fetch('https://api.vapi.ai/phone-number', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const vapiPhoneNumbers = await response.json();
  
      // Get all existing phone numbers from our database
      const existingPhoneNumbers = await this.phoneNumberModel.find().exec();
      
      // Create a map of VAPI phone number IDs for quick lookup
      const vapiPhoneNumberIds = new Set(vapiPhoneNumbers.map(phoneNumber => phoneNumber.id));
      
      // Delete phone numbers from database that don't exist 
      for (const dbPhoneNumber of existingPhoneNumbers) {
        if (!vapiPhoneNumberIds.has(dbPhoneNumber.id)) {
          await this.phoneNumberModel.findByIdAndDelete(dbPhoneNumber._id).exec();
        }
      }
  
      // Sync VAPI phone numbers with our database
      for (const vapiPhoneNumber of vapiPhoneNumbers) {
        // Check if phone number already exists in our database
        const existingPhoneNumber = await this.phoneNumberModel.findOne({ id: vapiPhoneNumber.id }).exec();
        
        if (!existingPhoneNumber) {
          // If phone number doesn't exist, create new one
          const newPhoneNumber = new this.phoneNumberModel({
            ...vapiPhoneNumber,
          });
          await newPhoneNumber.save();
        } else {
          // If phone number exists, update it with latest VAPI data
          await this.phoneNumberModel.findOneAndUpdate(
            { id: vapiPhoneNumber.id },
            { 
              ...vapiPhoneNumber,
              status: vapiPhoneNumber.status || existingPhoneNumber.status || 'active'
            },
            { new: true }
          ).exec();
        }
      }
  
      // Return phone numbers from our database (now fully synced with VAPI)
      return this.phoneNumberModel.find().exec();
      
    } catch (error) {
      console.error('Error syncing phone numbers:', error.message);
      // If VAPI sync fails, return what we have in database
      return this.phoneNumberModel.find().exec();
    }
  }

  async findById(id: string): Promise<PhoneNumberDocument> {
    // Only look for the phone number in our database since we've already synced with VAPI
    let phoneNumber;
    
    try {
      // Try to find by MongoDB ObjectId
      phoneNumber = await this.phoneNumberModel.findById(id).exec();
    } catch (error) {
      // If error (likely invalid ObjectId format), try to find by 'id' field
      phoneNumber = await this.phoneNumberModel.findOne({ id: id }).exec();
    }
    
    if (!phoneNumber) {
      throw new NotFoundException('Phone number not found');
    }
    
    // Return the phone number from our database
    return phoneNumber;
  }
}