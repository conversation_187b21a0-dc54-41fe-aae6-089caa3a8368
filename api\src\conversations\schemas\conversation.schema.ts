import { Schema } from 'mongoose';

export const MessageSchema = new Schema({
  role: { type: String, enum: ['user', 'assistant'], required: true },
  content: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
});

export const ConversationSchema = new Schema({
  agentId: { type: Schema.Types.ObjectId, ref: 'Agent', required: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  type: { type: String, enum: ['chat', 'call'], required: true },
  status: { type: String, enum: ['active', 'ended'], default: 'active' },
  messages: { type: [MessageSchema], default: [] },
  startedAt: { type: Date, default: Date.now },
  endedAt: { type: Date },
});
