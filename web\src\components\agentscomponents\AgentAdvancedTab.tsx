/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { AgentTabProps } from "@/types/agent.types";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { useRef, useState } from "react";
import { Card } from "@/components/ui/card";
import 'react-image-crop/dist/ReactCrop.css';


export default function AgentAdvancedTab( { agent, setAgent, phoneNumbers }: AgentTabProps) {

  const [modelSettings, setModelSettings] = useState({
    model: agent?.model?.model || '',
    provider: agent?.model?.provider || '',
    maxTokens: agent?.model?.maxTokens || 100,
    temperature: agent?.model?.temperature || 0.2
  });

  const handleModelChange = (field: string, value: string | number) => {
    const updated = { ...modelSettings, [field]: value };
    
    // Set default model when provider changes
    if (field === 'provider') {
      switch (value) {
        case 'openai':
          updated.model = 'gpt-4o';
          break;
        case 'anthropic':
          updated.model = 'claude-3-7-sonnet-20250219';
          break;
        case 'google':
          updated.model = 'gemini-2.0-flash';
          break;
        case 'cerebras':
          updated.model = 'llama-3.3-70b';
          break;
        case 'deep-seek':
          updated.model = 'deepseek-chat';
          break;
        case 'xai':
          updated.model = 'grok-2';
          break;
        case 'mistral':
          updated.model = 'mistral-large-latest';
          break;
        case 'perplexity-ai':
        case 'anyscale':
        case 'inflection-ai':
          updated.model = '';
          break;
        default:
          updated.model = '';
      }
    }
    
    setModelSettings(updated);
    
    setAgent({
      ...agent,
      model: {
        ...agent.model,
        provider: updated.provider,
        model: updated.model,
        maxTokens: updated.maxTokens,
        temperature: updated.temperature
      }
    });
  };

  
  const handleBackgroundDenoisingChange = (value: boolean) => {
    setAgent({ ...agent, backgroundDenoisingEnabled: value });
  };
 
  const handleServerUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAgent({ ...agent, server: { url: e.target.value } });
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
        <h3 className="text-lg font-semibold">Advanced Settings</h3>
      </div>

 {/* Model Settings */}
  <div className="space-y-4 mt-6">
    <h3 className="text-lg font-semibold">Model Settings</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div className="space-y-2">
  <Label htmlFor="model-provider">Provider</Label>
  <Select 
    value={modelSettings?.provider || ''} 
    onValueChange={(value) => handleModelChange('provider', value)}
  >
    <SelectTrigger id="model-provider" className="w-full">
      <SelectValue placeholder="Select provider" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="openai">OpenAI</SelectItem>
      <SelectItem value="anthropic">Anthropic</SelectItem>
      <SelectItem value="google">Google</SelectItem>
      <SelectItem value="cerebras">Cerebras</SelectItem>
      <SelectItem value="deep-seek">Deepseek</SelectItem>
      <SelectItem value="xai">Xai</SelectItem>
      <SelectItem value="mistral">Mistral</SelectItem>
      <SelectItem value="perplexity-ai">Perplexity AI</SelectItem>
      <SelectItem value="anyscale">Anyscale</SelectItem>
      <SelectItem value="inflection-ai">Inflection AI</SelectItem>
    </SelectContent>
  </Select>
</div>

<div className="space-y-2">
  <Label htmlFor="model-name">Model</Label>
  <Select 
    value={modelSettings?.model || ''} 
    onValueChange={(value) => handleModelChange('model', value)}
    disabled={['perplexity-ai', 'anyscale', 'inflection-ai'].includes(modelSettings.provider) } 
  >
    <SelectTrigger id="model-name" className="w-full">
      <SelectValue placeholder="Select model" />
    </SelectTrigger>
    <SelectContent>
      {modelSettings.provider === 'openai' && (
        <>
        <SelectItem value="gpt-4o">GPT 4o</SelectItem>
        <SelectItem value="gpt-4.1">GPT 4.1</SelectItem>
        <SelectItem value="gpt-4.1-mini">GPT 4.1 Mini</SelectItem>
        <SelectItem value="gpt-4.1-nano">GPT 4.1 Nano</SelectItem>
        <SelectItem value="gpt-4.5-preview">GPT 4.5 Preview</SelectItem>
        <SelectItem value="gpt-4o-mini">GPT 4o Mini</SelectItem>
        <SelectItem value="chatgpt-4o-latest">ChatGPT 4o Latest</SelectItem>
        <SelectItem value="o3">O3</SelectItem>
        <SelectItem value="o3-mini">O3 Mini</SelectItem>
        <SelectItem value="o4-mini">O4 Mini</SelectItem>
        <SelectItem value="o1-preview">O1 Preview</SelectItem>
        <SelectItem value="o1-mini">O1 Mini</SelectItem>
        <SelectItem value="gpt-4o-realtime-preview-2024-10-01">GPT 4o Realtime Preview</SelectItem>
        <SelectItem value="gpt-4-turbo">GPT 4 Turbo</SelectItem>
        <SelectItem value="gpt-4">GPT 4</SelectItem>
        <SelectItem value="gpt-3.5-turbo">GPT 3.5 Turbo</SelectItem>
      </>
      )}
      {modelSettings.provider === 'anthropic' && (
        <>
        <SelectItem value="claude-3-7-sonnet-20250219">Claude 3.7 Sonnet</SelectItem>
        <SelectItem value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</SelectItem>
        <SelectItem value="claude-3-5-haiku-20241022">Claude 3.5 Haiku (Old)</SelectItem>
        <SelectItem value="claude-3-opus-20240229">Claude 3 Opus</SelectItem>
        <SelectItem value="claude-3-sonnet-20240229">Claude 3 Sonnet</SelectItem>
        <SelectItem value="claude-3-haiku-20240307">Claude 3 Haiku (Latest)</SelectItem>
      </>
      )}
      {modelSettings.provider === 'google' && (
        <>
           <SelectItem value="gemini-2.5-flash-preview-04-17">Gemini 2.5 Flash Preview</SelectItem>
            <SelectItem value="gemini-2.0-flash">Gemini 2.0 Flash</SelectItem>
            <SelectItem value="gemini-2.0-flash-thinking-exp">Gemini 2.0 Flash Thinking (Experimental)</SelectItem>
            <SelectItem value="gemini-2.0-pro-exp-02-05">Gemini 2.0 Pro (Experimental)</SelectItem>
            <SelectItem value="gemini-2.0-flash-lite">Gemini 2.0 Flash Lite</SelectItem>
            <SelectItem value="gemini-2.0-flash-lite-preview-02-05">Gemini 2.0 Flash Lite Preview</SelectItem>
            <SelectItem value="gemini-2.0-flash-exp">Gemini 2.0 Flash (Experimental)</SelectItem>
            <SelectItem value="gemini-2.0-flash-realtime-exp">Gemini 2.0 Flash Realtime (Experimental)</SelectItem>
            <SelectItem value="gemini-1.5-flash">Gemini 1.5 Flash</SelectItem>
            <SelectItem value="gemini-1.5-flash-002">Gemini 1.5 Flash 002</SelectItem>
            <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
            <SelectItem value="gemini-1.5-pro-002">Gemini 1.5 Pro 002</SelectItem>
            <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
        </>
      )}
      {modelSettings.provider === 'cerebras' && (
        <>
         <SelectItem value="llama-3.3-70b">Llama 3.3 70B</SelectItem>
         <SelectItem value="llama3.1-8b">Llama 3.1 8B</SelectItem>
        </>
      )}
      {modelSettings.provider === 'deep-seek' && (
        <>
          <SelectItem value="deepseek-chat">DeepSeek Chat (V3)</SelectItem>
          <SelectItem value="deepseek-reasoner">Deepseek-R1</SelectItem>
        </>
      )}
      {modelSettings.provider === 'xai' && (
          <>
          <SelectItem value="grok-2">Grok 2</SelectItem>
          <SelectItem value="grok-3">Grok 3</SelectItem>
          <SelectItem value="grok-beta">Grok Beta</SelectItem>
        </>
      )}
      {modelSettings.provider === 'mistral' && (
        <>
          <SelectItem value="mistral-large-latest">Mistral Large</SelectItem>
          <SelectItem value="pixtral-large-latest">Pixtral Large</SelectItem>
          <SelectItem value="mistral-small">Mistral Small</SelectItem>
        </>
      )}
      {['perplexity-ai', 'anyscale', 'inflection-ai'].includes(modelSettings.provider) && (
        <SelectItem value="none" disabled>No models available</SelectItem>
      )}
    </SelectContent>
  </Select>
</div>

    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="max-tokens">Max Tokens</Label>
        <Input
          id="max-tokens"
          type="number"
          min={1}
          max={500}
          value={modelSettings?.maxTokens || 100}
          onChange={(e) => handleModelChange('maxTokens', Math.min(500, parseInt(e.target.value) || 0))}
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <div className="flex justify-between">
          <Label htmlFor="temperature">Temperature</Label>
          <span className="text-sm text-muted-foreground mt-3">
            {modelSettings?.temperature.toFixed(1) || 0.2}
          </span>
        </div>
        <Slider
          id="temperature"
          value={[modelSettings?.temperature]}
          min={0}
          max={2}
          step={0.1}
          className="w-full"
          onValueChange={(value) => handleModelChange('temperature', value[0])}
        />
      </div>
    </div>
  </div>

     {/* Server url */}
       <div className="space-y-4">
        <h3 className="text-lg font-semibold">Webhook (Server Url)</h3>
        <Card className="p-4">
          <Input
            placeholder="https://your-service.com/webhook"
            value={agent?.server?.url || ""}
            onChange={handleServerUrlChange}
          />
        </Card>
      </div>

      {/* Background Denoising */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Background Denoising</h3>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="background-denoising">Enable background noise reduction</Label>
            <Switch
              id="background-denoising"
              checked={agent?.backgroundDenoisingEnabled || false}
              onCheckedChange={handleBackgroundDenoisingChange}
            />
          </div>
        </Card>
      </div>
    </div>
  );
}