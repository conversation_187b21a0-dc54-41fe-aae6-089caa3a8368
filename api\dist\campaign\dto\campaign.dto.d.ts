export declare class CampaignDto {
    createdBy?: string;
    name: string;
    contacts?: string[];
    concurrentCalls: number;
    dailyCost: number;
    startDate: Date;
    endDate: Date;
    successRate?: number;
    sentiment?: "positive" | "neutral" | "negative";
    status?: "active" | "paused" | "completed";
    batchIntervalMinutes?: number;
}
export declare class UpdateCampaignStatusDto {
    status: "active" | "paused" | "completed";
}
