"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Trash2 } from "lucide-react";
import { EmptyState } from "./EmptyState";

type FileItem = {
  id: string;
  title: string;
  filename: string;
  size: number;
  type: string;
  uploadedAt: Date;
};

type FilesViewProps = {
  files: FileItem[];
  isEmpty: boolean;
  onAddClick: () => void;
  onDelete: (id: string) => void;
};

export function FilesView({ files, isEmpty, onAddClick, onDelete }: FilesViewProps) {
  if (isEmpty) {
    return <EmptyState type="file" onAdd={onAddClick} />;
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {files.map((file) => (
        <Card key={file.id} className="overflow-hidden">
          <CardContent className="p-4">
            <div className="h-32 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mb-4">
              <FileText className="h-16 w-16 text-gray-400 dark:text-gray-500" />
            </div>
            <div className="space-y-2">
              <h3 className="font-medium truncate">{file.title}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{file.filename}</p>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-red-500 hover:text-red-700 dark:text-red-400 hover:dark:text-red-300"
                  onClick={() => onDelete(file.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}