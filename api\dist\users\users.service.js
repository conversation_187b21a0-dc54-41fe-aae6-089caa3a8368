"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = __importStar(require("bcryptjs"));
const global_settings_service_1 = require("../global-settings/global-settings.service");
const organizations_service_1 = require("../organizations/organizations.service");
let UsersService = class UsersService {
    constructor(userModel, globalSettingsService, organizationsService) {
        this.userModel = userModel;
        this.globalSettingsService = globalSettingsService;
        this.organizationsService = organizationsService;
    }
    async create(userDto) {
        const existingUser = await this.userModel.findOne({ email: userDto.email }).exec();
        if (existingUser) {
            throw new common_1.BadRequestException('Email already in use');
        }
        const hashedPassword = await bcrypt.hash(userDto.password, 12);
        const newUser = new this.userModel({
            ...userDto,
            password: hashedPassword,
        });
        return newUser.save();
    }
    async findAll() {
        return this.userModel.find().select('-password').exec();
    }
    async findOne(email) {
        return this.userModel.findOne({ email }).exec();
    }
    async findById(id) {
        const user = await this.userModel.findById(id).select('-password').exec();
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return user;
    }
    async findByName(fullName) {
        return this.userModel.findOne({ fullName }).select('-password').exec();
    }
    async updateUser(id, updateDto) {
        const user = await this.userModel.findById(id).exec();
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        if (updateDto.email && updateDto.email !== user.email) {
            const existingUser = await this.userModel.findOne({ email: updateDto.email }).exec();
            if (existingUser) {
                throw new common_1.BadRequestException('Email already in use');
            }
        }
        if (updateDto.password) {
            updateDto.password = await bcrypt.hash(updateDto.password, 12);
        }
        else {
            delete updateDto.password;
        }
        Object.assign(user, {
            ...updateDto,
            updatedAt: new Date()
        });
        return user.save();
    }
    async approveUser(id) {
        const user = await this.userModel.findById(id).exec();
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        user.isApproved = true;
        user.updatedAt = new Date();
        return user.save();
    }
    async deleteUser(id) {
        const result = await this.userModel.deleteOne({ _id: id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
    }
    async hasSufficientCredits(userId, threshold = 1) {
        const user = await this.userModel.findById(userId).exec();
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found`);
        }
        if (userId === 'system') {
            return true;
        }
        try {
            if (!user.organizationId) {
                console.error('User has no organization assigned');
                return false;
            }
            const organization = await this.organizationsService.findOne(user.organizationId.toString());
            const minimumCreditsThreshold = organization.minimumCreditsThreshold || 1.0;
            const effectiveThreshold = Math.max(threshold, minimumCreditsThreshold);
            console.log(`Credit check for user ${userId}: threshold=${threshold}, orgMinThreshold=${minimumCreditsThreshold}, effectiveThreshold=${effectiveThreshold}`);
            try {
                const availableCredits = await this.organizationsService.getAvailableCredits(user.organizationId.toString());
                console.log(`Available credits: free=${availableCredits.freeCreditsRemaining}, paid=${availableCredits.paidCredits}, total=${availableCredits.totalAvailable}`);
                const hasSufficient = availableCredits.totalAvailable >= effectiveThreshold;
                console.log(`Credit check result: ${hasSufficient} (${availableCredits.totalAvailable} >= ${effectiveThreshold})`);
                return hasSufficient;
            }
            catch (orgError) {
                console.error('Error checking organization credits:', orgError);
                return false;
            }
        }
        catch (error) {
            console.error('Error getting organization settings for credit check:', error);
            return false;
        }
    }
    async deductCredits(userId, amount) {
        const user = await this.userModel.findById(userId).exec();
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found`);
        }
        if (!user.organizationId) {
            throw new common_1.BadRequestException('User has no organization to deduct credits from');
        }
        try {
            await this.organizationsService.deductCredits(user.organizationId.toString(), amount);
            return user;
        }
        catch (orgError) {
            console.error('Error deducting organization credits:', orgError);
            throw new common_1.BadRequestException('Failed to deduct credits from organization');
        }
    }
    async getUserOrganization(userId) {
        const user = await this.userModel.findById(userId).exec();
        if (!user || !user.organizationId) {
            return null;
        }
        try {
            return await this.organizationsService.findOne(user.organizationId.toString());
        }
        catch (error) {
            console.error('Error getting user organization:', error);
            return null;
        }
    }
    getOrganizationsService() {
        return this.organizationsService;
    }
    getGlobalSettingsService() {
        return this.globalSettingsService;
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('User')),
    __param(2, (0, common_1.Inject)((0, common_1.forwardRef)(() => organizations_service_1.OrganizationsService))),
    __metadata("design:paramtypes", [mongoose_2.Model,
        global_settings_service_1.GlobalSettingsService,
        organizations_service_1.OrganizationsService])
], UsersService);
//# sourceMappingURL=users.service.js.map