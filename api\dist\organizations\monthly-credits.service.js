"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MonthlyCreditsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonthlyCreditsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const schedule_1 = require("@nestjs/schedule");
const global_settings_service_1 = require("../global-settings/global-settings.service");
let MonthlyCreditsService = MonthlyCreditsService_1 = class MonthlyCreditsService {
    constructor(organizationModel, globalSettingsService) {
        this.organizationModel = organizationModel;
        this.globalSettingsService = globalSettingsService;
        this.logger = new common_1.Logger(MonthlyCreditsService_1.name);
    }
    async checkAndResetMonthlyCredits(organizationId) {
        const organization = await this.organizationModel.findById(organizationId).exec();
        if (!organization) {
            return;
        }
        const resetDay = organization.monthlyResetDate || 1;
        const now = new Date();
        const lastReset = new Date(organization.lastMonthlyReset);
        if (this.shouldResetCredits(now, lastReset, resetDay)) {
            await this.resetMonthlyCredits(organization);
        }
    }
    shouldResetCredits(now, lastReset, resetDay) {
        const monthsDiff = (now.getFullYear() - lastReset.getFullYear()) * 12 + (now.getMonth() - lastReset.getMonth());
        if (monthsDiff > 1) {
            return true;
        }
        if (monthsDiff === 0) {
            return false;
        }
        if (monthsDiff === 1) {
            return now.getDate() >= resetDay;
        }
        return false;
    }
    async resetMonthlyCredits(organization) {
        const monthlyMinutesAllowance = organization.monthlyMinutesAllowance || 0;
        organization.monthlyFreeCredits = monthlyMinutesAllowance;
        organization.monthlyFreeCreditsUsed = 0;
        organization.lastMonthlyReset = new Date();
        organization.usingFreeCredits = monthlyMinutesAllowance > 0;
        await organization.save();
        this.logger.log(`Reset monthly credits for organization ${organization._id}: ${monthlyMinutesAllowance} minutes`);
    }
    async getAvailableCredits(organizationId) {
        await this.checkAndResetMonthlyCredits(organizationId);
        const organization = await this.organizationModel.findById(organizationId).exec();
        if (!organization) {
            return {
                freeCreditsRemaining: 0,
                paidCredits: 0,
                totalAvailable: 0,
                usingFreeCredits: false,
            };
        }
        const freeCreditsRemaining = Math.max(0, organization.monthlyFreeCredits - organization.monthlyFreeCreditsUsed);
        const paidCredits = organization.credits;
        const totalAvailable = freeCreditsRemaining + paidCredits;
        return {
            freeCreditsRemaining,
            paidCredits,
            totalAvailable,
            usingFreeCredits: organization.usingFreeCredits && freeCreditsRemaining > 0,
        };
    }
    async deductCredits(organizationId, amount) {
        await this.checkAndResetMonthlyCredits(organizationId);
        const organization = await this.organizationModel.findById(organizationId).exec();
        if (!organization) {
            throw new Error(`Organization with ID ${organizationId} not found`);
        }
        const freeCreditsRemaining = Math.max(0, organization.monthlyFreeCredits - organization.monthlyFreeCreditsUsed);
        const totalAvailable = freeCreditsRemaining + organization.credits;
        if (totalAvailable < amount) {
            return {
                success: false,
                freeCreditsDeducted: 0,
                paidCreditsDeducted: 0,
                remainingFreeCredits: freeCreditsRemaining,
                remainingPaidCredits: organization.credits,
            };
        }
        let freeCreditsDeducted = 0;
        let paidCreditsDeducted = 0;
        if (freeCreditsRemaining > 0 && amount > 0) {
            freeCreditsDeducted = Math.min(freeCreditsRemaining, amount);
            organization.monthlyFreeCreditsUsed += freeCreditsDeducted;
            amount -= freeCreditsDeducted;
        }
        if (amount > 0) {
            paidCreditsDeducted = amount;
            organization.credits -= paidCreditsDeducted;
            organization.usingFreeCredits = false;
        }
        const newFreeCreditsRemaining = Math.max(0, organization.monthlyFreeCredits - organization.monthlyFreeCreditsUsed);
        if (newFreeCreditsRemaining === 0 && organization.usingFreeCredits) {
            organization.usingFreeCredits = false;
            this.logger.log(`Organization ${organizationId} has exhausted free credits, switching to paid billing`);
        }
        await organization.save();
        return {
            success: true,
            freeCreditsDeducted,
            paidCreditsDeducted,
            remainingFreeCredits: newFreeCreditsRemaining,
            remainingPaidCredits: organization.credits,
        };
    }
    async checkAllOrganizationsForReset() {
        this.logger.log('Checking all organizations for monthly credits reset');
        try {
            const organizations = await this.organizationModel.find({ status: 'active' }).exec();
            const now = new Date();
            let resetCount = 0;
            for (const organization of organizations) {
                const resetDay = organization.monthlyResetDate || 1;
                const lastReset = new Date(organization.lastMonthlyReset);
                if (this.shouldResetCredits(now, lastReset, resetDay)) {
                    await this.resetMonthlyCredits(organization);
                    resetCount++;
                    this.logger.log(`Reset credits for organization ${organization.name} on day ${resetDay}`);
                }
            }
            if (resetCount > 0) {
                this.logger.log(`Monthly credits reset completed for ${resetCount} organizations`);
            }
        }
        catch (error) {
            this.logger.error('Error during monthly credits reset check:', error);
        }
    }
    async initializeMonthlyCredits(organizationId) {
        const organization = await this.organizationModel.findById(organizationId).exec();
        if (!organization) {
            return;
        }
        const monthlyMinutesAllowance = organization.monthlyMinutesAllowance || 0;
        organization.monthlyFreeCredits = monthlyMinutesAllowance;
        organization.monthlyFreeCreditsUsed = 0;
        organization.lastMonthlyReset = new Date();
        organization.usingFreeCredits = monthlyMinutesAllowance > 0;
        await organization.save();
        this.logger.log(`Initialized monthly credits for organization ${organizationId}: ${monthlyMinutesAllowance} minutes`);
    }
};
exports.MonthlyCreditsService = MonthlyCreditsService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MonthlyCreditsService.prototype, "checkAllOrganizationsForReset", null);
exports.MonthlyCreditsService = MonthlyCreditsService = MonthlyCreditsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Organization')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        global_settings_service_1.GlobalSettingsService])
], MonthlyCreditsService);
//# sourceMappingURL=monthly-credits.service.js.map