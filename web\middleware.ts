import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  // Get the access token from cookies
  const accessToken = request.cookies.get('access_token')?.value;

  // Define the paths that require authentication
  const isProtectedPath = request.nextUrl.pathname.startsWith('/dashboard') || 
                          request.nextUrl.pathname.startsWith('/agents') ||
                          request.nextUrl.pathname.startsWith('/settings');
  
  // Define authentication paths (login/register)
  const isAuthPath = request.nextUrl.pathname === '/login' || 
                     request.nextUrl.pathname === '/register';

  // If accessing a protected route without a token
  if (isProtectedPath && !accessToken) {
    const loginUrl = new URL('/login', request.url);
    return NextResponse.redirect(loginUrl);
  }

  // If accessing login/register with a token
  if (isAuthPath && accessToken) {
    const dashboardUrl = new URL('/dashboard', request.url);
    return NextResponse.redirect(dashboardUrl);
  }

  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};