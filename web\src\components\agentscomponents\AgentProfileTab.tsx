/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRef, useState } from "react";
import { Headset, MessageSquare, PhoneOutgoing, PlusIcon, Upload, Volume2 } from "lucide-react";
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "../ui/dialog";
import { Agent, AgentTabProps } from "@/types/agent.types";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";
import { AgentPhoneCallDialog } from "./AgentPhoneCallDialog";
import { AgentChatWidget } from "./AgentChatWidget";


const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

export default function AgentProfileTab({ agent, setAgent, phoneNumbers, isCreateMode }: AgentTabProps) {

  const [isUploading, setIsUploading] = useState(false);
  const [showCropper, setShowCropper] = useState(false);
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [crop, setCrop] = useState<Crop>({unit: '%',width: 100,height: 100,x: 0,y: 0,});
  
  const [isCallDialogOpen, setIsCallDialogOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);

  const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);

  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleAvatarClick = () => {
    // Reset the file input value before clicking it
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size - 2MB limit
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
      alert("File is too large. Maximum size is 2MB.");
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }
    
    // Store the file for later upload
    setSelectedFile(file);

    // Reset crop states to ensure fresh cropping
    setCompletedCrop(null);
    setCrop({
      unit: '%',
      width: 100,
      height: 100,
      x: 0,
      y: 0,
    });
    
    // Create a preview for cropping
    const reader = new FileReader();
    reader.onload = () => {
      setImageSrc(reader.result as string);
      setShowCropper(true);
    };
    reader.readAsDataURL(file);
  };

  const handleCropComplete = (crop: PixelCrop) => {
    setCompletedCrop(crop);
  };

  const getCroppedImg = (image: HTMLImageElement, crop: PixelCrop): Promise<Blob> => {
    const canvas = document.createElement('canvas');
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    canvas.width = crop.width;
    canvas.height = crop.height;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('No 2d context');
    }

    // Fill with white background to prevent transparency issues
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width,
      crop.height
    );

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          throw new Error('Canvas is empty');
        }
        resolve(blob);
      }, 'image/jpeg', 0.95);
    });
  };

  const generateUniqueFilename = (originalFile: File): File => {
    // Extract file extension and base name
    const parts = originalFile.name.split('.');
    const extension = parts.length > 1 ? parts.pop() : '';
    const baseName = parts.join('.');
    
    // Generate a 3-digit random number (1000-9999)
    const randomDigits = Math.floor(Math.random() * 9000 + 1000);
    
    // Create a new filename with the random digits
    const newFilename = `${baseName}_${randomDigits}.${extension}`;
    
    // Create a new File object with the unique name
    return new File([originalFile], newFilename, { type: originalFile.type });
  };

  const uploadCroppedImage = async () => {
    if (!imgRef.current || !selectedFile) return;
    
    try {
      setShowCropper(false);
      setIsUploading(true);

      // Use completedCrop if available, otherwise use current crop
    const cropToUse = completedCrop || {
      x: crop.x,
      y: crop.y,
      width: typeof crop.width === 'number' ? crop.width : 0,
      height: typeof crop.height === 'number' ? crop.height : 0,
      unit: 'px'
    };
    
      
      // Get the cropped image as a blob
      const croppedBlob = await getCroppedImg(imgRef.current, cropToUse);
      // Create a new file from the blob
      const croppedFile = generateUniqueFilename(
        new File([croppedBlob], selectedFile.name, { type: 'image/jpeg' })
      );
      
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token available");
        return;
      }
      
      const formData = new FormData();
      formData.append('avatar', croppedFile);
      
      const response = await fetch(`${API_BASE_URL}/api/agents/${agent.id}/avatar`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`Failed to upload avatar: ${response.status}`);
      }
      
      const result = await response.json();
      
      // Update the agent with the server-stored avatar path
      setAgent({ 
        ...agent, 
        avatar: `${API_BASE_URL}${result.avatarPath}` 
      });
      
    } catch (error) {
      console.error("Error uploading avatar:", error);
    } finally {
      setIsUploading(false);
      setSelectedFile(null);
      setImageSrc(null);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const cancelCrop = () => {
    setShowCropper(false);
    setSelectedFile(null);
    setImageSrc(null);
  if (fileInputRef.current) {
    fileInputRef.current.value = '';
  }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    
    // Calculate the smallest dimension to ensure a square crop
    const size = Math.min(width, height); // 80% of the smallest dimension
    
    // Center the crop
    const x = (width - size) / 2;
    const y = (height - size) / 2;
    
    // Set initial crop to be centered and square
    setCrop({
      unit: 'px',
      width: size,
      height: size,
      x,
      y,
    });
  };

  const enforceSquareCrop = (newCrop: Crop): Crop => {
    // If width and height are different, make them equal using the smaller dimension
    if (newCrop.width !== newCrop.height) {
      const size = Math.min(newCrop.width, newCrop.height);
      return {
        ...newCrop,
        width: size,
        height: size,
      };
    }
    // Otherwise just ensure aspect ratio is set
    return { ...newCrop };
  };

  
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAgent({ ...agent, name: e.target.value });
  };
  
  const handleRoleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAgent({ ...agent, role: e.target.value });
  };

 const handleLocalPhoneNumberChange = (phoneNumberId: string) => {
    setAgent({
      ...agent,
      localPhoneNumberId: phoneNumberId
    });
  };


 const handleInternationalPhoneNumberChange = (phoneNumberId: string) => {
    setAgent({
      ...agent,
      internationalPhoneNumberId: phoneNumberId
    });
  };
  
  

const availableLanguages = [
    { code: 'en-US', name: 'English (US)' },
    { code: 'en-GB', name: 'English (UK)' },
    { code: 'en', name: 'English' },
    { code: 'fr-FR', name: 'French' },
    { code: 'fr', name: 'French' },
    { code: 'es-ES', name: 'Spanish' },
    { code: 'de-DE', name: 'German' },
    { code: 'it-IT', name: 'Italian' },
    { code: 'ar-SA', name: 'Arabic' },
  ];

  // const handleLanguageChange = (language: string) => {
  //     setAgent({
  //       ...agent,
  //       transcriber: {
  //         ...agent.transcriber,
  //         language
  //       }
  //     });
  //   }
  // };

  return (
    <>
    <div className="space-y-8 p-4 sm:p-6">
      {/* Basic Information */}
      <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
      {/* Left Side - Avatar */}
      <div className="w-full lg:w-1/3 xl:w-1/4 flex flex-col items-center">
        <div 
          className="relative cursor-pointer group w-full max-w-[200px]"
          onClick={handleAvatarClick}
        >
          <div className="aspect-square rounded-lg bg-black p-1 overflow-hidden">
            {agent?.avatar ? (
              <img 
                src={agent.avatar}
                alt={`${agent.name} avatar`}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="h-full w-full bg-gray-200 flex items-center justify-center text-5xl">
                {agent?.name ? agent.name.charAt(0).toUpperCase() : 'A'}
              </div>
            )}
            {isUploading && (
              <div className="absolute inset-0 bg-black bg-opacity-70 rounded-lg flex items-center justify-center">
                <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-white rounded-full"></div>
              </div>
            )}
          </div>
          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <Upload className="h-8 w-8 text-white" />
          </div>
          <input 
            type="file" 
            ref={fileInputRef}
            className="hidden" 
            accept="image/jpeg,image/png,image/gif,image/webp"
            onChange={handleFileChange}
          />
        </div>

      {/* Action Icons - Below avatar */}
      {!isCreateMode && (
        <div className="flex items-center justify-center gap-2 mt-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-emerald-50 dark:hover:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 transition-all duration-200 hover:scale-115"
                  >
                    <Volume2 className="h-4 w-4 text-emerald-500 dark:text-emerald-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-2 border-gray-200 dark:border-gray-700">
                  <p>Test voice</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-blue-200 dark:border-gray-700 transition-all duration-200 hover:scale-115"
                    onClick={() => setIsChatOpen(true)}
                  >
                    <MessageSquare className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-2 border-gray-200 dark:border-gray-700">
                  <p>Chat with agent</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-indigo-50 dark:hover:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 transition-all duration-200 hover:scale-115"
                  >
                    <Headset className="h-4 w-4 text-indigo-500 dark:text-indigo-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-2 border-gray-200 dark:border-gray-700">
                  <p>Start web call</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-amber-50 dark:hover:bg-amber-900/20 border border-purple-200 dark:border-gray-700 transition-all duration-200 hover:scale-115"
                    onClick={() => setIsCallDialogOpen(true)}
                  >
                    <PhoneOutgoing className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-2 border-gray-200 dark:border-gray-700">
                  <p>Start phone call</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
      )}
      </div>

      {/* Right Side - Info */}
      <div className="w-full lg:w-2/3 xl:w-3/4 space-y-6">
        {/* Name and Role */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input 
              id="name" 
              value={agent?.name || ''} 
              onChange={handleNameChange}
              placeholder="Enter agent name"
              className="w-full"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Input 
              id="role" 
              value={agent?.role || ''} 
              onChange={handleRoleChange}
              placeholder="Enter agent role"
              className="w-full"
            />
          </div>
        </div>
        
         {/* Phone Numbers - Single row on larger screens */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Phone Numbers</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="localPhoneNumber">Local Number</Label>
                <Select
                  value={agent?.localPhoneNumberId || ""}
                  onValueChange={handleLocalPhoneNumberChange}
                >
                  <SelectTrigger id="localPhoneNumber" className="w-full">
                    <SelectValue placeholder="Select a phone number" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {phoneNumbers?.map((phoneNumber) => (
                      <SelectItem key={phoneNumber.id} value={phoneNumber.id}>
                        {phoneNumber.number} {phoneNumber.name ? `(${phoneNumber.name})` : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="internationalPhoneNumber">International Number</Label>
                <Select
                  value={agent?.internationalPhoneNumberId || ""}
                  onValueChange={handleInternationalPhoneNumberChange}
                >
                  <SelectTrigger id="internationalPhoneNumber" className="w-full">
                    <SelectValue placeholder="Select a phone number" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {phoneNumbers?.map((phoneNumber) => (
                      <SelectItem key={phoneNumber.id} value={phoneNumber.id}>
                        {phoneNumber.number} {phoneNumber.name ? `(${phoneNumber.name})` : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

   {/* Language - Changed from badge to select */}
          <div className="space-y-2">
            <Label htmlFor="language">Language</Label>
            <Select
              value={agent?.transcriber?.language || ""}

            >
              <SelectTrigger id="language" className="w-full sm:w-auto">
                <SelectValue placeholder="Select a language" />
              </SelectTrigger>
              <SelectContent>
                {availableLanguages.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

        
      </div>
    </div>
     
     {/* Add Dialogs at the bottom of component - Only in edit mode */}
      {!isCreateMode && (
        <>
          <AgentPhoneCallDialog 
            isOpen={isCallDialogOpen}
            onClose={() => setIsCallDialogOpen(false)}
            agent={agent}
          />

          <AgentChatWidget 
            agent={agent}
            isOpen={isChatOpen}
            onClose={() => setIsChatOpen(false)}
          />
        </>
      )}

    {/* Image Cropper Dialog */}
         <Dialog open={showCropper} onOpenChange={setShowCropper}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Crop Avatar Image</DialogTitle>
              </DialogHeader>
              {imageSrc && (
                <div className="mt-4 flex flex-col items-center">
                  <ReactCrop
                    crop={crop}
                    onChange={(newCrop) => setCrop(enforceSquareCrop(newCrop))}
                    onComplete={handleCropComplete}
                    circularCrop={true}
                    minWidth={50} // Minimum crop size
                    minHeight={50}
                    keepSelection
                    locked={true}
                  >
                    <img 
                      ref={imgRef}
                      src={imageSrc} 
                      alt="Crop preview" 
                      style={{ maxHeight: '400px' }}
                      onLoad={onImageLoad}
                    />
                  </ReactCrop>
                  <p className="text-sm text-muted-foreground mt-2">
                    Drag to adjust the crop area.
                  </p>
                </div>
              )}
              <DialogFooter className="flex justify-between mt-4">
                <Button variant="outline" onClick={cancelCrop}>
                  Cancel
                </Button>
                <Button onClick={uploadCroppedImage}>
                  Apply & Upload
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

    </div>
    </>
  );
}