"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadContactDto = exports.ContactDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class ContactDto {
}
exports.ContactDto = ContactDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: "John Doe", description: "Name of the contact" }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ContactDto.prototype, "contactName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "+1234567890", description: "Phone number" }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ContactDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ContactDto.prototype, "customerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "2024-03-26T12:00:00Z",
        required: false,
        description: "Last call date",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ContactDto.prototype, "lastCall", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ["client", "sales"],
        required: false,
        description: "Contact labels",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ContactDto.prototype, "campaigns", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Asia/Dubai", description: "Region in timezone format" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Project Alpha", required: false, description: "Project Name" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "projectName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "A-102", required: false, description: "Unit Number" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "unitNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 50000, required: false, description: "Total Payable Amount" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactDto.prototype, "totalPayableAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5000, required: false, description: "Pending Payable Amount for the current installment" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactDto.prototype, "pendingPayableAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "2024-04-01T00:00:00Z", required: false, description: "Due date of the current installment" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ContactDto.prototype, "dueDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 12, required: false, description: "Total Number of Installments" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactDto.prototype, "totalInstallments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Credit Card", required: false, description: "Preferred Payment Type" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "paymentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5, required: false, description: "Number of Pending Installments" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactDto.prototype, "pendingInstallments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "2024-03-15T00:00:00Z", required: false, description: "Last Payment Date" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ContactDto.prototype, "lastPaymentDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 2000, required: false, description: "Last Payment Amount" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactDto.prototype, "lastPaymentAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Bank Transfer", required: false, description: "Last Payment Type" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "lastPaymentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Bucket A", required: false, description: "Collection Bucket" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "collectionBucket", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1500, required: false, description: "Unit Price" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactDto.prototype, "unitPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 2000, required: false, description: "Paid Amount Including" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ContactDto.prototype, "paidAmtIncluding", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "2025-04-28T14:30:00Z", required: false, description: "Event date and time" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)(),
    __metadata("design:type", String)
], ContactDto.prototype, "eventDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Dubai Marina", required: false, description: "Event location" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "eventLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "2:30 PM", required: false, description: "Event time" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "eventTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "John Smith", required: false, description: "Name of the registrant" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "nameOfRegistrant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "john Doe", required: false, description: "Name of the user that added the contact" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "addedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "2024-03-26T12:00:00Z", required: false, description: "Date when the contact was created" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ContactDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'manual', required: false, description: "Source of the contact" }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContactDto.prototype, "source", void 0);
class UploadContactDto {
}
exports.UploadContactDto = UploadContactDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UploadContactDto.prototype, "contactName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UploadContactDto.prototype, "customerId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UploadContactDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UploadContactDto.prototype, "campaignNames", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UploadContactDto.prototype, "region", void 0);
//# sourceMappingURL=contact.dto.js.map