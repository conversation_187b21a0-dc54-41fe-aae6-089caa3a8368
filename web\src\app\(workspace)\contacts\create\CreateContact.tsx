/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {Card,CardContent,CardDescription,CardHeader,CardTitle} from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import {Dialog,DialogContent,DialogDescription,DialogFooter,DialogHeader,DialogTitle } from "@/components/ui/dialog";

import { Check } from "lucide-react";
import {Campaign,Contact,createContact,NewContact} from "@/app/api/contacts";
import {Select,SelectContent,SelectItem,SelectTrigger,SelectValue} from "@/components/ui/select";
import { getRegionFromPhoneNumber } from "@/lib/phone-utils";
import { TimezoneSelector } from "@/components/ui/timezone-selector";



export default function CreateContact() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [campaignsLoading, setCampaignsLoading] = useState(true);
  const [error, setError] = useState("");
  const [campaigns, setCampaigns] = useState<Campaign[]>();
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [contact, setContact] = useState({
    contactName: "",
    phoneNumber: "",
    region: "",
    projectName: "",
    unitNumber: "",
    totalPayableAmount: "",
    pendingPayableAmount: "",
    dueDate: "",
    totalInstallments: "",
    paymentType: "",
    pendingInstallments: "",
    lastPaymentDate: "",
    lastPaymentAmount: "",
    lastPaymentType: "",
    collectionBucket: "",
    unitPrice: "",
    paidAmtIncluding: "",
    // Event related fields
    eventDate: "",
    eventLocation: "",
    eventTime: "",
    nameOfRegistrant: "",
    campaigns: [] as string[],
  });

  const fetchCampaigns = async () => {
    setCampaignsLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/campaigns`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );
      if (response.ok) {
        const data = await response.json();
        setCampaigns(data);
      }
    } catch (error) {
      console.error("Error fetching campaigns:", error);
    } finally {
      setCampaignsLoading(false);
    }
  };

 

  //  fetch campaigns and contacts list on component mount
  useEffect(() => {
    fetchCampaigns();
  }, []);

  useEffect(() => {
    if (contact.phoneNumber) {
      const detectedRegion = getRegionFromPhoneNumber(contact.phoneNumber);
      if (detectedRegion) {
        setContact((prev) => ({ ...prev, region: detectedRegion }));
      }
    }
  }, [contact.phoneNumber]);

  // Helper function to determine if the selected campaign is Collections, Sales, or AquaRiseEvent
  const getSelectedCampaignType = (): 'Sales' | 'Collections' | 'AquaRiseEvent' | null => {
    if (!contact.campaigns || contact.campaigns.length === 0 || !campaigns) {
      return null;
    }

    const selectedCampaign = campaigns.find(c => c._id === contact.campaigns[0]);
    if (!selectedCampaign) return null;

    // Case-insensitive comparison for more robustness
    const campaignName = selectedCampaign.name.toLowerCase();
    if (campaignName.includes('collections')) return 'Collections';
    if (campaignName.includes('sales')) return 'Sales';
    if (campaignName.includes('aquarise') || campaignName.includes('aqua rise')) return 'AquaRiseEvent';

    return null;
  };

  const handleCreate = async () => {
    // Basic validation
    if (!contact.contactName || !contact.phoneNumber || !contact.region || !contact.campaigns || contact.campaigns.length === 0) {
      setError("Contact Name, Phone Number, Region, and Campaign are required");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Convert string numeric values to actual numbers before submitting
      const contactToSubmit = {
        ...contact,
        totalPayableAmount:
          typeof contact.totalPayableAmount === "string"
            ? parseFloat(contact.totalPayableAmount) || 0
            : contact.totalPayableAmount,
        pendingPayableAmount:
          typeof contact.pendingPayableAmount === "string"
            ? parseFloat(contact.pendingPayableAmount) || 0
            : contact.pendingPayableAmount,
        lastPaymentAmount:
          typeof contact.lastPaymentAmount === "string"
            ? parseFloat(contact.lastPaymentAmount) || 0
            : contact.lastPaymentAmount,
        unitPrice:
          typeof contact.unitPrice === "string"
            ? parseFloat(contact.unitPrice) || 0
            : contact.unitPrice,
        paidAmtIncluding:
          typeof contact.paidAmtIncluding === "string"
            ? parseFloat(contact.paidAmtIncluding) || 0
            : contact.paidAmtIncluding,
        totalInstallments:
          typeof contact.totalInstallments === "string"
            ? parseInt(contact.totalInstallments) || 0
            : contact.totalInstallments,
        pendingInstallments:
          typeof contact.pendingInstallments === "string"
            ? parseInt(contact.pendingInstallments) || 0
            : contact.pendingInstallments,
        // Format eventDate as ISO string if it exists
        eventDate: contact.eventDate ? new Date(contact.eventDate).toISOString() : undefined,
      };

      await createContact(contactToSubmit as NewContact);
   
      setShowSuccessDialog(true);
    } catch (err) {
      setError("a contact with this name and phone number already exists, please try again with a different name or phone number");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (campaignsLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
        <div className="w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
          <p className="text-lg font-medium">Loading...</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-2">
      <div className="mb-6 w-xs">
        <Link
          href="/contacts"
          className="text-sm text-muted-foreground hover:text-primary flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Contacts
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Create New Contact</CardTitle>
          <CardDescription>
            Add a new contact with all required information.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Campaign Selection - Moved to the top */}
            <div className="space-y-4">
              <div>
                <Label className="mb-2">Campaign*</Label>
                <Select
                  value={
                    contact.campaigns && contact.campaigns.length > 0
                      ? campaigns?.find((c) => c._id === contact.campaigns[0])
                          ?.name || ""
                      : ""
                  }
                  onValueChange={(value) => {
                    const selectedCampaign = campaigns?.find(
                      (c) => c.name === value
                    );
                    if (selectedCampaign) {
                      setContact((prev) => ({
                        ...prev,
                        campaigns: [selectedCampaign._id],
                      }));
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a campaign" />
                  </SelectTrigger>
                  <SelectContent>
                    {campaigns?.map((campaign) => (
                      <SelectItem key={campaign._id} value={campaign.name}>
                        {campaign.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Basic Information - Always displayed */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="contactName" className="mb-2">
                    Contact Name*
                  </Label>
                  <Input
                    id="contactName"
                    value={contact.contactName}
                    onChange={(e) =>
                      setContact((prev) => ({
                        ...prev,
                        contactName: e.target.value,
                      }))
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="phoneNumber" className="mb-2">
                    Phone Number*
                  </Label>
                  <Input
                    id="phoneNumber"
                    value={contact.phoneNumber}
                    onChange={(e) =>
                      setContact((prev) => ({
                        ...prev,
                        phoneNumber: e.target.value,
                      }))
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="region" className="mb-2">
                    Region*
                  </Label>
                  <TimezoneSelector
                    value={contact.region}
                    onChange={(zone) =>
                      setContact((prev) => ({ ...prev, region: zone }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* AquaRiseEvent-specific fields - Only displayed for AquaRiseEvent campaign */}
            {getSelectedCampaignType() === 'AquaRiseEvent' && (
              <>
                {/* Event Information */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="eventDate" className="mb-2">
                        Event Date
                      </Label>
                      <Input
                        id="eventDate"
                        type="datetime-local"
                        value={contact.eventDate}
                        onChange={(e) =>
                          setContact((prev) => ({
                            ...prev,
                            eventDate: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="eventLocation" className="mb-2">
                        Event Location
                      </Label>
                      <Input
                        id="eventLocation"
                        value={contact.eventLocation}
                        onChange={(e) =>
                          setContact((prev) => ({
                            ...prev,
                            eventLocation: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="eventTime" className="mb-2">
                        Event Time
                      </Label>
                      <Input
                        id="eventTime"
                        value={contact.eventTime}
                        onChange={(e) =>
                          setContact((prev) => ({
                            ...prev,
                            eventTime: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="nameOfRegistrant" className="mb-2">
                        Name of Registrant
                      </Label>
                      <Input
                        id="nameOfRegistrant"
                        value={contact.nameOfRegistrant}
                        onChange={(e) =>
                          setContact((prev) => ({
                            ...prev,
                            nameOfRegistrant: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Collections-specific fields - Only displayed for Collections campaign */}
            {getSelectedCampaignType() === 'Collections' && (
              <>
                {/* Project Information */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="projectName" className="mb-2">
                        Project Name
                      </Label>
                      <Input
                        id="projectName"
                        value={contact.projectName}
                        onChange={(e) =>
                          setContact((prev) => ({
                            ...prev,
                            projectName: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="unitNumber" className="mb-2">
                        Unit Number
                      </Label>
                      <Input
                        id="unitNumber"
                        value={contact.unitNumber}
                        onChange={(e) =>
                          setContact((prev) => ({
                            ...prev,
                            unitNumber: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Payment Information */}
                <div className="grid grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="totalPayableAmount" className="mb-2">
                      Total Payable Amount
                    </Label>
                    <Input
                      id="totalPayableAmount"
                      type="text"
                      inputMode="decimal"
                      value={
                        typeof contact.totalPayableAmount === "number"
                          ? String(contact.totalPayableAmount)
                          : contact.totalPayableAmount || ""
                      }
                      onChange={(e) => {
                        // Store as string in state
                        const value = e.target.value;
                        // Allow only valid decimal input
                        if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                          setContact((prev) => ({
                            ...prev,
                            totalPayableAmount: value,
                          }));
                        }
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pendingPayableAmount" className="mb-2">
                      Pending Payable Amount
                    </Label>
                    <Input
                      id="pendingPayableAmount"
                      type="text"
                      inputMode="decimal"
                      value={contact.pendingPayableAmount || ""}
                      onChange={(e) => {
                        // Store as string in state
                        const value = e.target.value;
                        // Allow only valid decimal input
                        if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                          setContact((prev) => ({
                            ...prev,
                            pendingPayableAmount: value,
                          }));
                        }
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="dueDate" className="mb-2">
                      Due Date
                    </Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={contact.dueDate}
                      onChange={(e) =>
                        setContact((prev) => ({
                          ...prev,
                          dueDate: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>

                {/* Installment Information */}
                <div className="grid grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="totalInstallments" className="mb-2">
                      Total Installments Nb.
                    </Label>
                    <Input
                      id="totalInstallments"
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      value={contact.totalInstallments || ""}
                      onChange={(e) => {
                        const value = e.target.value.replace(/[^0-9]/g, "");
                        setContact((prev) => ({
                          ...prev,
                          totalInstallments: value,
                        }));
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pendingInstallments" className="mb-2">
                      Pending Installments Nb.
                    </Label>
                    <Input
                      id="pendingInstallments"
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      value={contact.pendingInstallments || ""}
                      onChange={(e) => {
                        const value = e.target.value.replace(/[^0-9]/g, "");
                        setContact((prev) => ({
                          ...prev,
                          pendingInstallments: value,
                        }));
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="paymentType" className="mb-2">
                      Payment Type
                    </Label>
                    <Input
                      id="paymentType"
                      value={contact.paymentType}
                      onChange={(e) =>
                        setContact((prev) => ({
                          ...prev,
                          paymentType: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>

                {/* Last Payment Information */}
                <div className="grid grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="lastPaymentDate" className="mb-2">
                      Last Payment Date
                    </Label>
                    <Input
                      id="lastPaymentDate"
                      type="date"
                      value={contact.lastPaymentDate}
                      onChange={(e) =>
                        setContact((prev) => ({
                          ...prev,
                          lastPaymentDate: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastPaymentAmount" className="mb-2">
                      Last Payment Amount
                    </Label>
                    <Input
                      id="lastPaymentAmount"
                      type="text"
                      inputMode="decimal"
                      value={contact.lastPaymentAmount || ""}
                      onChange={(e) => {
                        // Store as string in state
                        const value = e.target.value;
                        // Allow only valid decimal input
                        if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                          setContact((prev) => ({
                            ...prev,
                            lastPaymentAmount: value,
                          }));
                        }
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastPaymentType" className="mb-2">
                      Last Payment Type
                    </Label>
                    <Input
                      id="lastPaymentType"
                      value={contact.lastPaymentType}
                      onChange={(e) =>
                        setContact((prev) => ({
                          ...prev,
                          lastPaymentType: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>

                {/* Collection Bucket */}
                <div>
                  <Label htmlFor="collectionBucket" className="mb-2">
                    Collection Bucket
                  </Label>
                  <Input
                    id="collectionBucket"
                    value={contact.collectionBucket}
                    onChange={(e) =>
                      setContact((prev) => ({
                        ...prev,
                        collectionBucket: e.target.value,
                      }))
                    }
                  />
                </div>

                {/* Unit Price and Paid Amount Including */}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="unitPrice" className="mb-2">
                      Unit Price
                    </Label>
                    <Input
                      id="unitPrice"
                      type="text"
                      inputMode="decimal"
                      value={contact.unitPrice || ""}
                      onChange={(e) => {
                        // Store as string in state
                        const value = e.target.value;
                        // Allow only valid decimal input
                        if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                          setContact((prev) => ({
                            ...prev,
                            unitPrice: value,
                          }));
                        }
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="paidAmtIncluding" className="mb-2">
                      Paid Amount Including
                    </Label>
                    <Input
                      id="paidAmtIncluding"
                      type="text"
                      inputMode="decimal"
                      value={contact.paidAmtIncluding || ""}
                      onChange={(e) => {
                        // Store as string in state
                        const value = e.target.value;
                        // Allow only valid decimal input
                        if (value === "" || /^[0-9]*\.?[0-9]*$/.test(value)) {
                          setContact((prev) => ({
                            ...prev,
                            paidAmtIncluding: value,
                          }));
                        }
                      }}
                    />
                  </div>
                </div>
              </>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm">
                {error}
              </div>
            )}

            <div className="flex justify-end gap-4">
              <Button
                className="cursor-pointer"
                type="button"
                variant="outline"
                onClick={() => router.push("/contacts")}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreate}
                disabled={loading}
                className="cursor-pointer"
              >
                {loading ? "Creating..." : "Create Contact"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Check className="h-5 w-5 text-green-500" />
              Contact Created Successfully
            </DialogTitle>
            <DialogDescription>
              New contact has been added to your contacts list.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowSuccessDialog(false)}
            >
              Close
            </Button>
            <Button
              onClick={() => router.push("/contacts")}
              className="gap-2 cursor-pointer"
            >
              <ArrowLeft className="h-4 w-4" />
              View Contacts
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
