import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConversationsService } from './conversations.service';
import { ConversationsController } from './conversations.controller';
import { ConversationSchema } from './schemas/conversation.schema';
import { AiModule } from '../ai/ai.module';

@Module({
  imports: [
    forwardRef(() => AiModule),  
    MongooseModule.forFeature([{ name: 'Conversation', schema: ConversationSchema }]),
  ],
  providers: [ConversationsService],
  controllers: [ConversationsController],
  exports: [ConversationsService],
})
export class ConversationsModule {}
