"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
let HistoryService = class HistoryService {
    constructor(historyModel) {
        this.historyModel = historyModel;
    }
    async create(createHistoryDto) {
        const createdHistory = new this.historyModel(createHistoryDto);
        return createdHistory.save();
    }
    async findAll(page, limit, search, filterType = 'all') {
        const totalCount = await this.historyModel.countDocuments().exec();
        const filter = {};
        if (search && search.trim().length >= 1) {
            const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const searchRegex = new RegExp(escapedSearch, 'i');
            if (filterType === 'all') {
                filter.$or = [
                    { fullName: { $regex: searchRegex } },
                    { agent: { $regex: searchRegex } }
                ];
            }
            else if (filterType === 'name') {
                filter.fullName = { $regex: searchRegex };
            }
            else if (filterType === 'agent') {
                filter.agent = { $regex: searchRegex };
            }
        }
        let filteredCount = totalCount;
        if (Object.keys(filter).length > 0) {
            filteredCount = await this.historyModel.countDocuments(filter).exec();
        }
        let query = this.historyModel.find(filter).sort({ callStartTime: -1 });
        if (page !== undefined && limit !== undefined) {
            const skip = (page - 1) * limit;
            query = query.skip(skip).limit(limit);
        }
        const calls = await query.exec();
        calls.forEach(call => {
            call['_totalCount'] = totalCount;
            call['_filteredCount'] = filteredCount;
        });
        return calls;
    }
    async findById(id) {
        const history = await this.historyModel.findById(id).exec();
        if (!history) {
            throw new common_1.NotFoundException('History record not found');
        }
        return history;
    }
    async delete(id) {
        const deletedHistory = await this.historyModel.findByIdAndDelete(id).exec();
        if (!deletedHistory) {
            throw new common_1.NotFoundException('History record not found');
        }
        return deletedHistory;
    }
};
exports.HistoryService = HistoryService;
exports.HistoryService = HistoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('History')),
    __metadata("design:paramtypes", [Object])
], HistoryService);
//# sourceMappingURL=history.service.js.map