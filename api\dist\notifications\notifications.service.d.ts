import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { EmailService } from '../email/email.service';
import { Organization, OrganizationDocument } from '../organizations/interfaces/organization.interface';
export declare class NotificationsService {
    private emailService;
    private configService;
    private organizationModel;
    constructor(emailService: EmailService, configService: ConfigService, organizationModel: Model<OrganizationDocument>);
    shouldSendCreditRunoutNotification(organization: Organization): boolean;
    shouldSendCreditWarningNotification(organization: Organization): boolean;
    sendCreditRunoutNotification(organization: Organization): Promise<boolean>;
    sendCreditWarningNotification(organization: Organization): Promise<boolean>;
    processCreditNotifications(organization: Organization): Promise<void>;
    getCreditStatus(organization: Organization): {
        status: 'normal' | 'warning' | 'depleted';
        shouldNotify: boolean;
        message: string;
    };
}
