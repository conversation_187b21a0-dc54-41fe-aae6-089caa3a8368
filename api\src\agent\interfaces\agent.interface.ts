import { Document } from 'mongoose';

export interface Action {
  name: string;
  type: 'sendSMS' | 'sendEmail' | 'transferCall' | 'endCall' | 'extractInfo' | 'calendar';
  callType: 'inbound' | 'outbound' | 'both';
  trigger?: string;
  phoneNumber?: string;
  smsContent?: string;
}

export interface Voice {
  model: string;
  style: number;
  voiceId: string;
  provider: string;
  stability: number;
  similarityBoost: number;
  useSpeakerBoost: boolean;
  inputMinCharacters: number;
  inputPunctuationBoundaries: string[];
}

export interface Model {
  model: string;
  messages: { role: string; content: string }[];
  provider: string;
  maxTokens?: number;
  temperature: number;
  knowledgeBaseId?: string;
  emotionRecognitionEnabled: boolean;
}

export interface Transcriber {
  model: string;
  language: string;
  numerals: boolean;
  provider: string;
}

export interface AnalysisPlan {
  summaryPrompt: string;
  structuredDataPrompt: string;
  structuredDataSchema: any;
  structuredDataRequestTimeoutSeconds: number;
  successEvaluationPrompt: string;
  successEvaluationRubric: string;
}

export interface VoicemailDetection {
  provider: string;
}

export interface MessagePlan {
  idleMessages: string[];
  idleMessageMaxSpokenCount: number;
  idleTimeoutSeconds: number;
}

export interface StartSpeakingPlan {
  waitSeconds: number;
}

export interface StopSpeakingPlan {
  numWords: number;
}

export interface CompliancePlan {
  hipaaEnabled: boolean;
  pciEnabled: boolean;
}

export interface Agent {
  id: string;
  orgId: string;
  name: string;
  role: string;
  avatar: string;
  status: string;
  voice: Voice;
  createdAt: Date;
  updatedAt: Date;
  model: Model;
  recordingEnabled: boolean;
  firstMessage: string;
  voicemailMessage: string;
  endCallFunctionEnabled: boolean;
  endCallMessage: string;
  transcriber: Transcriber;
  clientMessages: string[];
  serverMessages: string[];
  server: {
    url: string;
  };
  endCallPhrases: string[];
  hipaaEnabled: boolean;
  maxDurationSeconds: number;
  backgroundSound: string;
  backchannelingEnabled: boolean;
  analysisPlan: AnalysisPlan;
  voicemailDetection: VoicemailDetection;
  backgroundDenoisingEnabled: boolean;
  messagePlan: MessagePlan;
  startSpeakingPlan: StartSpeakingPlan;
  stopSpeakingPlan: StopSpeakingPlan;
  compliancePlan: CompliancePlan;
  isServerUrlSecretSet: boolean;
  actions?: Action[];
}

export type AgentDocument = Agent & Document;
