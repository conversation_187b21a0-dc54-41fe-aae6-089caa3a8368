import { Injectable, Logger } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { BillingService } from '../billing/billing.service';
import { OrganizationsService } from '../organizations/organizations.service';

@Injectable()
export class AutoRechargeService {
  private readonly logger = new Logger(AutoRechargeService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly billingService: BillingService,
    private readonly organizationsService: OrganizationsService,
  ) {}

  /**
   * Check if a user needs auto-recharge and process it if needed
   */
  async checkAndProcessAutoRecharge(userId: string): Promise<boolean> {
    try {
      this.logger.log(`Auto-recharge check initiated for user ${userId}`);

      // Get the user
      const user = await this.usersService.findById(userId);

      // Check if user belongs to an organization
      if (!user.organizationId) {
        this.logger.warn(`Auto-recharge skipped for user ${userId}: No organization found`);
        return false;
      }

      // Get the organization
      const organization = await this.organizationsService.findOne(user.organizationId.toString());

      // Check if auto-recharge is enabled for this organization
      if (!organization.autoRechargeEnabled) {
        this.logger.warn(`Auto-recharge skipped for organization ${organization._id}: Auto-recharge is disabled`);
        return false;
      }

      // Check if organization credits are below the threshold
      const orgCredits = organization.credits || 0;
      const threshold = organization.autoRechargeThreshold || 1.0;

      this.logger.log(`Organization ${organization._id} credits: $${orgCredits.toFixed(2)}, threshold: $${threshold.toFixed(2)}`);

      if (orgCredits > threshold) {
        this.logger.log(`Auto-recharge not needed for organization ${organization._id}: Credits ($${orgCredits.toFixed(2)}) are above threshold ($${threshold.toFixed(2)})`);
        return false;
      }

      // Get the default payment method for the organization
      this.logger.log(`Fetching payment methods for organization ${organization._id}`);
      const paymentMethods = await this.billingService.getOrganizationPaymentMethods(organization._id.toString());

      this.logger.log(`Found ${paymentMethods.length} payment methods for organization ${organization._id}`);

      const defaultPaymentMethod = paymentMethods.find(pm => pm.isDefault);

      // If no default payment method, can't auto-recharge
      if (!defaultPaymentMethod) {
        this.logger.warn(`Auto-recharge failed for organization ${organization._id}: No default payment method found`);
        return false;
      }

      this.logger.log(`Using default payment method: ${defaultPaymentMethod._id} (${defaultPaymentMethod.stripePaymentMethodId})`);

      // Determine the recharge amount
      let rechargeAmount = organization.autoRechargeAmount || 0;
      this.logger.log(`Initial auto-recharge amount from settings: $${rechargeAmount.toFixed(2)}`);

      // If auto-recharge amount is not set or is zero, use the last payment amount
      if (!rechargeAmount || rechargeAmount <= 0) {
        this.logger.log(`Auto-recharge amount not set for organization ${organization._id}, checking transaction history`);

        // Get the last successful payment from transaction history
        const transactionHistory = await this.billingService.getOrganizationTransactionHistory(organization._id.toString(), 1, 10);

        if (transactionHistory && transactionHistory.transactions && transactionHistory.transactions.length > 0) {
          this.logger.log(`Found ${transactionHistory.transactions.length} transactions in history`);

          // Find the most recent successful payment
          const lastSuccessfulPayment = transactionHistory.transactions.find(t =>
            t.status === 'succeeded' && t.amount > 0 && !t.description?.includes('Auto-recharge')
          );

          if (lastSuccessfulPayment) {
            rechargeAmount = lastSuccessfulPayment.amount;
            this.logger.log(`Using last payment amount for auto-recharge: $${rechargeAmount.toFixed(2)} (transaction ID: ${lastSuccessfulPayment._id})`);
          } else {
            this.logger.log(`No suitable previous payment found in transaction history`);
          }
        } else {
          this.logger.log(`No transaction history found for organization ${organization._id}`);
        }

        // If still no amount determined, use default minimum ($10)
        if (!rechargeAmount || rechargeAmount <= 0) {
          rechargeAmount = 10.0; // Default minimum amount
          this.logger.log(`No previous payment found, using default minimum amount: $${rechargeAmount.toFixed(2)}`);
        }
      }

      // Ensure minimum payment amount of $10
      if (rechargeAmount < 10.0) {
        this.logger.log(`Recharge amount $${rechargeAmount.toFixed(2)} is below minimum, adjusting to $10.00`);
        rechargeAmount = 10.0;
      }

      // Convert to cents for Stripe
      const amountInCents = Math.round(rechargeAmount * 100);
      this.logger.log(`Processing payment of $${rechargeAmount.toFixed(2)} (${amountInCents} cents) for organization ${organization._id}`);

      try {
        // Process the payment
        this.logger.log(`Calling billingService.processOrganizationPayment with organizationId: ${organization._id}, userId: ${userId}, paymentMethodId: ${defaultPaymentMethod.stripePaymentMethodId}`);

        await this.billingService.processOrganizationPayment(
          organization._id.toString(),
          userId,
          user.email,
          defaultPaymentMethod.stripePaymentMethodId,
          {
            amount: amountInCents,
            currency: 'usd',
            description: `Auto-recharge: $${rechargeAmount.toFixed(2)}`,
          },
          organization.name,
          false, // Don't save payment method again
          false  // Don't set as default
        );

        this.logger.log(`Auto-recharge payment processed successfully for organization ${organization._id}`);

        // Verify the credits were actually added by fetching the organization again
        const updatedOrganization = await this.organizationsService.findOne(organization._id.toString());
        this.logger.log(`Organization credits after auto-recharge: $${updatedOrganization.credits.toFixed(2)} (was $${orgCredits.toFixed(2)})`);

        this.logger.log(`Auto-recharge successful for organization ${organization._id}: Added $${rechargeAmount.toFixed(2)}`);
        return true;
      } catch (paymentError) {
        this.logger.error(`Payment processing failed during auto-recharge for organization ${organization._id}:`, paymentError);
        throw paymentError; // Re-throw to be caught by the outer try-catch
      }
    } catch (error) {
      this.logger.error(`Auto-recharge failed for user ${userId}:`, error);
      if (error.response) {
        this.logger.error(`Error response: ${JSON.stringify(error.response.data || {})}`);
      }
      return false;
    }
  }
}
