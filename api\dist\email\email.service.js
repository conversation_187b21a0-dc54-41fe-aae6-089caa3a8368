"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const node_mailjet_1 = __importDefault(require("node-mailjet"));
let EmailService = class EmailService {
    constructor(configService) {
        this.configService = configService;
        const apiKey = this.configService.get("MAILJET_API_KEY");
        const apiSecret = this.configService.get("MAILJET_API_SECRET");
        if (apiKey && apiSecret) {
            this.mailjet = new node_mailjet_1.default({
                apiKey: apiKey,
                apiSecret: apiSecret,
            });
        }
    }
    async sendEmail(to, subject, htmlContent, textContent) {
        if (!this.mailjet) {
            console.error("Mailjet not configured. Please set MAILJET_API_KEY and MAILJET_API_SECRET");
            return false;
        }
        try {
            const request = this.mailjet.post("send", { version: "v3.1" }).request({
                Messages: [
                    {
                        From: {
                            Email: this.configService.get("MAILJET_FROM_EMAIL") ||
                                "<EMAIL>",
                            Name: this.configService.get("MAILJET_FROM_NAME") ||
                                "Orova AI",
                        },
                        To: [
                            {
                                Email: to,
                            },
                        ],
                        Subject: subject,
                        TextPart: textContent || "",
                        HTMLPart: htmlContent,
                    },
                ],
            });
            const result = await request;
            console.log("Email sent successfully:", result.body);
            return true;
        }
        catch (error) {
            console.error("Error sending email:", error);
            return false;
        }
    }
    generateCreditRunoutEmail(clientName, currentCredit, fundingUrl) {
        const subject = "Orova Notification Low Balance";
        const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Orova Notification</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 40px 20px;
            background-color: #f5f5f5;
          }
          .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          }
          .header {
            background-color: #ffffff;
            padding: 40px 30px 20px;
            text-align: center;
          }
          .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
          }
          .company-name {
            font-size: 28px;
            font-weight: 600;
            color: #4a4a7a;
            margin: 0 0 10px 0;
          }
          .notification-id {
            font-size: 16px;
            color: #9ca3af;
            margin: 0 0 30px 0;
          }
          .content {
            padding: 0 30px 40px;
          }
          .info-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
            width: 100%;
            box-sizing: border-box;
          }
          .info-col {
            text-align: center;
            flex: 1 1 150px;
            min-width: 120px;
            max-width: 200px;
          }
          .info-label {
            font-size: 12px;
            color: #9ca3af;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
            display: block;
          }
          .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            display: block;
          }
          .greeting {
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
          }
          .message {
            font-size: 16px;
            color: #4b5563;
            margin: 20px 0;
            line-height: 1.6;
          }
          .cta-container {
            text-align: center;
            margin: 30px 0;
          }
          .cta-button {
            display: inline-block;
            background-color: #4a4a7a;
            color: white !important;
            text-decoration: none;
            padding: 14px 28px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
            text-decoration: none !important;
          }
          .cta-button:hover {
            background-color: #3a3a6a;
            transform: translateY(-1px);
            text-decoration: none !important;
            color: white !important;
          }
          .support-text {
            font-size: 14px;
            color: #6b7280;
            margin: 30px 0 10px 0;
            line-height: 1.5;
          }
          .footer-text {
            font-size: 12px;
            color: #9ca3af;
            margin: 0;
            text-align: center;
          }
          .link {
            color: #4a4a7a;
            text-decoration: none;
          }
          a {
            color: inherit;
            text-decoration: none;
          }
          .info-value a {
            color: #111827 !important;
            text-decoration: none !important;
          }

          /* Mobile responsiveness */
          @media only screen and (max-width: 600px) {
            .email-container {
              margin: 0 10px;
            }
            .header {
              padding: 30px 20px 15px;
            }
            .content {
              padding: 0 20px 30px;
            }
            .info-section {
              flex-direction: column;
              gap: 20px;
              padding: 15px;
            }
            .info-col {
              flex: none;
              min-width: auto;
              max-width: none;
              width: 100%;
            }
            .company-name {
              font-size: 24px;
            }
            .cta-button {
              padding: 12px 24px;
              font-size: 14px;
            }
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <img src="https://static.orova.ai/logo/OROVA-Avatar.png" alt="Orova AI" class="logo" />
            <h1 class="company-name">Orova Notification Low Balance</h1>
            <p class="notification-id">Notification #${Date.now().toString().slice(-8)}</p>
          </div>

          <div class="content">
            <div class="info-section">
              <div class="info-col">
                <span class="info-label">Balance</span>
                <span class="info-value">$${currentCredit.toFixed(2)}</span>
              </div>
              <div class="info-col">
                <span class="info-label">Website</span>
                <span class="info-value">${this.configService.get("FRONTEND_URL") || "https://demo.orova.ai"}</span>
              </div>
              <div class="info-col">
                <span class="info-label">Minimum Funds</span>
                <span class="info-value">$10.00</span>
              </div>
            </div>

            <div class="greeting">Hey ${clientName} 👋</div>

            <div class="message">
              We've noticed your org has reached the minimum credit threshold. As a result, you may not have enough credits to cover your upcoming charges.
            </div>

            <div class="message">
              To ensure uninterrupted service, please purchase more credits:
            </div>

            <div class="cta-container">
              <a href="${fundingUrl}" class="cta-button">Add Funds</a>
            </div>

            <div class="support-text">
              If you have any questions, visit our support site at <a href="https://www.orova.ai/" class="link">https://www.orova.ai/</a>, contact us at <a href="mailto:<EMAIL>" class="link"><EMAIL></a>, or call us at <a href="tel:+***********" class="link">******-460-0095</a>.
            </div>

            <div class="footer-text">
              This is an automated email. Please do not reply to this email.
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
        const textContent = `
      Hey ${clientName},

      🚨 CREDIT ALERT: Your wallet is low on credits

      Your Orova AI account has run out of credits, which means you won't be able to make any new calls until you add more funds to your account.

      To ensure uninterrupted service, please purchase more credits by visiting: ${fundingUrl}

      Need Help?
      If you have any questions or need assistance, please don't hesitate to contact our support team.

      Best regards,
      The Orova AI Team

    `;
        return { subject, htmlContent, textContent };
    }
    generateCreditWarningEmail(clientName, currentCredit, fundingUrl) {
        const subject = "Orova Notification Out of Balance";
        const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Orova Notification</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 40px 20px;
            background-color: #f5f5f5;
          }
          .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          }
          .header {
            background-color: #ffffff;
            padding: 40px 30px 20px;
            text-align: center;
          }
          .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
          }
          .company-name {
            font-size: 28px;
            font-weight: 600;
            color: #4a4a7a;
            margin: 0 0 10px 0;
          }
          .notification-id {
            font-size: 16px;
            color: #9ca3af;
            margin: 0 0 30px 0;
          }
          .content {
            padding: 0 30px 40px;
          }
          .info-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
            width: 100%;
            box-sizing: border-box;
          }
          .info-col {
            text-align: center;
            flex: 1 1 150px;
            min-width: 120px;
            max-width: 200px;
          }
          .info-label {
            font-size: 12px;
            color: #9ca3af;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
            display: block;
          }
          .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            display: block;
          }
          .greeting {
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
          }
          .message {
            font-size: 16px;
            color: #4b5563;
            margin: 20px 0;
            line-height: 1.6;
          }
          .cta-container {
            text-align: center;
            margin: 30px 0;
          }
          .cta-button {
            display: inline-block;
            background-color: #4a4a7a;
            color: white !important;
            text-decoration: none;
            padding: 14px 28px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
            text-decoration: none !important;
          }
          .cta-button:hover {
            background-color: #3a3a6a;
            transform: translateY(-1px);
            text-decoration: none !important;
            color: white !important;
          }
          .support-text {
            font-size: 14px;
            color: #6b7280;
            margin: 30px 0 10px 0;
            line-height: 1.5;
          }
          .footer-text {
            font-size: 12px;
            color: #9ca3af;
            margin: 0;
            text-align: center;
          }
          .link {
            color: #4a4a7a;
            text-decoration: none;
          }
          a {
            color: inherit;
            text-decoration: none;
          }
          .info-value a {
            color: #111827 !important;
            text-decoration: none !important;
          }

          /* Mobile responsiveness */
          @media only screen and (max-width: 600px) {
            .email-container {
              margin: 0 10px;
            }
            .header {
              padding: 30px 20px 15px;
            }
            .content {
              padding: 0 20px 30px;
            }
            .info-section {
              flex-direction: column;
              gap: 20px;
              padding: 15px;
            }
            .info-col {
              flex: none;
              min-width: auto;
              max-width: none;
              width: 100%;
            }
            .company-name {
              font-size: 24px;
            }
            .cta-button {
              padding: 12px 24px;
              font-size: 14px;
            }
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <img src="https://static.orova.ai/logo/OROVA-Avatar.png" alt="Orova AI" class="logo" />
            <h1 class="company-name">Orova Notification Out of Balance</h1>
            <p class="notification-id">Notification #${Date.now().toString().slice(-8)}</p>
          </div>

          <div class="content">
            <div class="info-section">
              <div class="info-col">
                <span class="info-label">Balance</span>
                <span class="info-value">$${currentCredit.toFixed(2)}</span>
              </div>
              <div class="info-col">
                <span class="info-label">Website</span>
                <span class="info-value">${this.configService.get("FRONTEND_URL") || "https://demo.orova.ai"}</span>
              </div>
              <div class="info-col">
                <span class="info-label">Minimum Funds</span>
                <span class="info-value">$10.00</span>
              </div>
            </div>

            <div class="greeting">Hey ${clientName} 👋</div>

            <div class="message">
              We've noticed your org wallet is low on credits. As a result, you may not have enough credits to cover your upcoming charges.
            </div>

            <div class="message">
              To ensure uninterrupted service, please purchase more credits:
            </div>

            <div class="cta-container">
              <a href="${fundingUrl}" class="cta-button">Add Funds</a>
            </div>

            <div class="support-text">
              If you have any questions, visit our support site at <a href="https://www.orova.ai/" class="link">https://www.orova.ai/</a>, contact us at <a href="mailto:<EMAIL>" class="link"><EMAIL></a>, or call us at <a href="tel:+***********" class="link">******-460-0095</a>.
            </div>

            <div class="footer-text">
              This is an automated email. Please do not reply to this email.
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
        const textContent = `
      Hey ${clientName},

      ⚠️ CREDIT WARNING: Your wallet is running low on credits

      Current Credit Balance: $${currentCredit.toFixed(2)}

      To ensure uninterrupted service, we recommend adding funds to your account before your credits are completely depleted.

      Add funds by visiting: ${fundingUrl}

      Need Help?
      Thank you for using Orova AI. If you have any questions, please contact our support team.

      Best regards,
      The Orova AI Team

    `;
        return { subject, htmlContent, textContent };
    }
    async sendCreditRunoutNotification(clientName, email, currentCredit, fundingUrl) {
        const template = this.generateCreditRunoutEmail(clientName, currentCredit, fundingUrl);
        return this.sendEmail(email, template.subject, template.htmlContent, template.textContent);
    }
    async sendCreditWarningNotification(clientName, email, currentCredit, fundingUrl) {
        const template = this.generateCreditWarningEmail(clientName, currentCredit, fundingUrl);
        return this.sendEmail(email, template.subject, template.htmlContent, template.textContent);
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], EmailService);
//# sourceMappingURL=email.service.js.map