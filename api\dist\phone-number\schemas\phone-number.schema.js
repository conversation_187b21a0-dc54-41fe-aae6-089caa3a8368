"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhoneNumberSchema = void 0;
const mongoose_1 = require("mongoose");
exports.PhoneNumberSchema = new mongoose_1.Schema({
    id: { type: String },
    provider: { type: String },
    createdAt: { type: Date },
    updatedAt: { type: Date },
    orgId: { type: String },
    credentialId: { type: String },
    assistantId: { type: String },
    name: { type: String },
    number: { type: String },
    numberE164CheckEnabled: { type: Boolean },
    status: { type: String, default: 'active' },
    squadId: { type: String },
    workflowId: { type: String },
    fallbackDestination: {
        type: { type: String },
        number: { type: String },
        callerId: { type: String },
        description: { type: String },
        extension: { type: String },
        message: { type: String },
        numberE164CheckEnabled: { type: Boolean },
        transferPlan: {
            mode: { type: String }
        }
    },
    hooks: [
        {
            on: { type: String },
            do: [
                {
                    type: { type: String }
                }
            ]
        }
    ],
    server: {
        url: { type: String },
        timeoutSeconds: { type: Number },
        secret: { type: String },
        headers: { type: Object },
        backoffPlan: {
            maxRetries: { type: Number },
            type: { type: Object },
            baseDelaySeconds: { type: Number }
        }
    }
}, {
    strict: false,
    timestamps: true
});
exports.PhoneNumberSchema.pre("save", function (next) {
    this.updatedAt = new Date();
    next();
});
exports.PhoneNumberSchema.index({ number: 1 });
//# sourceMappingURL=phone-number.schema.js.map