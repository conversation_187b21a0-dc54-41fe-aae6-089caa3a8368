/**
 * Migration script to drop the unique index on contactName and create a compound index on contactName and phoneNumber
 * 
 * Run this script using:
 * node -e "require('./api/src/migrations/drop-contactName-index.js').migrate()"
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function migrate() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/orova';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Get the contacts collection
    const db = mongoose.connection.db;
    const contactsCollection = db.collection('contacts');

    // Get existing indexes
    const indexes = await contactsCollection.indexes();
    console.log('Current indexes:', indexes);

    // Check if contactName_1 index exists
    const contactNameIndex = indexes.find(index => 
      index.name === 'contactName_1' && index.unique === true
    );

    if (contactNameIndex) {
      // Drop the unique index on contactName
      await contactsCollection.dropIndex('contactName_1');
      console.log('Dropped unique index on contactName');
    } else {
      console.log('No unique index on contactName found');
    }

    // Check if compound index already exists
    const compoundIndex = indexes.find(index => 
      index.name === 'contactName_1_phoneNumber_1' && index.unique === true
    );

    if (!compoundIndex) {
      // Create compound index on contactName and phoneNumber
      await contactsCollection.createIndex(
        { contactName: 1, phoneNumber: 1 },
        { unique: true }
      );
      console.log('Created compound unique index on contactName and phoneNumber');
    } else {
      console.log('Compound index on contactName and phoneNumber already exists');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  }
}

module.exports = { migrate };

// If this script is run directly
if (require.main === module) {
  migrate().catch(console.error);
}
