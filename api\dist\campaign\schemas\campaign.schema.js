"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CampaignSchema = void 0;
const mongoose_1 = require("mongoose");
exports.CampaignSchema = new mongoose_1.Schema({
    createdBy: { type: String },
    name: { type: String, required: true },
    concurrentCalls: { type: Number, default: 1 },
    dailyCost: { type: Number, default: 0 },
    startDate: { type: Date, required: false },
    endDate: { type: Date || String, required: false, default: null },
    successRate: { type: Number, default: 0 },
    sentiment: {
        type: String,
        enum: ['positive', 'neutral', 'negative'],
        default: 'neutral'
    },
    status: {
        type: String,
        enum: ['active', 'paused', 'completed', 'inactive'],
        default: 'inactive'
    },
    contacts: [{
            contactId: { type: mongoose_1.Types.ObjectId, ref: 'Contact' },
            contactName: { type: String, required: false, default: '' },
            phoneNumber: { type: String, required: false, default: '' },
            _id: false
        }],
    agentId: { type: String, required: false },
    recallHours: { type: Number, default: 24 },
    maxRecalls: { type: Number, default: 3 },
    followUpDays: [{ type: String }],
    callSchedule: {
        startTime: { type: String, default: '09:00' },
        endTime: { type: String, default: '14:00' },
        timezone: { type: String, default: 'America/New_York' },
        daysOfWeek: [{ type: String }],
        callTime: { type: String, default: '09:00' }
    },
    callWindow: {
        startTime: { type: String, default: '09:00' },
        endTime: { type: String, default: '17:00' },
        daysOfWeek: [{ type: String }]
    },
    instantCall: { type: Boolean, default: false },
    batchIntervalMinutes: { type: Number, default: 3 },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
}, {
    timestamps: true
});
//# sourceMappingURL=campaign.schema.js.map