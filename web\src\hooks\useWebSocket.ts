/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useEffect, useState, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';

export function useWebSocket(namespace: string = '') {
  const [isConnected, setIsConnected] = useState(false);
  const socketRef = useRef<Socket | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Create socket connection
    try {
      const socketUrl = `${API_BASE_URL}${namespace ? `/${namespace}` : ''}`;
      console.log(`Connecting to WebSocket at: ${socketUrl}`);

      // Disconnect existing socket if any
      if (socketRef.current) {
        console.log('Disconnecting existing socket before creating a new one');
        socketRef.current.disconnect();
      }

      const socket = io(socketUrl, {
        transports: ['websocket', 'polling'], // Add polling as fallback
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 10,
        reconnectionDelay: 1000,
        timeout: 20000,
        forceNew: true, // Force a new connection
        path: '/socket.io/',
      });

      // Set up event listeners
      socket.on('connect', () => {
        console.log(`WebSocket connected to ${namespace || 'default'} namespace`);
        setIsConnected(true);
        setError(null);
      });

      socket.on('disconnect', (reason) => {
        console.log(`WebSocket disconnected from ${namespace || 'default'} namespace: ${reason}`);
        setIsConnected(false);
      });

      socket.on('connect_error', (err) => {
        console.error(`WebSocket connection error: ${err.message}`);
        setError(`Connection error: ${err.message}`);

        // Try to switch to polling if websocket fails
        try {
          if (socket.io?.opts?.transports?.[0] === 'websocket') {
            console.log('Switching to polling transport due to connection error');
            socket.io.opts.transports = ['polling', 'websocket'];
          }
        } catch (e) {
          console.error('Error switching transports:', e);
        }

        // Try to reconnect manually after a delay
        setTimeout(() => {
          console.log('Attempting to reconnect manually...');
          socket.connect();
        }, 3000);
      });

      // Add a ping/pong mechanism to keep the connection alive
      const pingInterval = setInterval(() => {
        if (socket.connected) {
          console.log('Sending ping to server');
          socket.emit('ping', {}, (response: any) => {
            console.log('Received pong from server:', response);
          });
        }
      }, 30000); // Every 30 seconds

      // Store socket in ref
      socketRef.current = socket;

      // Clean up on unmount
      return () => {
        clearInterval(pingInterval);
        if (socket) {
          console.log(`Disconnecting from WebSocket at: ${socketUrl}`);
          socket.disconnect();
          socketRef.current = null;
        }
      };
    } catch (error) {
      console.error('Error setting up WebSocket connection:', error);
      // setError(`WebSocket setup error: ${error.message}`);
      return () => {};
    }
  }, [namespace]);

  // Function to emit events
  const emit = useCallback((event: string, data: any, callback?: (response: any) => void) => {
    if (socketRef.current && isConnected) {
      if (callback) {
        socketRef.current.emit(event, data, callback);
      } else {
        socketRef.current.emit(event, data);
      }
      return true;
    }
    return false;
  }, [isConnected]);

  // Function to subscribe to events
  const on = useCallback((event: string, callback: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
      return () => {
        socketRef.current?.off(event, callback);
      };
    }
    return () => {};
  }, []);

  // Function to register user with socket
  const registerUser = useCallback((userId: string) => {
    if (!userId) {
      console.error('Cannot register user: userId is empty');
      return false;
    }

    if (socketRef.current && isConnected) {
      console.log(`Registering user ${userId} with socket ${socketRef.current.id}`);
      socketRef.current.emit('registerUser', { userId }, (response: any) => {
        if (response && response.success) {
          console.log(`User ${userId} successfully registered with socket: ${response.message}`);
        } else {
          console.error(`Failed to register user ${userId} with socket:`, response);
        }
      });
      return true;
    } else {
      console.warn(`Cannot register user ${userId}: socket is ${socketRef.current ? 'created' : 'not created'} and connection is ${isConnected ? 'active' : 'inactive'}`);

      // If socket exists but not connected, try to connect
      if (socketRef.current && !isConnected) {
        console.log('Attempting to connect socket before registering user');
        socketRef.current.connect();

        // Schedule another registration attempt after connection
        setTimeout(() => {
          if (socketRef.current && socketRef.current.connected) {
            console.log(`Retrying registration for user ${userId} after connection`);
            socketRef.current.emit('registerUser', { userId });
          }
        }, 1000);
      }

      return false;
    }
  }, [isConnected]);

  return { isConnected, error, emit, on, registerUser, socket: socketRef.current };
}
