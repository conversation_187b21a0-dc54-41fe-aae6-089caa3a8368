"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LoggerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const schedule_1 = require("@nestjs/schedule");
let LoggerService = LoggerService_1 = class LoggerService {
    constructor(logModel) {
        this.logModel = logModel;
        this.logger = new common_1.Logger(LoggerService_1.name);
        this.logFilePath = path.join(__dirname, '../../logs/api.log');
        if (!fs.existsSync(path.dirname(this.logFilePath))) {
            fs.mkdirSync(path.dirname(this.logFilePath), { recursive: true });
        }
    }
    writeToFile(message) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${message}\n`;
        fs.appendFileSync(this.logFilePath, logMessage, 'utf8');
    }
    async saveToDatabase(level, message, trace) {
        const traceString = trace ? (typeof trace === 'string' ? trace : JSON.stringify(trace)) : null;
        const logEntry = new this.logModel({ level, message, trace: traceString });
        await logEntry.save();
    }
    async log(message) {
        this.logger.log(message);
        this.writeToFile(`[INFO] ${message}`);
        await this.saveToDatabase('INFO', message);
    }
    async warn(message) {
        this.logger.warn(message);
        this.writeToFile(`[WARN] ${message}`);
        await this.saveToDatabase('WARN', message);
    }
    async error(message, trace) {
        this.logger.error(message, trace);
        const traceStr = trace ? (typeof trace === 'string' ? trace : JSON.stringify(trace)) : 'No stack trace';
        this.writeToFile(`[ERROR] ${message} - ${traceStr}`);
        await this.saveToDatabase('ERROR', message, trace);
    }
    async deleteOldLogs(days = 4) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        const result = await this.logModel.deleteMany({
            timestamp: { $lt: cutoffDate }
        }).exec();
        this.logger.log(`Deleted ${result.deletedCount} logs older than ${days} days`);
        return result.deletedCount;
    }
    async scheduledLogCleanup() {
        try {
            const deletedCount = await this.deleteOldLogs();
            this.logger.log(`Scheduled cleanup: Removed ${deletedCount} logs older than 4 days`);
        }
        catch (error) {
            this.logger.error(`Error during scheduled log cleanup: ${error.message}`, error.stack);
        }
    }
};
exports.LoggerService = LoggerService;
__decorate([
    (0, schedule_1.Cron)('0 0 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoggerService.prototype, "scheduledLogCleanup", null);
exports.LoggerService = LoggerService = LoggerService_1 = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.DEFAULT }),
    __param(0, (0, mongoose_1.InjectModel)('Log')),
    __metadata("design:paramtypes", [mongoose_2.Model])
], LoggerService);
//# sourceMappingURL=logger.service.js.map