import { Document } from 'mongoose';
export type ScheduledCallDocument = ScheduledCall & Document;
export declare class ScheduledCall {
    agentId: string;
    contacts: {
        Name: string;
        MobileNumber: string;
    }[];
    scheduledTime: Date;
    region: string;
    status: string;
    scheduledByName: string;
    scheduledByTimestamp: Date;
    retryCount: number;
    lastProcessedAt: Date;
}
export declare const ScheduledCallSchema: import("mongoose").Schema<ScheduledCall, import("mongoose").Model<ScheduledCall, any, any, any, Document<unknown, any, ScheduledCall> & ScheduledCall & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, ScheduledCall, Document<unknown, {}, import("mongoose").FlatRecord<ScheduledCall>> & import("mongoose").FlatRecord<ScheduledCall> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export interface UpdateScheduledCallDto {
    agentId?: string;
    contacts?: {
        Name: string;
        MobileNumber: string;
    }[];
    scheduledTime?: Date;
    region?: string;
    status?: string;
    scheduledByName?: string;
    scheduledByTimestamp?: Date;
    retryCount?: number;
    lastProcessedAt?: Date;
}
