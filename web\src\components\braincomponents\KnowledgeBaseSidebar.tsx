"use client";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Link as LinkIcon, FileSpreadsheet, File } from "lucide-react";

type SidebarNavProps = {
  items: {
    title: string;
    icon: React.ReactNode;
    href: string;
  }[];
  currentPath: string;
  onNavigate: (href: string) => void;
};

export function KnowledgeBaseSidebar({
  items,
  currentPath,
  onNavigate,
}: SidebarNavProps) {
  return (
    <nav className="flex flex-col space-y-1">
      {items.map((item) => (
        <Button
          key={item.href}
          variant={currentPath === item.href ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start gap-2",
            currentPath === item.href
              ? "bg-gray-100 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
              : "hover:bg-gray-50 dark:hover:bg-gray-800"
          )}
          onClick={() => onNavigate(item.href)}
        >
          {item.icon}
          {item.title}
        </Button>
      ))}
    </nav>
  );
}

export const sidebarItems = [
  {
    title: "Paragraphs",
    icon: <FileText className="h-5 w-5" />,
    href: "paragraphs",
  },
  {
    title: "Web Links",
    icon: <LinkIcon className="h-5 w-5" />,
    href: "web-links",
  },
  {
    title: "Files",
    icon: <File className="h-5 w-5" />,
    href: "files",
  },
  {
    title: "Spreadsheets",
    icon: <FileSpreadsheet className="h-5 w-5" />,
    href: "spreadsheets",
  },
];