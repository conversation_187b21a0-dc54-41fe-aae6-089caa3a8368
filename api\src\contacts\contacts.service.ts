import {Injectable,NotFoundException,HttpException,HttpStatus,Inject,forwardRef,OnModuleInit } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { ContactDto, UploadContactDto } from "./dto/contact.dto";
import { ContactDocument } from "./interfaces/contact.interface";
import { CampaignDocument } from "../campaign/interfaces/campaign.interface";
import { CampaignService } from "../campaign/campaign.service";
import axios from "axios";
import * as qs from "qs";
import { LoggerService } from "src/logger/logger.service";
import { ScheduledCallService } from "src/scheduled-call/scheduled-call.service";
import { toWords } from "number-to-words";
import moment from "moment-timezone";
import { PhoneNumberUtil } from "google-libphonenumber";

@Injectable()
export class ContactsService implements OnModuleInit {
  constructor(
    @InjectModel("Contact")
    private readonly contactModel: Model<ContactDocument>,
    @InjectModel("Campaign")
    private readonly campaignModel: Model<CampaignDocument>,
    private readonly loggerService: LoggerService,
    private readonly scheduledCallService: ScheduledCallService,
    @Inject(forwardRef(() => CampaignService))
    private readonly campaignService: CampaignService
  ) {}

  async onModuleInit() {
    try {
      // Get the underlying MongoDB collection
      const collection = this.contactModel.collection;

      // Get existing indexes
      const indexes = await collection.indexes();

      // Check if contactName_1 index exists
      const contactNameIndex = indexes.find(
        (index) => index.name === "contactName_1" && index.unique === true
      );

      if (contactNameIndex) {
        // Drop the unique index on contactName
        await collection.dropIndex("contactName_1");
        await this.loggerService.log("Dropped unique index on contactName");
      }

      // Check if compound index already exists
      const compoundIndex = indexes.find(
        (index) =>
          index.name === "contactName_1_phoneNumber_1" && index.unique === true
      );

      if (!compoundIndex) {
        // Create compound index on contactName and phoneNumber
        await collection.createIndex(
          { contactName: 1, phoneNumber: 1 },
          { unique: true }
        );
        await this.loggerService.log(
          "Created compound unique index on contactName and phoneNumber"
        );
      }
    } catch (error) {
      await this.loggerService.error("Error updating indexes:", error.message);
    }
  }

  private normalizePhoneNumber(phone: string): string {
    if (!phone) return phone;
    return phone.startsWith("+") ? phone : `+${phone}`;
  }
  private async getRegionFromPhoneNumber(phone: string): Promise<string> {
    const phoneUtil = PhoneNumberUtil.getInstance();
    try {
      const number = phoneUtil.parse(phone);
      const regionCode = phoneUtil.getRegionCodeForNumber(number);

      const zones = moment.tz.zonesForCountry(regionCode);
      if (zones && zones.length > 0) {
        return zones[0];
      }

      return "Unknown";
    } catch (error) {
      return "Unknown";
    }
  }

  async create(createContactDto: ContactDto, userName: string): Promise<ContactDocument> {
    createContactDto.phoneNumber = this.normalizePhoneNumber(
      createContactDto.phoneNumber
    );

    let region = createContactDto.region || null;
    if (!region) {
      createContactDto.region = await this.getRegionFromPhoneNumber(
        createContactDto.phoneNumber
      );
    }

    // Check for duplicate contact based on name and phone number
    const existingContact = await this.contactModel
      .findOne({
        contactName: createContactDto.contactName,
        phoneNumber: createContactDto.phoneNumber,
      })
      .exec();

    if (existingContact) {
      throw new HttpException(
        "A contact with this name and phone number already exists",
        HttpStatus.CONFLICT
      );
    }

    const createdContact = new this.contactModel({
    ...createContactDto,
    source: 'manual',
    addedBy: userName,
  });

    if (createContactDto.campaigns && createContactDto.campaigns.length > 0) {
      // Add the contact to each campaign with full contact details
      for (const campaignId of createContactDto.campaigns) {
        await this.campaignModel.updateOne(
          { _id: campaignId },
          {
            $addToSet: {
              contacts: {
                contactId: createdContact._id,
                contactName: createdContact.contactName,
                phoneNumber: createdContact.phoneNumber,
              },
            },
          }
        );
      }

      // Save the contact first to get the ID
      const savedContact = await createdContact.save();

      // Schedule calls for the contact in each campaign
      try {
        for (const campaignId of createContactDto.campaigns) {
          const campaign = await this.campaignModel.findById(campaignId).exec();
          if (campaign) {
            await this.loggerService.log(
              `Scheduling calls for new contact ${savedContact.contactName} in campaign ${campaign.name}`
            );

            // Use the injected campaign service

            // Schedule calls for this contact in the campaign
            await this.campaignService.scheduleCallsForContactInCampaign(
              campaign,
              savedContact
            );
          }
        }

        return savedContact;
      } catch (error) {
        await this.loggerService.error(
          `Error scheduling calls for new contact:`,
          error.message
        );
        return savedContact;
      }
    }

    return createdContact.save();
  }

  async update(
    id: string,
    updateContactDto: ContactDto
  ): Promise<ContactDocument> {
    const existingContact = await this.contactModel.findById(id).exec();
    if (!existingContact) throw new NotFoundException("Contact not found");

    if (updateContactDto.phoneNumber) {
      updateContactDto.phoneNumber = this.normalizePhoneNumber(
        updateContactDto.phoneNumber
      );
    }

    // Check if there's another contact with the same name and phone number (excluding this one)
    if (updateContactDto.contactName && updateContactDto.phoneNumber) {
      const duplicateContact = await this.contactModel
        .findOne({
          _id: { $ne: id }, // exclude current contact
          contactName: updateContactDto.contactName,
          phoneNumber: updateContactDto.phoneNumber,
        })
        .exec();

      if (duplicateContact) {
        throw new HttpException(
          "Another contact with this name and phone number already exists",
          HttpStatus.CONFLICT
        );
      }
    }

    if (updateContactDto.campaigns) {
      // First, remove the contact from all campaigns it was previously in
      await this.campaignModel.updateMany(
        { "contacts.contactId": existingContact._id },
        { $pull: { contacts: { contactId: existingContact._id } } }
      );

      // Then, add the contact to the new campaigns with full contact details
      for (const campaignId of updateContactDto.campaigns) {
        await this.campaignModel.updateOne(
          { _id: campaignId },
          {
            $addToSet: {
              contacts: {
                contactId: existingContact._id,
                contactName:
                  updateContactDto.contactName || existingContact.contactName,
                phoneNumber:
                  updateContactDto.phoneNumber || existingContact.phoneNumber,
              },
            },
          }
        );
      }

      // Update the campaigns field in the contact document
      await this.contactModel.updateOne(
        { _id: existingContact._id },
        { $set: { campaigns: updateContactDto.campaigns } }
      );

      // Schedule calls for the contact in the new campaigns
      try {
        // Find the newly added campaign IDs
        const existingCampaignIds = existingContact.campaigns || [];
        const newCampaignIds = updateContactDto.campaigns.filter(
          (campaignId) => !existingCampaignIds.includes(campaignId)
        );

        if (newCampaignIds.length > 0) {
          // Get the updated contact with the new campaigns
          const updatedContact = await this.contactModel
            .findById(existingContact._id)
            .exec();

          // For each new campaign, schedule calls for this contact
          for (const campaignId of newCampaignIds) {
            const campaign = await this.campaignModel
              .findById(campaignId)
              .exec();
            if (campaign) {
              await this.loggerService.log(
                `Scheduling calls for contact ${updatedContact.contactName} in campaign ${campaign.name}`
              );

              // Use the injected campaign service

              // Schedule calls for this contact in the campaign
              await this.campaignService.scheduleCallsForContactInCampaign(
                campaign,
                updatedContact
              );
            }
          }
        }
      } catch (error) {
        await this.loggerService.error(
          `Error scheduling calls for contact:`,
          error.message
        );
      }
    }

    return this.contactModel
      .findByIdAndUpdate(id, updateContactDto, { new: true })
      .exec();
  }

  async findAll(
    page?: number,
    limit?: number,
    search?: string,
    filterType: 'name' | 'campaign' = 'name',
    campaignId?: string,
    noCampaign?: boolean,
  ): Promise<ContactDocument[]> {
    try {
      const totalCount = await this.contactModel.countDocuments().exec();
      // Build the filter object
      const filter: any = {};

      // Add search filter if provided
      if (search && search.trim().length >= 1) {
        // Escape special characters in the search string
        const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const searchRegex = new RegExp(escapedSearch, 'i');

        if (filterType === 'name') {
          filter.$or = [
            { contactName: { $regex: searchRegex } },
            { phoneNumber: { $regex: searchRegex } }
          ];
        } else if (filterType === 'campaign') {
          // For campaign filtering, we need to first find campaigns matching the search
          const matchingCampaigns = await this.campaignModel
            .find({ name: { $regex: searchRegex } })
            .select('_id')
            .exec();

          // Extract campaign IDs
          const campaignIds = matchingCampaigns.map(campaign => campaign._id);

          // Find contacts that have any of these campaign IDs
          if (campaignIds.length > 0) {
            filter.campaigns = { $in: campaignIds };
          } else {
            // If no matching campaigns, return no results
            filter._id = { $exists: false }; // This will match no documents
          }
        }
      }

       // Add campaign filter if provided
      if (campaignId) {
        filter.campaigns = campaignId;
      }

      // Handle "no campaign" filter
      if (noCampaign) {
        filter.$or = [
          { campaigns: { $exists: false } },
          { campaigns: { $size: 0 } },
          { campaigns: null }
        ];
      }

       // Get filtered count if search is active
      let filteredCount = totalCount;
      if (Object.keys(filter).length > 0) {
        filteredCount = await this.contactModel.countDocuments(filter).exec();
      }


      // Build the base query
      let query = this.contactModel
        .find(filter)
        .sort({ contactName: 1 })
        .populate('campaigns', 'name');

      // Apply pagination if both page and limit are provided
      if (page && limit) {
        const skip = (page - 1) * limit;
        query = query.skip(skip).limit(limit);
      }

      // Execute the query
    const contacts = await query.exec();

    // Add the total count to each call object
    contacts.forEach(contact => {
      // Set the value that will be used by the virtual getter
      contact['_totalCount'] = totalCount;
      contact['_filteredCount'] = filteredCount;
    });

    return contacts;

    } catch (error) {
      await this.loggerService.error('Error retrieving contacts:', error.message);
      throw error;
    }
  }


  async findById(id: string): Promise<ContactDocument> {
    const contact = await this.contactModel
      .findById(id)
      .populate("campaigns", "name")
      .exec();
    if (!contact) throw new NotFoundException("Contact not found");
    return contact;
  }

  async remove(id: string): Promise<void> {
    const result = await this.contactModel.findByIdAndDelete(id).exec();
    if (!result) throw new NotFoundException("Contact not found");

    // Remove the contact from all campaigns it was in
    await this.campaignModel.updateMany(
      { "contacts.contactId": result._id },
      { $pull: { contacts: { contactId: result._id } } }
    );
  }

  async searchContacts(query: string): Promise<ContactDocument[]> {
    const searchRegex = new RegExp(query, "i");
    return this.contactModel
      .find({
        $or: [
          { contactName: { $regex: searchRegex } },
          { phoneNumber: { $regex: searchRegex } },
        ],
      })
      .populate("campaigns", "name")
      .exec();
  }

  async filterBycampaign(campaign: string[]): Promise<ContactDocument[]> {
    return this.contactModel
      .find({
        campaigns: { $in: campaign },
      })
      .populate("campaigns", "name")
      .exec();
  }

  async getToken(): Promise<string> {
    const data = qs.stringify({
      grant_type: "client_credentials",
      client_id:
        process.env.CLIENT_ID || "8f9180d3-8259-41ab-9b68-380f4cd5aaa7",
      client_secret:
        process.env.CLIENT_SECRET || "****************************************",
      scope:
        process.env.SCOPE || "8f9180d3-8259-41ab-9b68-380f4cd5aaa7/.default",
    });

    try {
      const response = await axios.post(
        "https://login.microsoftonline.com/b8ed89d6-dafd-4034-801a-1e7d96625270/oauth2/v2.0/token",
        data,
        { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
      );
      return response.data.access_token;
    } catch (error) {
      console.error(
        "Error fetching token:",
        error.response?.data || error.message
      );
      throw new HttpException(
        "Error fetching token",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async processContacts(externalContacts: any[]): Promise<ContactDocument[]> {
    const processedContacts: ContactDocument[] = [];
    // Map of campaignId -> array of contacts to schedule
    const contactsByCampaign: Record<string, ContactDocument[]> = {};

    for (const c of externalContacts) {
      // Normalize phone number
      const normalizedPhone = this.normalizePhoneNumber(c.mobileNumber);

      // Determine region
      let region = c.region || null;
      if (!region) {
        region = await this.getRegionFromPhoneNumber(normalizedPhone);
      }

      // Look up campaign IDs by name
      const campaignIds: string[] = [];
      if (c.campaign && c.campaign.length > 0) {
        const camp = await this.campaignModel.findOne({ name: c.campaign }).exec();
        if (camp) {
          campaignIds.push(camp.id);
        } else {
          await this.loggerService.log(`Campaign not found for name: ${c.campaign}`);
        }
      }

      // Build contact data object
      const contactData = {
        contactName: c.name,
        phoneNumber: normalizedPhone,
        lastCall: c.previousCallTime || null,
        region,
        campaigns: campaignIds,
        updatedAt: new Date(),
        source: 'CRM',
        customerId: c.zohoCRMRecordId || null,
        projectName: c.projectName || null,
        unitNumber: c.unitNumber || null,
        totalPayableAmount: c.totalPayableAmount || null,
        pendingPayableAmount: c.pendingPayableAmount || null,
        dueDate: c.dueDate || null,
        totalInstallments: c.totalInstallments || null,
        paymentType: c.paymentType || null,
        pendingInstallments: c.pendingInstallments || null,
        lastPaymentDate: c.lastPaymentDate || null,
        lastPaymentAmount: c.lastPaymentAmount || null,
        lastPaymentType: c.lastPaymentType || null,
        collectionBucket: c.collectionBucket || null,
        unitPrice: c.unitPrice || null,
        paidAmtIncluding: c.paidAmtIncluding || null,
        eventDate: c.eventDate || null,
        eventLocation: c.eventLocation || null,
        eventTime: c.eventTime || null,
        nameOfRegistrant: c.nameOfRegistrant || null,
      };

      // Check for existing contact by name+phone
      const existingContact = await this.contactModel
        .findOne({ contactName: c.name, phoneNumber: normalizedPhone })
        .exec();
      // If not found, check by customerId
      const existingByCustomerId = !existingContact && c.zohoCRMRecordId
        ? await this.contactModel.findOne({ customerId: c.zohoCRMRecordId }).exec()
        : null;

      // Decide whether to update or create
      let savedContact: ContactDocument;
      const toUpdate = existingContact || existingByCustomerId;
      if (toUpdate) {
        savedContact = await this.contactModel
          .findByIdAndUpdate(toUpdate._id, contactData, { new: true })
          .exec();
        await this.loggerService.log(
          `Updated existing contact: ${c.name} (${normalizedPhone}).`
        );
      } else {
        savedContact = await new this.contactModel(contactData).save();
        await this.loggerService.log(
          `Imported new contact: ${c.name} (${normalizedPhone}).`
        );
      }

      processedContacts.push(savedContact);

      // Accumulate for bulk scheduling
      for (const campId of campaignIds) {
        if (!contactsByCampaign[campId]) {
          contactsByCampaign[campId] = [];
        }
        contactsByCampaign[campId].push(savedContact);

        // Add the contact to the campaign's contacts array
        await this.campaignModel.updateOne(
          { _id: campId },
          {
            $addToSet: {
              contacts: {
                contactId: savedContact._id,
                contactName: savedContact.contactName,
                phoneNumber: savedContact.phoneNumber,
              },
            },
          }
        );
        await this.loggerService.log(
          `Added contact ${savedContact.contactName} to campaign ${campId}`
        );
      }
    }

    // Bulk-schedule once per campaign
    for (const [campId, contacts] of Object.entries(contactsByCampaign)) {
      const campaign = await this.campaignModel.findById(campId).exec();
      if (!campaign) continue;
      await this.loggerService.log(
        `Bulk scheduling ${contacts.length} contacts for campaign ${campaign.name}`
      );
      // Make sure scheduleCallsForContacts is public
      await this.campaignService.scheduleCallsForContacts(campaign, contacts);
    }

    return processedContacts;
  }


  async callData(number: string, name: string): Promise<any> {
    const normalizedPhone = this.normalizePhoneNumber(number);
    const contact = await this.contactModel
      .findOne({ contactName: name, phoneNumber: normalizedPhone })
      .exec();

    if (!contact) {
      throw new NotFoundException("Contact not found");
    }

    const totalPayableAmountWords =
      contact.totalPayableAmount !== null &&
      contact.totalPayableAmount !== undefined
        ? toWords(contact.totalPayableAmount)
        : null;
    const pendingPayableAmountWords =
      contact.pendingPayableAmount !== null &&
      contact.pendingPayableAmount !== undefined
        ? toWords(contact.pendingPayableAmount)
        : null;
    const lastPaymentAmountWords =
      contact.lastPaymentAmount !== null &&
      contact.lastPaymentAmount !== undefined
        ? toWords(contact.lastPaymentAmount)
        : null;
    const unitPriceWords =
      contact.unitPrice !== null && contact.unitPrice !== undefined
        ? toWords(contact.unitPrice)
        : null;
    const paidAmtIncludingWords =
      contact.paidAmtIncluding !== null &&
      contact.paidAmtIncluding !== undefined
        ? toWords(contact.paidAmtIncluding)
        : null;

    const contactObject = contact.toObject();
    const { dueDate, eventDate, campaigns, updatedAt, _id, __v, ...otherData } =
      contactObject;

    if (totalPayableAmountWords) {
      otherData.totalPayableAmount = totalPayableAmountWords;
    }
    if (pendingPayableAmountWords) {
      otherData.pendingPayableAmount = pendingPayableAmountWords;
    }
    if (lastPaymentAmountWords) {
      otherData.lastPaymentAmount = lastPaymentAmountWords;
    }
    if (unitPriceWords) {
      otherData.unitPrice = unitPriceWords;
    }
    if (paidAmtIncludingWords) {
      otherData.paidAmtIncluding = paidAmtIncludingWords;
    }

    const FormateddueDate = dueDate
      ? moment(dueDate).format("Do MMMM YYYY")
      : null;

    const currentTime = moment()
      .tz(otherData.region)
      .format("YYYY-MM-DD HH:mm:ss");

    return {
      ...otherData,
      currentTime,
      dueDate: FormateddueDate,
    };
  }

  async importContacts(userName: string): Promise<ContactDocument[]> {
    const accessToken = await this.getToken();
    await this.loggerService.log("Starting import of contacts from CRM...");
    try {
      const leadsResponse = await axios.get(
        "https://apim-bgt-prd-001.azure-api.net/ai/api/vw_ai_leads",
        { headers: { Authorization: `Bearer ${accessToken}` } }
      );
      await this.loggerService.log("Retrieved contacts data from CRM.");

      const externalContacts = leadsResponse.data.value;
      const savedContacts: ContactDocument[] = [];

      for (const c of externalContacts) {
        const normalizedPhone = this.normalizePhoneNumber(c.MobileNumber);

        // Check for duplicate contact based on name and phone number
        const existingContact = await this.contactModel
          .findOne({
            contactName: c.Name,
            phoneNumber: normalizedPhone,
          })
          .exec();

        if (!existingContact) {
          const contactData = {
            contactName: c.Name,
            phoneNumber: normalizedPhone,
            lastCall: c.previousCallTime,
            campaigns: [],
            updatedAt: new Date(),
            addedBy: userName,
            source: "CRM",
          };

          const savedContact = await new this.contactModel(contactData).save();
          savedContacts.push(savedContact);
          await this.loggerService.log(
            `Imported new contact: ${c.Name} (${normalizedPhone}).`
          );
        } else {
          await this.loggerService.log(
            `Contact already exists: ${c.Name} (${normalizedPhone}).`
          );
        }
      }

      await this.loggerService.log(
        `Import completed. ${savedContacts.length} new contacts imported.`
      );
      return savedContacts;
    } catch (error) {
      await this.loggerService.error(
        "Error importing contacts",
        error.response?.data || error.message
      );
      throw new HttpException(
        "Error importing contacts",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async uploadContacts(contacts: UploadContactDto[], userName: string): Promise<any> {
  try {
    const results = {
      success: [],
      errors: [],
      successfulContacts: [] // Array to store successfully processed contacts
    };

    for (const contact of contacts) {
      try {
        // Only check required fields
        if (!contact.contactName || !contact.phoneNumber) {
          results.errors.push({
            contact: contact.contactName || 'Unknown',
            error: 'Contact name and phone number are required'
          });
          continue;
        }

        // Normalize phone number
        const normalizedPhone = this.normalizePhoneNumber(contact.phoneNumber);

        // Check for existing contact
        const existingContact = await this.contactModel.findOne({
          contactName: contact.contactName,
          phoneNumber: normalizedPhone,
        });

        if (existingContact) {
          results.errors.push({
            contact: contact.contactName,
            error: 'Contact already exists'
          });
          continue;
        }

        // Create new contact with minimal fields
        const newContact = new this.contactModel({
          contactName: contact.contactName,
          phoneNumber: normalizedPhone,
          customerId: contact.customerId || null, 
          campaignNames: contact.campaignNames || [],
          source: 'file Upload',
          addedBy: userName,
          updatedAt: new Date(),
          ...(contact.region && { region: contact.region })
        });

        await newContact.save();
        results.success.push({
          contact: contact.contactName,
          id: newContact._id
        });

        results.successfulContacts.push({
          contactName: contact.contactName,
          phoneNumber: normalizedPhone,
          customerId: contact.customerId, 
          region: contact.region,
          source: 'file Upload',
          addedBy: userName
        });

      } catch (error) {
        results.errors.push({
          contact: contact.contactName || 'Unknown',
          error: error.message
        });
      }
    }

    return {
      message: 'Upload processing completed',
      totalProcessed: contacts.length,
      successCount: results.success.length,
      errorCount: results.errors.length,
      successfulContacts: results.successfulContacts,
      results
    };

  } catch (error) {
    throw new HttpException(
      'Error processing upload',
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}




}
