import { Document } from 'mongoose';
export declare class OrganizationPaymentMethod {
    organizationId: string;
    type: string;
    stripePaymentMethodId: string;
    last4?: string;
    expMonth?: string;
    expYear?: string;
    cardholderName?: string;
    isDefault: boolean;
    brand?: string;
}
export type OrganizationPaymentMethodDocument = OrganizationPaymentMethod & Document;
export declare const OrganizationPaymentMethodSchema: import("mongoose").Schema<OrganizationPaymentMethod, import("mongoose").Model<OrganizationPaymentMethod, any, any, any, Document<unknown, any, OrganizationPaymentMethod> & OrganizationPaymentMethod & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, OrganizationPaymentMethod, Document<unknown, {}, import("mongoose").FlatRecord<OrganizationPaymentMethod>> & import("mongoose").FlatRecord<OrganizationPaymentMethod> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
