/* eslint-disable @typescript-eslint/no-explicit-any */

import { Agent } from "./agent.types";
import { Contact } from "@/app/api/contacts";


// Basic campaign contact interface
export interface CampaignContact {
  contactId?: string;
  contactName: string;
  phoneNumber: string;
  campaigns?: any[];
  createdAt?: Date;
}

// Campaign data interface
export interface CampaignData {
  name: string;
  concurrentCalls: number;
  dailyCost: number;
  startDate: string;
  endDate: string | null;
  successRate: number;
  sentiment: string;
  instantCall?: boolean;
  batchIntervalMinutes?: number;
  status: string;
  sources: Contact[];  // Use the full Contact type
  sourceType?: "contacts" | "import" | "thirdparty";
  agents: Agent[];
  agentId?: string;
  callSchedule: {
    startTime: string;
    endTime: string;
    timezone: string;
    daysOfWeek: DayOfWeek[];
  };
  callWindow: {
    startTime: string;
    endTime: string;
    timezone?: string;
    daysOfWeek: DayOfWeek[];
  };
  callRoute?: "outbound" | "inbound" | "both";
  recallHours?: number;
  maxRecalls?: number;
  followUpDays?: DayOfWeek[];
}

type DayOfWeek = "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday";