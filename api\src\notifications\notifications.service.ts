import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EmailService } from '../email/email.service';
import { Organization, OrganizationDocument } from '../organizations/interfaces/organization.interface';

@Injectable()
export class NotificationsService {
  constructor(
    private emailService: EmailService,
    private configService: ConfigService,
    @InjectModel('Organization') private organizationModel: Model<OrganizationDocument>
  ) {}

  /**
   * Check if organization should receive credit runout notification
   */
  shouldSendCreditRunoutNotification(organization: Organization): boolean {
    // Send notification if credits are at or below minimum threshold and email settings are configured
    const minimumThreshold = organization.minimumCreditsThreshold || 1.0;
    const hasCreditsIssue = organization.credits <= minimumThreshold;
    const hasEmailSettings = !!organization.email && !!organization.fullName;

    // Check if we haven't sent a runout email recently (within last 24 hours)
    const lastSent = organization.lastRunoutEmailSent;
    const canSendAgain = !lastSent || (Date.now() - new Date(lastSent).getTime()) > 24 * 60 * 60 * 1000;

    console.log(`Credit runout check for org ${organization._id}:`, {
      credits: organization.credits,
      minimumThreshold,
      hasCreditsIssue,
      hasEmailSettings,
      email: organization.email,
      fullName: organization.fullName,
      lastSent,
      canSendAgain,
      shouldSend: hasCreditsIssue && hasEmailSettings && canSendAgain
    });

    return hasCreditsIssue && hasEmailSettings && canSendAgain;
  }

  /**
   * Check if organization should receive credit warning notification
   */
  shouldSendCreditWarningNotification(organization: Organization): boolean {
    // Send warning when credits are at 2x the minimum threshold
    const warningThreshold = organization.minimumCreditsThreshold * 2;
    const hasCreditsWarning = organization.credits > 0 && organization.credits <= warningThreshold;
    const hasEmailSettings = !!organization.email && !!organization.fullName;

    // Check if we haven't sent a warning email recently (within last 24 hours)
    // This prevents spam - warning is sent only once per day when in warning state
    const lastSent = organization.lastWarningEmailSent;
    const canSendAgain = !lastSent || (Date.now() - new Date(lastSent).getTime()) > 24 * 60 * 60 * 1000;

    console.log(`Credit warning check for org ${organization._id}:`, {
      credits: organization.credits,
      minimumThreshold: organization.minimumCreditsThreshold,
      warningThreshold,
      hasCreditsWarning,
      hasEmailSettings,
      email: organization.email,
      fullName: organization.fullName,
      lastSent,
      canSendAgain,
      shouldSend: hasCreditsWarning && hasEmailSettings && canSendAgain
    });

    return hasCreditsWarning && hasEmailSettings && canSendAgain;
  }

  /**
   * Send credit runout notification
   */
  async sendCreditRunoutNotification(organization: Organization): Promise<boolean> {
    if (!this.shouldSendCreditRunoutNotification(organization)) {
      return false;
    }

    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    const fundingUrl = `${frontendUrl}/billing`;

    try {
      const success = await this.emailService.sendCreditRunoutNotification(
        organization.fullName,
        organization.email,
        organization.credits,
        fundingUrl
      );

      if (success) {
        // Update the last sent timestamp to prevent spam
        await this.organizationModel.findByIdAndUpdate(organization._id, {
          lastRunoutEmailSent: new Date()
        });
        console.log(`Credit runout notification sent to ${organization.fullName} (${organization.email})`);
      } else {
        console.error(`Failed to send credit runout notification to ${organization.fullName} (${organization.email})`);
      }

      return success;
    } catch (error) {
      console.error('Error sending credit runout notification:', error);
      return false;
    }
  }

  /**
   * Send credit warning notification
   */
  async sendCreditWarningNotification(organization: Organization): Promise<boolean> {
    if (!this.shouldSendCreditWarningNotification(organization)) {
      return false;
    }

    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    const fundingUrl = `${frontendUrl}/billing`;

    try {
      const success = await this.emailService.sendCreditWarningNotification(
        organization.fullName,
        organization.email,
        organization.credits,
        fundingUrl
      );

      if (success) {
        // Update the last sent timestamp to prevent spam
        await this.organizationModel.findByIdAndUpdate(organization._id, {
          lastWarningEmailSent: new Date()
        });
        console.log(`Credit warning notification sent to ${organization.fullName} (${organization.email})`);
      } else {
        console.error(`Failed to send credit warning notification to ${organization.fullName} (${organization.email})`);
      }

      return success;
    } catch (error) {
      console.error('Error sending credit warning notification:', error);
      return false;
    }
  }

  /**
   * Process credit notifications for an organization
   * This method should be called whenever credits are updated
   */
  async processCreditNotifications(organization: Organization): Promise<void> {
    try {
      // If credits are above warning threshold, reset warning email timestamp
      // This allows warning emails to be sent again if credits fall back into warning territory
      const warningThreshold = organization.minimumCreditsThreshold * 2;
      if (organization.credits > warningThreshold && organization.lastWarningEmailSent) {
        await this.organizationModel.findByIdAndUpdate(organization._id, {
          lastWarningEmailSent: null
        });
      }

      // If credits are above minimum threshold, reset runout email timestamp
      // This allows runout emails to be sent again if credits fall below threshold again
      const minimumThreshold = organization.minimumCreditsThreshold || 1.0;
      if (organization.credits > minimumThreshold && organization.lastRunoutEmailSent) {
        await this.organizationModel.findByIdAndUpdate(organization._id, {
          lastRunoutEmailSent: null
        });
      }

      // Check for runout notification first (higher priority)
      if (this.shouldSendCreditRunoutNotification(organization)) {
        await this.sendCreditRunoutNotification(organization);
      }
      // Only send warning if not sending runout notification
      else if (this.shouldSendCreditWarningNotification(organization)) {
        await this.sendCreditWarningNotification(organization);
      }
    } catch (error) {
      console.error('Error processing credit notifications:', error);
    }
  }

  /**
   * Get credit status for an organization
   */
  getCreditStatus(organization: Organization): {
    status: 'normal' | 'warning' | 'depleted';
    shouldNotify: boolean;
    message: string;
  } {
    const minimumThreshold = organization.minimumCreditsThreshold || 1.0;
    const warningThreshold = minimumThreshold * 2;

    if (organization.credits <= minimumThreshold) {
      return {
        status: 'depleted',
        shouldNotify: this.shouldSendCreditRunoutNotification(organization),
        message: 'Credits have reached minimum threshold. Service may be limited.'
      };
    }

    if (organization.credits <= warningThreshold) {
      return {
        status: 'warning',
        shouldNotify: this.shouldSendCreditWarningNotification(organization),
        message: `Credits are running low. Current balance: $${organization.credits.toFixed(2)}`
      };
    }

    return {
      status: 'normal',
      shouldNotify: false,
      message: `Credit balance is healthy: $${organization.credits.toFixed(2)}`
    };
  }
}
