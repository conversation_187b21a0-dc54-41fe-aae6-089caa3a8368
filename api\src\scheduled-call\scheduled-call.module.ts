import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ScheduledCallController } from "./scheduled-call.controller";
import { ScheduledCallService } from "./scheduled-call.service";
import {
  ScheduledCall,
  ScheduledCallSchema,
} from "./schemas/scheduled-call.schema";
import { VapiModule } from "src/vapi/vapi.module";
import { CallSchedulerService } from "src/call-scheduler/call-scheduler.service";
import { ContactSchema } from "src/contacts/schemas/contact.schema";
import { CampaignSchema } from "src/campaign/schemas/campaign.schema";
import { ContactsModule } from "src/contacts/contacts.module";
import { CampaignModule } from "src/campaign/campaign.module";
import { UsersModule } from "src/users/users.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ScheduledCall.name, schema: ScheduledCallSchema },
      { name: 'Contact', schema: ContactSchema },
      { name: 'Campaign', schema: CampaignSchema },
    ]),
    forwardRef(() => VapiModule),
    forwardRef(() => ContactsModule),
    forwardRef(() => CampaignModule),
    UsersModule,
  ],
  controllers: [ScheduledCallController],
  providers: [ScheduledCallService, CallSchedulerService],
  exports: [ScheduledCallService],
})
export class ScheduledCallModule {}
