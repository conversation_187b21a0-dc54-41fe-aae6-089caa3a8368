import { FullUserType } from '../auth/interceptors/user-redaction.interceptor';
import { CampaignService } from './campaign.service';
import { CampaignDto, UpdateCampaignStatusDto } from './dto/campaign.dto';
export declare class CampaignController {
    private readonly campaignService;
    constructor(campaignService: CampaignService);
    create(createCampaignDto: CampaignDto, user: FullUserType): Promise<import("./interfaces/campaign.interface").CampaignDocument>;
    findAll(status?: string, search?: string): Promise<import("./interfaces/campaign.interface").CampaignDocument[]>;
    findOne(id: string): Promise<import("./interfaces/campaign.interface").CampaignDocument>;
    update(id: string, updateCampaignDto: CampaignDto, user: FullUserType): Promise<import("./interfaces/campaign.interface").CampaignDocument>;
    updateStatus(id: string, statusDto: UpdateCampaignStatusDto): Promise<import("./interfaces/campaign.interface").CampaignDocument>;
    remove(id: string): Promise<void>;
    getMetrics(id: string): Promise<any>;
}
