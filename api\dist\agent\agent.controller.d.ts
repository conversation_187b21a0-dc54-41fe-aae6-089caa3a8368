import { AgentService } from './agent.service';
export declare class AgentController {
    private readonly agentsService;
    constructor(agentsService: AgentService);
    getUploadedFiles(): Promise<{
        files: string[];
        count: number;
    }>;
    getVolumeFiles(): Promise<{
        files: string[];
        count: number;
        error?: string;
        volumePath: string;
        isVolumeAccessible: boolean;
    }>;
    create(createAgentDto: any): Promise<any>;
    findAll(): Promise<any[]>;
    findById(id: string): Promise<any>;
    update(id: string, updateAgentDto: any): Promise<any>;
    updateStatus(id: string, status: string): Promise<any>;
    uploadFile(file: Express.Multer.File): Promise<{
        filename: string;
        path: string;
    }>;
    uploadAndSetAvatar(id: string, file: Express.Multer.File): Promise<any>;
    remove(id: string): Promise<void>;
}
