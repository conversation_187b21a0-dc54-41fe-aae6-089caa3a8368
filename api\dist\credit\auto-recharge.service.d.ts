import { UsersService } from '../users/users.service';
import { BillingService } from '../billing/billing.service';
import { OrganizationsService } from '../organizations/organizations.service';
export declare class AutoRechargeService {
    private readonly usersService;
    private readonly billingService;
    private readonly organizationsService;
    private readonly logger;
    constructor(usersService: UsersService, billingService: BillingService, organizationsService: OrganizationsService);
    checkAndProcessAutoRecharge(userId: string): Promise<boolean>;
}
