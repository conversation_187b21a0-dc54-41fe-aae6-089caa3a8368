
'use client';

import { useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { fetchContacts } from '@/app/api/contacts';

// This is a redirect page to maintain backward compatibility
// It will redirect to the new URL format that includes contactId
export default function LegacyEditContactPage() {
  const params = useParams();
  const contactName = params?.contactName as string;
  const router = useRouter();

  useEffect(() => {
    const redirectToNewFormat = async () => {
      try {
        // Fetch all contacts
        const contacts = await fetchContacts();

        // Find the first contact with the matching name
        const contact = contacts.find(
          (c) => c.contactName.toLowerCase() === decodeURIComponent(contactName).toLowerCase()
        );

        if (contact) {
          // Redirect to the new URL format with contactId
          router.push(`/contacts/edit/${encodeURIComponent(contact.contactName)}/${encodeURIComponent(contact._id)}`);
        } else {
          // If no contact found, redirect to contacts list
          router.push('/contacts');
        }
      } catch (error) {
        console.error('Error redirecting:', error);
        // On error, redirect to contacts list
        router.push('/contacts');
      }
    };

    redirectToNewFormat();
  }, [contactName, router]);

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">Redirecting...</h2>
        <p className="text-muted-foreground">Please wait while we redirect you to the contact edit page.</p>
      </div>
    </div>
  );
}
