import { Schema, Types } from 'mongoose';
export declare const OrganizationSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "suspended";
    credits: number;
    monthlyFreeCredits: number;
    monthlyFreeCreditsUsed: number;
    lastMonthlyReset: NativeDate;
    usingFreeCredits: boolean;
    monthlyResetDate: number;
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
    callPricePerMinute: number;
    minimumCreditsThreshold: number;
    monthlyMinutesAllowance: number;
    adminUsers: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    users: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    description?: string;
    email?: string;
    fullName?: string;
    stripeCustomerId?: string;
    lastWarningEmailSent?: NativeDate;
    lastRunoutEmailSent?: NativeDate;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "suspended";
    credits: number;
    monthlyFreeCredits: number;
    monthlyFreeCreditsUsed: number;
    lastMonthlyReset: NativeDate;
    usingFreeCredits: boolean;
    monthlyResetDate: number;
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
    callPricePerMinute: number;
    minimumCreditsThreshold: number;
    monthlyMinutesAllowance: number;
    adminUsers: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    users: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    description?: string;
    email?: string;
    fullName?: string;
    stripeCustomerId?: string;
    lastWarningEmailSent?: NativeDate;
    lastRunoutEmailSent?: NativeDate;
}>> & import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "suspended";
    credits: number;
    monthlyFreeCredits: number;
    monthlyFreeCreditsUsed: number;
    lastMonthlyReset: NativeDate;
    usingFreeCredits: boolean;
    monthlyResetDate: number;
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
    callPricePerMinute: number;
    minimumCreditsThreshold: number;
    monthlyMinutesAllowance: number;
    adminUsers: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    users: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    description?: string;
    email?: string;
    fullName?: string;
    stripeCustomerId?: string;
    lastWarningEmailSent?: NativeDate;
    lastRunoutEmailSent?: NativeDate;
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
