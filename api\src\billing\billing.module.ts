import { Module, forwardRef } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ConfigModule } from "@nestjs/config";
import { LoggerModule } from "src/logger/logger.module";
import { UsersModule } from "src/users/users.module";
import { OrganizationPaymentMethodSchema } from "./schemas/organization-payment-method.schema";
import { OrganizationTransactionSchema } from "./schemas/organization-transaction.schema";
import { BillingController } from "./billing.controller";
import { BillingService } from "./billing.service";
import { OrganizationsModule } from "src/organizations/organizations.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: "OrganizationPaymentMethod",
        schema: OrganizationPaymentMethodSchema,
      },
      {
        name: "OrganizationTransaction",
        schema: OrganizationTransactionSchema,
      },
    ]),
    ConfigModule,
    LoggerModule,
    forwardRef(() => UsersModule),
    forwardRef(() => OrganizationsModule),
  ],
  controllers: [BillingController],
  providers: [BillingService],
  exports: [BillingService],
})
export class BillingModule {}
