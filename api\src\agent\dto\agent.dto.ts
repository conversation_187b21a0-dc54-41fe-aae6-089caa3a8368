import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsNumber,
  ValidateNested,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';

// Updated Action DTO
export class ActionDto {
  @ApiProperty({ example: 'Send Confirmation SMS', description: 'Name of the action' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'sendSMS',
    enum: ['sendSMS', 'sendEmail', 'transferCall', 'endCall', 'extractInfo', 'calendar'],
    description: 'Type of action to perform',
  })
  @IsEnum(['sendSMS', 'sendEmail', 'transferCall', 'endCall', 'extractInfo', 'calendar'])
  type: 'sendSMS' | 'sendEmail' | 'transferCall' | 'endCall' | 'extractInfo' | 'calendar';

  @ApiProperty({
    example: 'both',
    enum: ['inbound', 'outbound', 'both'],
    default: 'both',
    description: 'Type of call this action applies to',
  })
  @IsEnum(['inbound', 'outbound', 'both'])
  callType: 'inbound' | 'outbound' | 'both';

  @ApiProperty({
    example: 'I want to talk to a real assistant',
    required: false,
    description: 'Trigger phrase or condition for the action',
  })
  @IsOptional()
  @IsString()
  trigger?: string;

  @ApiProperty({ example: '+**********', required: false, description: 'Phone number for SMS or call actions' })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({
    example: 'Hello, your appointment is confirmed!',
    required: false,
    description: 'Content of the SMS message',
  })
  @IsOptional()
  @IsString()
  smsContent?: string;
}

// Updated Model DTO (replacing the old LanguageModelDto)
export class ModelDto {
  @ApiProperty({ example: 'openai', description: 'Provider of the language model' })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({ example: 'gpt-4o-mini', description: 'Specific model identifier' })
  @IsString()
  @IsNotEmpty()
  model: string;

  @ApiProperty({
    example: 0.7,
    description: 'Temperature setting for the model (0.0 to 1.0)',
    default: 0.7,
  })
  @IsNumber()
  temperature: number;

  @ApiProperty({
    example: [{ role: 'system', content: 'Call script goes here' }],
    description: 'Array of message objects with role and content',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  messages: { role: string; content: string }[];

  @ApiProperty({ example: 300, required: false, description: 'Maximum tokens for the model' })
  @IsOptional()
  @IsNumber()
  maxTokens?: number;

  @ApiProperty({ example: 'd28775f5-604d-4399-b9f7-3c56ee956d4a', required: false, description: 'Knowledge base identifier' })
  @IsOptional()
  @IsString()
  knowledgeBaseId?: string;

  @ApiProperty({ example: true, description: 'Flag to enable emotion recognition' })
  @IsBoolean()
  emotionRecognitionEnabled: boolean;
}

// Updated Voice DTO (replacing the old VoiceSettingsDto)
export class VoiceDto {
  @ApiProperty({ example: 'eleven_turbo_v2_5', description: 'Voice model identifier' })
  @IsString()
  @IsNotEmpty()
  model: string;

  @ApiProperty({ example: 0.2, description: 'Voice style factor' })
  @IsNumber()
  style: number;

  @ApiProperty({ example: 'sfmzdbhtmFeQb5D4tEio', description: 'Unique voice identifier' })
  @IsString()
  voiceId: string;

  @ApiProperty({ example: '11labs', description: 'Voice provider' })
  @IsString()
  provider: string;

  @ApiProperty({ example: 0.5, description: 'Voice stability' })
  @IsNumber()
  stability: number;

  @ApiProperty({ example: 0.75, description: 'Voice similarity boost' })
  @IsNumber()
  similarityBoost: number;

  @ApiProperty({ example: true, description: 'Flag to use speaker boost' })
  @IsBoolean()
  useSpeakerBoost: boolean;

  @ApiProperty({ example: 15, description: 'Minimum number of characters for input' })
  @IsNumber()
  inputMinCharacters: number;

  @ApiProperty({
    example: [".", "،", "?", "!", ";", ",", "，", "۔"],
    description: 'Punctuation boundaries for input',
  })
  @IsArray()
  @IsString({ each: true })
  inputPunctuationBoundaries: string[];
}

// Updated Agent DTO
export class AgentDto {
  @ApiProperty({ example: 'd15e4f0e-9307-43c7-8dcf-9fc2e9de5dea', description: 'Unique agent identifier' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ example: 'c86e3479-4fce-42fd-920d-a5ec15b866a2', description: 'Organization identifier' })
  @IsString()
  @IsNotEmpty()
  orgId: string;

  @ApiProperty({ example: 'Lisa', description: 'Name of the agent' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: () => VoiceDto, description: 'Voice configuration for the agent' })
  @ValidateNested()
  @Type(() => VoiceDto)
  voice: VoiceDto;

  @ApiProperty({ example: '2025-03-12T14:25:46.595Z', description: 'Creation timestamp' })
  @IsString()
  createdAt: Date;

  @ApiProperty({ example: '2025-03-18T17:07:52.138Z', description: 'Last update timestamp' })
  @IsString()
  updatedAt: Date;

  @ApiProperty({ type: () => ModelDto, description: 'Language model configuration' })
  @ValidateNested()
  @Type(() => ModelDto)
  model: ModelDto;

  @ApiProperty({ example: true, description: 'Whether recording is enabled' })
  @IsBoolean()
  recordingEnabled: boolean;

  @ApiProperty({ example: 'Hey, is this {{customer.name}}?', description: 'First message of the agent' })
  @IsString()
  firstMessage: string;

  @ApiProperty({
    example: "Hey, this is Lisa from Binghatti Properties. Could you please call me back when you're free?",
    description: 'Voicemail message',
  })
  @IsString()
  voicemailMessage: string;

  @ApiProperty({ example: true, description: 'Flag to enable end call function' })
  @IsBoolean()
  endCallFunctionEnabled: boolean;

  @ApiProperty({ example: 'Thank you for contacting us. Have a great day!', description: 'End call message' })
  @IsString()
  endCallMessage: string;

  @ApiProperty({
    example: { model: 'nova-2-phonecall', language: 'en', numerals: false, provider: 'deepgram' },
    description: 'Transcriber configuration',
  })
  @IsOptional()
  transcriber: any; // Optionally, create a dedicated TranscriberDto

  @ApiProperty({
    example: ['function-call', 'metadata', 'transcript', 'transfer-update'],
    description: 'List of client messages',
  })
  @IsArray()
  @IsString({ each: true })
  clientMessages: string[];

  @ApiProperty({ example: ['end-of-call-report'], description: 'List of server messages' })
  @IsArray()
  @IsString({ each: true })
  serverMessages: string[];

  @ApiProperty({ example: 'https://dev.orova.ai/webhook/', description: 'Server URL for webhook callbacks' })
  @IsString()
  serverUrl: string;

  @ApiProperty({ example: ['goodbye'], description: 'List of phrases that trigger end call' })
  @IsArray()
  @IsString({ each: true })
  endCallPhrases: string[];

  @ApiProperty({ example: false, description: 'HIPAA compliance flag' })
  @IsBoolean()
  hipaaEnabled: boolean;

  @ApiProperty({ example: 5219, description: 'Maximum duration of the call in seconds' })
  @IsNumber()
  maxDurationSeconds: number;

  @ApiProperty({ example: 'office', description: 'Background sound setting' })
  @IsString()
  backgroundSound: string;

  @ApiProperty({ example: true, description: 'Flag for backchanneling' })
  @IsBoolean()
  backchannelingEnabled: boolean;

  @ApiProperty({
    example: {
      summaryPrompt: 'You are an expert note-taker. ...',
      structuredDataPrompt: 'Extract the following information from the call transcript: ...',
      structuredDataSchema: {},
      structuredDataRequestTimeoutSeconds: 10,
      successEvaluationPrompt: 'You are an expert call evaluator. ...',
      successEvaluationRubric: 'PassFail',
    },
    description: 'Analysis plan configuration',
  })
  @IsOptional()
  analysisPlan: any; // Optionally, create an AnalysisPlanDto

  @ApiProperty({ example: { provider: 'twilio' }, description: 'Voicemail detection configuration' })
  @IsOptional()
  voicemailDetection: any; // Optionally, create a VoicemailDetectionDto

  @ApiProperty({ example: true, description: 'Background denoising flag' })
  @IsBoolean()
  backgroundDenoisingEnabled: boolean;

  @ApiProperty({
    example: { idleMessages: ['Are you still there?'], idleMessageMaxSpokenCount: 9, idleTimeoutSeconds: 9 },
    description: 'Message plan configuration',
  })
  @IsOptional()
  messagePlan: any; // Optionally, create a MessagePlanDto

  @ApiProperty({ example: { waitSeconds: 0.3 }, description: 'Start speaking plan configuration' })
  @IsOptional()
  startSpeakingPlan: any; // Optionally, create a StartSpeakingPlanDto

  @ApiProperty({ example: { numWords: 2 }, description: 'Stop speaking plan configuration' })
  @IsOptional()
  stopSpeakingPlan: any; // Optionally, create a StopSpeakingPlanDto

  @ApiProperty({
    example: { hipaaEnabled: false, pciEnabled: false },
    description: 'Compliance plan configuration',
  })
  @IsOptional()
  compliancePlan: any; // Optionally, create a CompliancePlanDto

  @ApiProperty({ example: true, description: 'Flag to indicate if the server URL secret is set' })
  @IsBoolean()
  isServerUrlSecretSet: boolean;

  @ApiProperty({
    type: () => [ActionDto],
    required: false,
    description: 'List of actions the agent can perform',
    default: [],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ActionDto)
  actions?: ActionDto[];
}
