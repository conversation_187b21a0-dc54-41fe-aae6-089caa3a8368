/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader } from "@/components/ui/card";
import { ArrowLeft, Loader2, Save, Calendar, AlertCircle, Trash2 } from "lucide-react";
import { getCampaign, updateCampaign, rescheduleCampaignCalls, removeDuplicateCalls } from "@/app/api/campaign";
import Link from "next/link";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import SourcesStep from "../../create/steps/SourcesStep";
import AgentsStep from "../../create/steps/AgentsStep";
import ScheduleStep from "../../create/steps/ScheduleStep";
import SettingsStep, { DayOfWeek } from "../../create/steps/SettingsSteps";
import { CampaignContact, CampaignData } from "@/types/campaign.types";


// Campaign data state (same as in CreateCampaign)

interface EditCampaignContentProps {
  id: string;
  campaignId?: string;
}

export default function EditCampaign({ campaignId: propCampaignId }: EditCampaignContentProps) {
  const params = useParams();
  const router = useRouter();
  const campaignId = propCampaignId || (params.id as string);

  const [activeTab, setActiveTab] = useState("sources");
  const [loading, setLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isRescheduling, setIsRescheduling] = useState(false);
  const [isRemovingDuplicates, setIsRemovingDuplicates] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [rescheduleDialogOpen, setRescheduleDialogOpen] = useState(false);
  const [removeDuplicatesDialogOpen, setRemoveDuplicatesDialogOpen] = useState(false);
  const [rescheduleSuccessDialogOpen, setRescheduleSuccessDialogOpen] = useState(false);
  const [removeDuplicatesSuccessDialogOpen, setRemoveDuplicatesSuccessDialogOpen] = useState(false);
  const [rescheduleResult, setRescheduleResult] = useState<{
    rescheduledCount: number;
    duplicatesRemoved: number;
    totalCount: number;
  }>({ rescheduledCount: 0, duplicatesRemoved: 0, totalCount: 0 });
  const [duplicatesResult, setDuplicatesResult] = useState<{
    duplicatesRemoved: number;
  }>({ duplicatesRemoved: 0 });
  const [userRole, setUserRole] = useState<string | null>(null);

  // Campaign data state
  const [campaignData, setCampaignData] = useState<CampaignData>({
    // Default values (same as in CreateCampaign)
    name: "",
    concurrentCalls: 1,
    dailyCost: 0,
    startDate: "",
    endDate: "",
    successRate: 0,
    instantCall: false,
    batchIntervalMinutes: 3,
    sentiment: "neutral",
    status: "inactive",
    sources: [],
    sourceType: "contacts",
    agents: [],
    callSchedule: {
      startTime: "09:00",
      endTime: "17:00",
      timezone: "America/New_York",
      daysOfWeek: ["monday", "tuesday", "wednesday", "thursday", "friday"]
    },
    callWindow: {
      startTime: "09:00",
      endTime: "17:00",
      daysOfWeek: ["monday", "tuesday", "wednesday", "thursday", "friday"] as DayOfWeek[]
    },
    recallHours: 24,
    maxRecalls: 3,
    followUpDays: ["monday", "tuesday", "wednesday", "thursday", "friday"] as DayOfWeek[],
  });


  const fetchCampaignData = async () => {
    try {
      const campaign = await getCampaign(campaignId);

      // Transform API data to match our UI model
      setCampaignData(prev => {
        const campaignContacts = (campaign.contacts || []) as CampaignContact[];

         // Find matching contacts in our sources list by name and phone number
      const selectedSources = prev.sources.filter(contact =>
        campaignContacts.some(c =>
          c.contactName === contact.contactName &&
          c.phoneNumber === contact.phoneNumber
        )
      );

      // If we couldn't find matches, use the campaign contacts directly
      const sourcesToUse = selectedSources.length > 0
        ? selectedSources
        : campaignContacts.map(c => ({
            _id: c.contactId || '',
            contactName: c.contactName || '',
            phoneNumber: c.phoneNumber || '',
            campaigns: [], 
            createdAt: new Date(),  // Add required fields
            source: 'campaign',
          }));

        // Keep the agents array from prev state
        return {
          ...prev,
          name: campaign.name,
          concurrentCalls: campaign.concurrentCalls,
          dailyCost: campaign.dailyCost || 0,
          startDate: campaign.startDate,
          endDate: campaign.endDate || null,
          instantCall: campaign.instantCall || false,
          batchIntervalMinutes: campaign.batchIntervalMinutes || 3,
          successRate: campaign.successRate || 0,
          sentiment: campaign.sentiment || "neutral",
          status: campaign.status,
          sources: sourcesToUse,
          sourceType: "contacts",
          // Keep the full agents array from prev state
          agents: prev.agents,
          // Set the campaign's agent ID
          agentId: campaign.agentId,
          callSchedule: campaign.callSchedule ? {
            ...campaign.callSchedule,
            daysOfWeek: campaign.callSchedule.daysOfWeek as DayOfWeek[]
          } : prev.callSchedule,
          callWindow: campaign.callWindow ? {
            ...campaign.callWindow,
            daysOfWeek: campaign.callWindow.daysOfWeek as DayOfWeek[]
          } : prev.callWindow,
          recallHours: campaign.recallHours || 24,
          maxRecalls: campaign.maxRecalls || 3,
          followUpDays: campaign.followUpDays as DayOfWeek[] || prev.followUpDays,
        };
      });

    } catch (error) {
      console.error("Error fetching campaign:", error);

    }
  };




  const fetchUserProfile = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token available");
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      const userData = await response.json();
      setUserRole(userData.role);
    } catch (err) {
      console.error("Failed to load user profile:", err);
    }
  };

  // Fetch agents
  const fetchAgents = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) throw new Error('Failed to fetch agents');

      const agents = await response.json();
      setCampaignData(prev => ({ ...prev, agents }));
    } catch (error) {
      console.error('Error loading agents:', error);
    }
  };

  useEffect(() => {
    const loadCampaignData = async () => {
      setLoading(true);
      try {
        // First fetch agents and contacts
        await Promise.all([
          fetchUserProfile(),
          fetchAgents(),
        ]);


        // Then fetch campaign data
        await fetchCampaignData();

      } catch (error) {
        console.error("Error loading campaign data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadCampaignData();
  }, [campaignId]);

  // Update campaign data from any tab
  const updateCampaignData = (data: Partial<CampaignData>) => {
    setCampaignData(prev => ({ ...prev, ...data }));
  };

  // Handle campaign update
  const handleUpdateCampaign = async () => {
    setIsSaving(true);
    try {
      // Transform data if needed before sending to API
      const formattedData = {
        ...campaignData,
        contacts: campaignData.sources.map(contact => ({
          contactId: contact._id || undefined,
          contactName: contact.contactName || '',
          phoneNumber: contact.phoneNumber || '',
        })).filter(c => c.contactName || c.phoneNumber),
        endDate: campaignData.endDate || null,

      };

      await updateCampaign(campaignId, formattedData);
      setSuccessDialogOpen(true);
    } catch (err) {
      console.error('Error updating campaign:', err);

    } finally {
      setIsSaving(false);
    }
  };

  // Handle campaign calls rescheduling
  const handleRescheduleCalls = async () => {
    setIsRescheduling(true);
    try {
      // Prepare data for rescheduling
      const rescheduleData = {
        agentId: campaignData.agentId || '',
        concurrentCalls: campaignData.concurrentCalls,
        batchIntervalMinutes: campaignData.batchIntervalMinutes || 3,
        callWindow: {
          startTime: campaignData.callWindow.startTime,
          endTime: campaignData.callWindow.endTime,
          daysOfWeek: campaignData.callWindow.daysOfWeek.map(day => day.toString())
        }
      };

      const result = await rescheduleCampaignCalls(rescheduleData);
      setRescheduleResult(result);
      setRescheduleSuccessDialogOpen(true);
    } catch (err) {
      console.error('Error rescheduling campaign calls:', err);
    } finally {
      setIsRescheduling(false);
      setRescheduleDialogOpen(false);
    }
  };

  // Handle removing duplicate calls
  const handleRemoveDuplicates = async () => {
    setIsRemovingDuplicates(true);
    try {
      // Prepare data for removing duplicates
      const data = {
        agentId: campaignData.agentId || ''
      };

      const result = await removeDuplicateCalls(data);
      setDuplicatesResult(result);
      setRemoveDuplicatesSuccessDialogOpen(true);
    } catch (err) {
      console.error('Error removing duplicate calls:', err);
    } finally {
      setIsRemovingDuplicates(false);
      setRemoveDuplicatesDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="container max-w-4xl mx-auto py-6">
        <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl mx-auto py-6">
      <div className="mb-6">
        <Link href="/campaign" className="text-sm text-muted-foreground hover:text-primary flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Campaigns
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardDescription>
            Update your campaign settings
          </CardDescription>
        </CardHeader>

        <CardContent className="p-0 sm:p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid grid-cols-4 mb-8 w-full h-auto">
                <TabsTrigger
                    value="sources"
                    className="py-3 text-sm md:text-base font-medium"
                >
                    Sources
                </TabsTrigger>
                <TabsTrigger
                    value="agents"
                    className="py-3 text-sm md:text-base font-medium"
                >
                    Agent
                </TabsTrigger>
                <TabsTrigger
                    value="schedule"
                    className="py-3 text-sm md:text-base font-medium"
                >
                    Schedule
                </TabsTrigger>
                <TabsTrigger
                    value="settings"
                    className="py-3 text-sm md:text-base font-medium"
                >
                    Settings
                </TabsTrigger>
                </TabsList>

                <div className="px-4 sm:px-0">
                <TabsContent value="sources" className="mt-6">
                    <SourcesStep data={campaignData} updateData={updateCampaignData} />
                </TabsContent>

                <TabsContent value="agents" className="mt-6">
                    <AgentsStep data={campaignData} updateData={updateCampaignData} loading={loading}  userRole={userRole} />
                </TabsContent>

                <TabsContent value="schedule" className="mt-6">
                    <ScheduleStep data={campaignData} updateData={(newData) => updateCampaignData(newData as Partial<CampaignData>)} />
                </TabsContent>

                <TabsContent value="settings" className="mt-6">
                    <SettingsStep data={campaignData} updateData={updateCampaignData} />
                </TabsContent>
                </div>
            </Tabs>
            </CardContent>

        <CardFooter className="flex justify-between">
          <div className="flex gap-2">
            {(userRole === 'admin' || userRole === 'superadmin') && (
              <Button
                variant="outline"
                onClick={() => setRemoveDuplicatesDialogOpen(true)}
                disabled={isRemovingDuplicates || !campaignData.agentId}
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                {isRemovingDuplicates ? "Removing..." : "Remove Duplicates"}
              </Button>
            )}
            {userRole === 'superadmin' && (
              <Button
                variant="outline"
                onClick={() => setRescheduleDialogOpen(true)}
                disabled={isRescheduling || !campaignData.agentId}
                className="flex items-center gap-2"
              >
                <Calendar className="h-4 w-4" />
                {isRescheduling ? "Rescheduling..." : "Reschedule Calls"}
              </Button>
            )}
          </div>
          <Button
            onClick={handleUpdateCampaign}
            disabled={isSaving}
          >
            {isSaving ? "Saving..." : "Save Changes"}
            {!isSaving && <Save className="ml-2 h-4 w-4" />}
          </Button>
        </CardFooter>
      </Card>

      {/* Success Dialog */}
      <Dialog open={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Campaign Updated</DialogTitle>
            <DialogDescription>
              Your campaign &quot;{campaignData.name}&quot; has been successfully updated.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => router.push('/campaign')}>
              Return to Campaigns
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reschedule Confirmation Dialog */}
      <Dialog open={rescheduleDialogOpen} onOpenChange={setRescheduleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reschedule Campaign Calls</DialogTitle>
            <DialogDescription>
              This will reschedule all pending calls for campaign &quot;{campaignData.name}&quot; using the current campaign settings.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="default" className="mb-4">
              <AlertCircle color="orange" className="h-4 w-4" />
              <AlertTitle className="text-orange-400 mb-2" >Warning</AlertTitle>
              <AlertDescription>
                This action will reschedule all pending calls for this campaign. The calls will be rescheduled starting from the current time + 5 minutes, based on the campaign&apos;s current settings:
                <ul className="list-disc pl-5 mt-2 space-y-1">
                  <li>Starting time: Current time + 5 minutes</li>
                  <li>Concurrent calls: {campaignData.concurrentCalls}</li>
                  <li>Batch interval: {campaignData.batchIntervalMinutes || 3} minutes</li>
                  <li>Call window: {campaignData.callWindow.startTime} - {campaignData.callWindow.endTime}</li>
                  <li>Days of week: {campaignData.callWindow.daysOfWeek.join(', ')}</li>
                </ul>
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setRescheduleDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleRescheduleCalls}
              disabled={isRescheduling}
            >
              {isRescheduling ? "Rescheduling..." : "Reschedule Calls"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

        {/* Reschedule Success Dialog */}
      <Dialog open={rescheduleSuccessDialogOpen} onOpenChange={setRescheduleSuccessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Calls Rescheduled</DialogTitle>
            <DialogDescription>
              Successfully processed calls for campaign &quot;{campaignData.name}&quot;:
               <ul className="list-disc pl-5 space-y-1 mt-2">
                <li>{rescheduleResult.rescheduledCount} calls rescheduled</li>
                <li>{rescheduleResult.duplicatesRemoved} duplicate calls removed</li>
                <li>{rescheduleResult.totalCount} total calls processed</li>
              </ul>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setRescheduleSuccessDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Duplicates Confirmation Dialog */}
      <Dialog open={removeDuplicatesDialogOpen} onOpenChange={setRemoveDuplicatesDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Duplicate Calls</DialogTitle>
            <DialogDescription>
              This will check for and remove any duplicate pending calls for campaign &quot;{campaignData.name}&quot;.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="default" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                This action will scan all pending calls for this campaign and remove any duplicates, keeping only the most recent call for each contact.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setRemoveDuplicatesDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleRemoveDuplicates}
              disabled={isRemovingDuplicates}
            >
              {isRemovingDuplicates ? "Removing..." : "Remove Duplicates"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Duplicates Success Dialog */}
      <Dialog open={removeDuplicatesSuccessDialogOpen} onOpenChange={setRemoveDuplicatesSuccessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Duplicates Removed</DialogTitle>
            <DialogDescription>
              Successfully processed duplicate calls for campaign &quot;{campaignData.name}&quot;:
            </DialogDescription>
            <div className="mt-2">
              <ul className="list-disc pl-5 space-y-1">
                <li>{duplicatesResult.duplicatesRemoved} duplicate calls removed</li>
              </ul>
            </div>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setRemoveDuplicatesSuccessDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}