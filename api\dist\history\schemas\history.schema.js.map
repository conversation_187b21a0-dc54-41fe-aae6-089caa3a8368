{"version": 3, "file": "history.schema.js", "sourceRoot": "", "sources": ["../../../src/history/schemas/history.schema.ts"], "names": [], "mappings": ";;;AAAA,uCAAkC;AAErB,QAAA,aAAa,GAAG,IAAI,iBAAM,CAAC;IACtC,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC1C,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC9C,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC1B,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;IACzC,cAAc,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAChC,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC7B,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;IAC7B,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;IAC3B,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC9B,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;IAC1C,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;IAC5C,aAAa,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC/B,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC1B,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;IAC7C,eAAe,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;IAChD,mBAAmB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;IACpD,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC9B,gBAAgB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAClC,iBAAiB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACnC,iBAAiB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACnC,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC7B,cAAc,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAChC,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACxB,aAAa,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;IAChC,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,UAAU;YACV,SAAS;YACT,mBAAmB;YACnB,mBAAmB;YACnB,UAAU;SACX;QACD,OAAO,EAAE,SAAS;KACnB;IACD,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAGvB,aAAa,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC/B,gBAAgB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAClC,eAAe,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACjC,sBAAsB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACxC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC3B,oBAAoB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACtC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC3B,cAAc,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAChC,uBAAuB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACzC,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC1B,aAAa,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC/B,oBAAoB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACtC,eAAe,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACjC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC3B,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC9B,mBAAmB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACrC,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC7B,UAAU,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC5B,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC9B,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC1B,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IACzB,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAC9B,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;IAEvB,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;IAC5C,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;CAC7C,EAAE;IACD,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC1B,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC5B,EAAE,EAAE,KAAK;CACV,CAAC,CAAC;AAGH,qBAAa,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,IAAI;IACtC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAGH,qBAAa,CAAC,OAAO,CAAC,aAAa,GAAG,KAAK;IACzC,OAAO,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AACrC,CAAC,CAAC;AAEF,qBAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;IACtC,OAAO,IAAI,CAAC,WAAW,CAAC;AAC1B,CAAC,CAAC,CAAC;AAEH,qBAAa,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC;IACzC,OAAO,IAAI,CAAC,cAAc,CAAC;AAC7B,CAAC,CAAC,CAAC;AAEH,qBAAa,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1C,qBAAa,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAClC,qBAAa,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC"}