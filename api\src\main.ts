import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';


async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    cors: true,
    // Enable WebSockets
    logger: ['error', 'warn', 'log', 'debug', 'verbose']
  });
  const configService = app.get(ConfigService);

  app.use(bodyParser.json({ limit: '10mb' }));
  app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

   // Define upload directory consistently
  const distUploadDir = join(__dirname, '../uploads');
  const rootUploadDir = join(__dirname, '../../uploads');
   // Ensure the directory exists
   if (!existsSync(distUploadDir)) {
    mkdirSync(distUploadDir, { recursive: true });
  }

  if (!existsSync(rootUploadDir)) {
    mkdirSync(rootUploadDir, { recursive: true });
  }


   // Serve static files from the 'uploads' directory
   app.useStaticAssets(distUploadDir, {
    prefix: '/api/uploads/',
  });

  app.useStaticAssets(rootUploadDir, {
    prefix: '/api/uploads/',
  });


  app.setGlobalPrefix('api');
  const swaggerConfig = new DocumentBuilder()
    .setTitle('My API')
    .setDescription('API documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api/docs', app, document);

  const port = configService.get<number>('PORT') || 5000;
  await app.listen(port);
}

bootstrap();
