import { Schema, Types } from "mongoose";
export declare const ContactSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
    id: false;
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    source: "manual" | "file Upload" | "CRM" | "-";
    contactName: string;
    phoneNumber: string;
    campaigns: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    unansweredCalls: number;
    campaignNames: string[];
    region?: string;
    lastCall?: NativeDate;
    customerId?: string;
    projectName?: string;
    unitNumber?: string;
    totalPayableAmount?: number;
    pendingPayableAmount?: number;
    dueDate?: NativeDate;
    totalInstallments?: number;
    paymentType?: string;
    pendingInstallments?: number;
    lastPaymentDate?: NativeDate;
    lastPaymentAmount?: number;
    lastPaymentType?: string;
    collectionBucket?: string;
    unitPrice?: number;
    paidAmtIncluding?: number;
    eventDate?: string;
    eventLocation?: string;
    eventTime?: string;
    nameOfRegistrant?: string;
    addedBy?: string;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    source: "manual" | "file Upload" | "CRM" | "-";
    contactName: string;
    phoneNumber: string;
    campaigns: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    unansweredCalls: number;
    campaignNames: string[];
    region?: string;
    lastCall?: NativeDate;
    customerId?: string;
    projectName?: string;
    unitNumber?: string;
    totalPayableAmount?: number;
    pendingPayableAmount?: number;
    dueDate?: NativeDate;
    totalInstallments?: number;
    paymentType?: string;
    pendingInstallments?: number;
    lastPaymentDate?: NativeDate;
    lastPaymentAmount?: number;
    lastPaymentType?: string;
    collectionBucket?: string;
    unitPrice?: number;
    paidAmtIncluding?: number;
    eventDate?: string;
    eventLocation?: string;
    eventTime?: string;
    nameOfRegistrant?: string;
    addedBy?: string;
}>> & import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    source: "manual" | "file Upload" | "CRM" | "-";
    contactName: string;
    phoneNumber: string;
    campaigns: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    }[];
    unansweredCalls: number;
    campaignNames: string[];
    region?: string;
    lastCall?: NativeDate;
    customerId?: string;
    projectName?: string;
    unitNumber?: string;
    totalPayableAmount?: number;
    pendingPayableAmount?: number;
    dueDate?: NativeDate;
    totalInstallments?: number;
    paymentType?: string;
    pendingInstallments?: number;
    lastPaymentDate?: NativeDate;
    lastPaymentAmount?: number;
    lastPaymentType?: string;
    collectionBucket?: string;
    unitPrice?: number;
    paidAmtIncluding?: number;
    eventDate?: string;
    eventLocation?: string;
    eventTime?: string;
    nameOfRegistrant?: string;
    addedBy?: string;
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
