import { Schema, Types } from 'mongoose';
import { ROLES } from '../enums/role.enum';

export const UserSchema = new Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  fullName: { type: String, required: true },
  role: { type: String, enum: ROLES, default: 'user' },
  isApproved: { type: Boolean, default: false },
  // Organization that this user belongs to
  organizationId: { type: Types.ObjectId, ref: 'Organization' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});