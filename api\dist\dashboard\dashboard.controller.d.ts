import { DashboardService } from './dashboard.service';
export declare class DashboardController {
    private readonly dashboardService;
    constructor(dashboardService: DashboardService);
    getDashboardMetrics(timeRange?: string, agentType?: string): Promise<(import("mongoose").Document<unknown, {}, import("./schemas/dashboard.schema").DashboardStats> & import("./schemas/dashboard.schema").DashboardStats & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }) | {
        callMetrics: {
            totalCalls: number;
            totalMinutes: number;
            averageLength: number;
            connectionRate: number;
            answerRate: number;
        };
        totalCounts: {
            totalCampaigns: number;
            totalScheduledCalls: number;
            totalContacts: number;
            totalCalls: number;
        };
        sentiments: {
            positive: number;
            neutral: number;
            negative: number;
        };
        callEndReasons: {
            reason: string;
            count: number;
            percentage: number;
        }[];
        agentRoles: string[];
        topAgents: {
            id: string;
            name: string;
            avatar: string;
            role: string;
            status: string;
            callCount: number;
        }[];
        recentCalls: (import("mongoose").Document<unknown, {}, import("../history/interfaces/history.interface").History> & import("../history/interfaces/history.interface").History & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        recentSchedules: (import("mongoose").Document<unknown, {}, import("../scheduled-call/schemas/scheduled-call.schema").ScheduledCall> & import("../scheduled-call/schemas/scheduled-call.schema").ScheduledCall & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        })[];
        recentCampaigns: (import("mongoose").Document<unknown, {}, import("../campaign/interfaces/campaign.interface").Campaign> & import("../campaign/interfaces/campaign.interface").Campaign & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        })[];
    }>;
    refreshStats(): Promise<void>;
}
