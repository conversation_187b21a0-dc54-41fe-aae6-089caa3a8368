"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduledCallModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const scheduled_call_controller_1 = require("./scheduled-call.controller");
const scheduled_call_service_1 = require("./scheduled-call.service");
const scheduled_call_schema_1 = require("./schemas/scheduled-call.schema");
const vapi_module_1 = require("../vapi/vapi.module");
const call_scheduler_service_1 = require("../call-scheduler/call-scheduler.service");
const contact_schema_1 = require("../contacts/schemas/contact.schema");
const campaign_schema_1 = require("../campaign/schemas/campaign.schema");
const contacts_module_1 = require("../contacts/contacts.module");
const campaign_module_1 = require("../campaign/campaign.module");
const users_module_1 = require("../users/users.module");
let ScheduledCallModule = class ScheduledCallModule {
};
exports.ScheduledCallModule = ScheduledCallModule;
exports.ScheduledCallModule = ScheduledCallModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: scheduled_call_schema_1.ScheduledCall.name, schema: scheduled_call_schema_1.ScheduledCallSchema },
                { name: 'Contact', schema: contact_schema_1.ContactSchema },
                { name: 'Campaign', schema: campaign_schema_1.CampaignSchema },
            ]),
            (0, common_1.forwardRef)(() => vapi_module_1.VapiModule),
            (0, common_1.forwardRef)(() => contacts_module_1.ContactsModule),
            (0, common_1.forwardRef)(() => campaign_module_1.CampaignModule),
            users_module_1.UsersModule,
        ],
        controllers: [scheduled_call_controller_1.ScheduledCallController],
        providers: [scheduled_call_service_1.ScheduledCallService, call_scheduler_service_1.CallSchedulerService],
        exports: [scheduled_call_service_1.ScheduledCallService],
    })
], ScheduledCallModule);
//# sourceMappingURL=scheduled-call.module.js.map