import { Model } from "mongoose";
import { ScheduledCallService } from "src/scheduled-call/scheduled-call.service";
import { VapiService } from "src/vapi/vapi.service";
import { ContactDocument } from "src/contacts/interfaces/contact.interface";
import { CampaignDocument } from "src/campaign/interfaces/campaign.interface";
import { UsersService } from "src/users/users.service";
export declare class CallSchedulerService {
    private readonly scheduledCallService;
    private readonly vapiService;
    private readonly usersService;
    private readonly contactModel;
    private readonly campaignModel;
    private readonly logger;
    private recentlyProcessedContacts;
    private isProcessingCalls;
    constructor(scheduledCallService: ScheduledCallService, vapiService: VapiService, usersService: UsersService, contactModel: Model<ContactDocument>, campaignModel: Model<CampaignDocument>);
    private checkCampaignStatus;
    private cleanupRecentlyProcessedContacts;
    recoverStuckCalls(): Promise<void>;
    handleScheduledCalls(): Promise<void>;
}
