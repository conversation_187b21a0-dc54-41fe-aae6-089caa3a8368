import { Model } from 'mongoose';
import { Campaign } from 'src/campaign/interfaces/campaign.interface';
import { ScheduledCall } from 'src/scheduled-call/schemas/scheduled-call.schema';
import { Agent } from 'src/agent/interfaces/agent.interface';
import { History } from 'src/history/interfaces/history.interface';
import { Contact } from 'src/contacts/interfaces/contact.interface';
import { DashboardStats } from './schemas/dashboard.schema';
export declare class DashboardService {
    private readonly historyModel;
    private readonly scheduledCallModel;
    private readonly campaignModel;
    private readonly agentModel;
    private readonly contactModel;
    private readonly dashboardStatsModel;
    private readonly logger;
    constructor(historyModel: Model<History>, scheduledCallModel: Model<ScheduledCall>, campaignModel: Model<Campaign>, agentModel: Model<Agent>, contactModel: Model<Contact>, dashboardStatsModel: Model<DashboardStats>);
    updateStats(): Promise<void>;
    getLatestStats(timeRange: string, agentType: string): Promise<import("mongoose").Document<unknown, {}, DashboardStats> & DashboardStats & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    private updateStatsForCombination;
    clearCachedStats(): Promise<void>;
    getDashboardMetrics(timeRange: string, agentType: string, fromCache?: boolean): Promise<(import("mongoose").Document<unknown, {}, DashboardStats> & DashboardStats & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }) | {
        callMetrics: {
            totalCalls: number;
            totalMinutes: number;
            averageLength: number;
            connectionRate: number;
            answerRate: number;
        };
        totalCounts: {
            totalCampaigns: number;
            totalScheduledCalls: number;
            totalContacts: number;
            totalCalls: number;
        };
        sentiments: {
            positive: number;
            neutral: number;
            negative: number;
        };
        callEndReasons: {
            reason: string;
            count: number;
            percentage: number;
        }[];
        agentRoles: string[];
        topAgents: {
            id: string;
            name: string;
            avatar: string;
            role: string;
            status: string;
            callCount: number;
        }[];
        recentCalls: (import("mongoose").Document<unknown, {}, History> & History & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        recentSchedules: (import("mongoose").Document<unknown, {}, ScheduledCall> & ScheduledCall & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        })[];
        recentCampaigns: (import("mongoose").Document<unknown, {}, Campaign> & Campaign & {
            _id: import("mongoose").Types.ObjectId;
        } & {
            __v: number;
        })[];
    }>;
    getAllAgentRoles(): Promise<string[]>;
    private calculateTotalCounts;
    private getDateFilter;
    private calculateCallMetrics;
    private calculateCallEndReasons;
    private calculateTopAgents;
    private calculateSentiments;
}
