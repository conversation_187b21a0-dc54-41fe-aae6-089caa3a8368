
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export type StatusType = "active" | "inactive" | "paused";
export type DayOfWeek = "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday";

export interface SettingsStepProps {
  data: {
    startDate?: string; // Make optional
    endDate?: string | null; // Make optional
    concurrentCalls?: number; // Make optional
    recallHours?: number;
    maxRecalls?: number;
    followUpDays?: DayOfWeek[];
    status?: string; // Make optional
    instantCall?: boolean;
    batchIntervalMinutes?: number;
    callSchedule?: { // Make entire callSchedule optional
      startTime?: string;
      endTime?: string;
      timezone?: string;
      daysOfWeek?: string[];
      callTime?: string;
    };
    callWindow?: { // Make entire callWindow optional
      startTime?: string;
      endTime?: string;
      daysOfWeek?: string[];
    };
  };
  updateData: (newData: Partial<{
    startDate?: string;
    endDate?: string;
    concurrentCalls?: number;
    recallHours?: number;
    maxRecalls?: number;
    followUpDays?: DayOfWeek[];
    instantCall?: boolean;
    batchIntervalMinutes?: number;
    status?: StatusType;
    callSchedule?: {
      startTime: string;
      endTime: string;
      timezone: string;
      daysOfWeek: DayOfWeek[];
      callTime?: string;
    };
  }>) => void;
}

export default function SettingsStep({ data, updateData }: SettingsStepProps) {

  // Default values if not provided
  const recallHours = data.recallHours || 24;
  const maxRecalls = data.maxRecalls || 3;
  const concurrentCalls = data.concurrentCalls || 10;
  const instantCall = data.instantCall || false;
  const batchIntervalMinutes = data.batchIntervalMinutes || 3;


  // Handle concurrent calls change
  const handleConcurrentCallsChange = (value: number[]) => {
    updateData({ concurrentCalls: value[0] });
  };

  // Handle recall hours change
  const handleRecallHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      updateData({ recallHours: value });
    }
  };

  // Handle max recalls change
  const handleMaxRecallsChange = (value: string) => {
    updateData({ maxRecalls: parseInt(value) });
  };

   // Handle instant call change
   const handleInstantCallChange = (value: string) => {
    updateData({ instantCall: value === "yes" });
  };

  // Handle batch interval change
  const handleBatchIntervalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      updateData({ batchIntervalMinutes: value });
    }
  };

  return (
    <div className="space-y-10 ">
      {/* Call Settings */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Call Settings</Label>
        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between mb-2">
              <Label htmlFor="concurrent-calls">Concurrent Calls</Label>
              <span className="text-sm text-muted-foreground">{concurrentCalls}</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
            Set the maximum number of simultaneous calls for this campaign
          </p>
            <Slider
              id="concurrent-calls"
              min={10}
              max={100}
              step={10}
              value={[concurrentCalls]}
              onValueChange={handleConcurrentCallsChange}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
            <span>10</span>
            <span>20</span>
            <span>30</span>
            <span>40</span>
            <span>50</span>
            <span>60</span>
            <span>70</span>
            <span>80</span>
            <span>90</span>
            <span>100</span>
          </div>
          <div className="text-center font-medium">
            {data.concurrentCalls} {data.concurrentCalls === 1 ? 'call' : 'calls'} at a time
          </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="batch-interval">Batch Interval (minutes)</Label>
            <p className="text-sm text-muted-foreground mb-4">
              Set the time interval between batches of scheduled calls
            </p>
            <div className="flex items-center space-x-2 w-32">
              <Input
                id="batch-interval"
                type="number"
                min="1"
                max="60"
                value={batchIntervalMinutes}
                onChange={handleBatchIntervalChange}
                className="text-center"
              />
              <span className="text-sm text-muted-foreground">minutes</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recall Settings */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Recall Settings</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2 flex flex-col items-center text-center">
            <Label htmlFor="recall-hours">Hours Between Recalls</Label>
            <div className="flex items-center space-x-2 w-32">
              <Input
                id="recall-hours"
                type="number"
                min="1"
                value={recallHours}
                onChange={handleRecallHoursChange}
                className="text-center"
              />
              <span className="text-sm text-muted-foreground">hours</span>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Time to wait before attempting to recall unanswered contacts
            </p>
          </div>
          <div className="space-y-2 flex flex-col items-center text-center">
            <Label htmlFor="max-recalls">Maximum Recall Attempts</Label>
            <Select
              value={maxRecalls.toString()}
              onValueChange={handleMaxRecallsChange}

            >
              <SelectTrigger id="max-recalls" className="text-center">
                <SelectValue placeholder="Select maximum recalls" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 Attempt</SelectItem>
                <SelectItem value="2">2 Attempts</SelectItem>
                <SelectItem value="3">3 Attempts</SelectItem>
                <SelectItem value="4">4 Attempts</SelectItem>
                <SelectItem value="5">5 Attempts</SelectItem>
                <SelectItem value="6">6 Attempts</SelectItem>
                <SelectItem value="7">7 Attempts</SelectItem>
                <SelectItem value="8">8 Attempts</SelectItem>
                <SelectItem value="9">9 Attempts</SelectItem>
                <SelectItem value="10">10 Attempts</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground mt-2">
              Number of times to retry reaching a contact
            </p>
          </div>
        </div>
      </div>

       {/* Campaign Status */}
       {/* <div className="space-y-4">
        <Label className="text-base font-medium">Campaign Status</Label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card
            className={`cursor-pointer transition-all hover:bg-green-50 ${status === "active" ? "border-green-500 bg-green-50" : ""}`}
            onClick={() => handleStatusChange("active")}
          >
            <CardContent className="p-4 flex flex-col items-center justify-center space-y-2">
              <Play className="h-8 w-8 text-green-500" />
              <Label className="font-medium">Active</Label>
              <p className="text-xs text-center text-muted-foreground">Start immediately</p>
            </CardContent>
          </Card>

          <Card
            className={`cursor-pointer transition-all hover:bg-amber-50 ${status === "paused" ? "border-amber-500 bg-amber-50" : ""}`}
            onClick={() => handleStatusChange("paused")}
          >
            <CardContent className="p-4 flex flex-col items-center justify-center space-y-2">
              <Pause className="h-8 w-8 text-amber-500" />
              <Label className="font-medium">Paused</Label>
              <p className="text-xs text-center text-muted-foreground">Save for later</p>
            </CardContent>
          </Card>

          <Card
            className={`cursor-pointer transition-all hover:bg-red-50 ${status === "inactive" ? "border-red-500 bg-red-50" : ""}`}
            onClick={() => handleStatusChange("inactive")}
          >
            <CardContent className="p-4 flex flex-col items-center justify-center space-y-2">
              <StopCircle className="h-8 w-8 text-red-500" />
              <Label className="font-medium">Inactive</Label>
              <p className="text-xs text-center text-muted-foreground">Draft mode</p>
            </CardContent>
          </Card>
        </div>
      </div> */}

       {/* Instant Call Settings */}
       <div className="space-y-4">
        <Label className="text-base font-medium">Instant Call</Label>
        <p className="text-sm text-muted-foreground mb-4">
          Enable instant calling to immediately connect with contacts when the campaign is active
        </p>
        <RadioGroup
          value={instantCall ? "yes" : "no"}
          onValueChange={handleInstantCallChange}
          className="flex space-x-8"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="yes" id="instant-call-yes" />
            <Label htmlFor="instant-call-yes">Yes</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="no" id="instant-call-no" />
            <Label htmlFor="instant-call-no">No</Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
}