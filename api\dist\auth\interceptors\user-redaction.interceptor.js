"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRedactionInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
function flattenUserObject(user) {
    const flatUser = {};
    function flatten(obj) {
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                if (typeof obj[key] === 'object' &&
                    !(obj[key] instanceof Date) &&
                    obj[key] !== null &&
                    !Array.isArray(obj[key])) {
                    flatten(obj[key]);
                }
                else {
                    flatUser[key] = obj[key];
                }
            }
        }
    }
    flatten(user);
    return flatUser;
}
const excludeFields = (user) => {
    const flatUser = flattenUserObject(user);
    const { password, isApproved, refreshToken, updatedAt, ...rest } = flatUser;
    return {
        ...rest,
        fullName: flatUser.fullName
    };
};
let UserRedactionInterceptor = class UserRedactionInterceptor {
    intercept(_context, next) {
        return next.handle().pipe((0, operators_1.map)((payload) => {
            const data = payload.data ? payload.data : payload;
            if (payload.data) {
                return {
                    ...payload,
                    data: Array.isArray(data)
                        ? data.map((user) => excludeFields(user))
                        : excludeFields(data),
                };
            }
            else {
                return Array.isArray(data)
                    ? data.map((user) => excludeFields(user))
                    : excludeFields(data);
            }
        }));
    }
};
exports.UserRedactionInterceptor = UserRedactionInterceptor;
exports.UserRedactionInterceptor = UserRedactionInterceptor = __decorate([
    (0, common_1.Injectable)()
], UserRedactionInterceptor);
//# sourceMappingURL=user-redaction.interceptor.js.map