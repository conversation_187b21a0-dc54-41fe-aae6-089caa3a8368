import { Card, CardContent } from "@/components/ui/card";
import { Star } from "lucide-react";

export default function Testimonials() {
  return (
    <section className="py-20">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 className="text-3xl font-bold text-center mb-12">
        Loved by AI Enthusiasts
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {/* Testimonial 1 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <Star className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-2">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-5 w-5 text-yellow-400"
                      fill="currentColor"
                    />
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              &quot;Orova AI has revolutionized how we build and deploy AI agents.
              The natural language interface makes it incredibly intuitive.&quot;
            </p>
            <div className="font-medium">Sarah Johnson</div>
            <div className="text-gray-500 text-sm">AI Developer</div>
          </CardContent>
        </Card>
        {/* Testimonial 2 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <Star className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-2">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-5 w-5 text-yellow-400"
                      fill="currentColor"
                    />
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              &quot;The voice integration feature is a game-changer. Our agents now
              feel more human-like than ever before.&quot;
            </p>
            <div className="font-medium">Michael Chen</div>
            <div className="text-gray-500 text-sm">Product Manager</div>
          </CardContent>
        </Card>
        {/* Testimonial 3 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <Star className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-2">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-5 w-5 text-yellow-400"
                      fill="currentColor"
                    />
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              &quot;The pre-built templates saved us weeks of development time.
              Highly recommended for any AI project.&quot;
            </p>
            <div className="font-medium">Emily Rodriguez</div>
            <div className="text-gray-500 text-sm">Startup Founder</div>
          </CardContent>
        </Card>
      </div>
    </div>
  </section>
  )
}
