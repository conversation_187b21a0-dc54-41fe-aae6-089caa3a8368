"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const campaign_service_1 = require("../campaign/campaign.service");
const axios_1 = __importDefault(require("axios"));
const qs = __importStar(require("qs"));
const logger_service_1 = require("../logger/logger.service");
const scheduled_call_service_1 = require("../scheduled-call/scheduled-call.service");
const number_to_words_1 = require("number-to-words");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const google_libphonenumber_1 = require("google-libphonenumber");
let ContactsService = class ContactsService {
    constructor(contactModel, campaignModel, loggerService, scheduledCallService, campaignService) {
        this.contactModel = contactModel;
        this.campaignModel = campaignModel;
        this.loggerService = loggerService;
        this.scheduledCallService = scheduledCallService;
        this.campaignService = campaignService;
    }
    async onModuleInit() {
        try {
            const collection = this.contactModel.collection;
            const indexes = await collection.indexes();
            const contactNameIndex = indexes.find((index) => index.name === "contactName_1" && index.unique === true);
            if (contactNameIndex) {
                await collection.dropIndex("contactName_1");
                await this.loggerService.log("Dropped unique index on contactName");
            }
            const compoundIndex = indexes.find((index) => index.name === "contactName_1_phoneNumber_1" && index.unique === true);
            if (!compoundIndex) {
                await collection.createIndex({ contactName: 1, phoneNumber: 1 }, { unique: true });
                await this.loggerService.log("Created compound unique index on contactName and phoneNumber");
            }
        }
        catch (error) {
            await this.loggerService.error("Error updating indexes:", error.message);
        }
    }
    normalizePhoneNumber(phone) {
        if (!phone)
            return phone;
        return phone.startsWith("+") ? phone : `+${phone}`;
    }
    async getRegionFromPhoneNumber(phone) {
        const phoneUtil = google_libphonenumber_1.PhoneNumberUtil.getInstance();
        try {
            const number = phoneUtil.parse(phone);
            const regionCode = phoneUtil.getRegionCodeForNumber(number);
            const zones = moment_timezone_1.default.tz.zonesForCountry(regionCode);
            if (zones && zones.length > 0) {
                return zones[0];
            }
            return "Unknown";
        }
        catch (error) {
            return "Unknown";
        }
    }
    async create(createContactDto, userName) {
        createContactDto.phoneNumber = this.normalizePhoneNumber(createContactDto.phoneNumber);
        let region = createContactDto.region || null;
        if (!region) {
            createContactDto.region = await this.getRegionFromPhoneNumber(createContactDto.phoneNumber);
        }
        const existingContact = await this.contactModel
            .findOne({
            contactName: createContactDto.contactName,
            phoneNumber: createContactDto.phoneNumber,
        })
            .exec();
        if (existingContact) {
            throw new common_1.HttpException("A contact with this name and phone number already exists", common_1.HttpStatus.CONFLICT);
        }
        const createdContact = new this.contactModel({
            ...createContactDto,
            source: 'manual',
            addedBy: userName,
        });
        if (createContactDto.campaigns && createContactDto.campaigns.length > 0) {
            for (const campaignId of createContactDto.campaigns) {
                await this.campaignModel.updateOne({ _id: campaignId }, {
                    $addToSet: {
                        contacts: {
                            contactId: createdContact._id,
                            contactName: createdContact.contactName,
                            phoneNumber: createdContact.phoneNumber,
                        },
                    },
                });
            }
            const savedContact = await createdContact.save();
            try {
                for (const campaignId of createContactDto.campaigns) {
                    const campaign = await this.campaignModel.findById(campaignId).exec();
                    if (campaign) {
                        await this.loggerService.log(`Scheduling calls for new contact ${savedContact.contactName} in campaign ${campaign.name}`);
                        await this.campaignService.scheduleCallsForContactInCampaign(campaign, savedContact);
                    }
                }
                return savedContact;
            }
            catch (error) {
                await this.loggerService.error(`Error scheduling calls for new contact:`, error.message);
                return savedContact;
            }
        }
        return createdContact.save();
    }
    async update(id, updateContactDto) {
        const existingContact = await this.contactModel.findById(id).exec();
        if (!existingContact)
            throw new common_1.NotFoundException("Contact not found");
        if (updateContactDto.phoneNumber) {
            updateContactDto.phoneNumber = this.normalizePhoneNumber(updateContactDto.phoneNumber);
        }
        if (updateContactDto.contactName && updateContactDto.phoneNumber) {
            const duplicateContact = await this.contactModel
                .findOne({
                _id: { $ne: id },
                contactName: updateContactDto.contactName,
                phoneNumber: updateContactDto.phoneNumber,
            })
                .exec();
            if (duplicateContact) {
                throw new common_1.HttpException("Another contact with this name and phone number already exists", common_1.HttpStatus.CONFLICT);
            }
        }
        if (updateContactDto.campaigns) {
            await this.campaignModel.updateMany({ "contacts.contactId": existingContact._id }, { $pull: { contacts: { contactId: existingContact._id } } });
            for (const campaignId of updateContactDto.campaigns) {
                await this.campaignModel.updateOne({ _id: campaignId }, {
                    $addToSet: {
                        contacts: {
                            contactId: existingContact._id,
                            contactName: updateContactDto.contactName || existingContact.contactName,
                            phoneNumber: updateContactDto.phoneNumber || existingContact.phoneNumber,
                        },
                    },
                });
            }
            await this.contactModel.updateOne({ _id: existingContact._id }, { $set: { campaigns: updateContactDto.campaigns } });
            try {
                const existingCampaignIds = existingContact.campaigns || [];
                const newCampaignIds = updateContactDto.campaigns.filter((campaignId) => !existingCampaignIds.includes(campaignId));
                if (newCampaignIds.length > 0) {
                    const updatedContact = await this.contactModel
                        .findById(existingContact._id)
                        .exec();
                    for (const campaignId of newCampaignIds) {
                        const campaign = await this.campaignModel
                            .findById(campaignId)
                            .exec();
                        if (campaign) {
                            await this.loggerService.log(`Scheduling calls for contact ${updatedContact.contactName} in campaign ${campaign.name}`);
                            await this.campaignService.scheduleCallsForContactInCampaign(campaign, updatedContact);
                        }
                    }
                }
            }
            catch (error) {
                await this.loggerService.error(`Error scheduling calls for contact:`, error.message);
            }
        }
        return this.contactModel
            .findByIdAndUpdate(id, updateContactDto, { new: true })
            .exec();
    }
    async findAll(page, limit, search, filterType = 'name', campaignId, noCampaign) {
        try {
            const totalCount = await this.contactModel.countDocuments().exec();
            const filter = {};
            if (search && search.trim().length >= 1) {
                const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const searchRegex = new RegExp(escapedSearch, 'i');
                if (filterType === 'name') {
                    filter.$or = [
                        { contactName: { $regex: searchRegex } },
                        { phoneNumber: { $regex: searchRegex } }
                    ];
                }
                else if (filterType === 'campaign') {
                    const matchingCampaigns = await this.campaignModel
                        .find({ name: { $regex: searchRegex } })
                        .select('_id')
                        .exec();
                    const campaignIds = matchingCampaigns.map(campaign => campaign._id);
                    if (campaignIds.length > 0) {
                        filter.campaigns = { $in: campaignIds };
                    }
                    else {
                        filter._id = { $exists: false };
                    }
                }
            }
            if (campaignId) {
                filter.campaigns = campaignId;
            }
            if (noCampaign) {
                filter.$or = [
                    { campaigns: { $exists: false } },
                    { campaigns: { $size: 0 } },
                    { campaigns: null }
                ];
            }
            let filteredCount = totalCount;
            if (Object.keys(filter).length > 0) {
                filteredCount = await this.contactModel.countDocuments(filter).exec();
            }
            let query = this.contactModel
                .find(filter)
                .sort({ contactName: 1 })
                .populate('campaigns', 'name');
            if (page && limit) {
                const skip = (page - 1) * limit;
                query = query.skip(skip).limit(limit);
            }
            const contacts = await query.exec();
            contacts.forEach(contact => {
                contact['_totalCount'] = totalCount;
                contact['_filteredCount'] = filteredCount;
            });
            return contacts;
        }
        catch (error) {
            await this.loggerService.error('Error retrieving contacts:', error.message);
            throw error;
        }
    }
    async findById(id) {
        const contact = await this.contactModel
            .findById(id)
            .populate("campaigns", "name")
            .exec();
        if (!contact)
            throw new common_1.NotFoundException("Contact not found");
        return contact;
    }
    async remove(id) {
        const result = await this.contactModel.findByIdAndDelete(id).exec();
        if (!result)
            throw new common_1.NotFoundException("Contact not found");
        await this.campaignModel.updateMany({ "contacts.contactId": result._id }, { $pull: { contacts: { contactId: result._id } } });
    }
    async searchContacts(query) {
        const searchRegex = new RegExp(query, "i");
        return this.contactModel
            .find({
            $or: [
                { contactName: { $regex: searchRegex } },
                { phoneNumber: { $regex: searchRegex } },
            ],
        })
            .populate("campaigns", "name")
            .exec();
    }
    async filterBycampaign(campaign) {
        return this.contactModel
            .find({
            campaigns: { $in: campaign },
        })
            .populate("campaigns", "name")
            .exec();
    }
    async getToken() {
        const data = qs.stringify({
            grant_type: "client_credentials",
            client_id: process.env.CLIENT_ID || "8f9180d3-8259-41ab-9b68-380f4cd5aaa7",
            client_secret: process.env.CLIENT_SECRET || "****************************************",
            scope: process.env.SCOPE || "8f9180d3-8259-41ab-9b68-380f4cd5aaa7/.default",
        });
        try {
            const response = await axios_1.default.post("https://login.microsoftonline.com/b8ed89d6-dafd-4034-801a-1e7d96625270/oauth2/v2.0/token", data, { headers: { "Content-Type": "application/x-www-form-urlencoded" } });
            return response.data.access_token;
        }
        catch (error) {
            console.error("Error fetching token:", error.response?.data || error.message);
            throw new common_1.HttpException("Error fetching token", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async processContacts(externalContacts) {
        const processedContacts = [];
        const contactsByCampaign = {};
        for (const c of externalContacts) {
            const normalizedPhone = this.normalizePhoneNumber(c.mobileNumber);
            let region = c.region || null;
            if (!region) {
                region = await this.getRegionFromPhoneNumber(normalizedPhone);
            }
            const campaignIds = [];
            if (c.campaign && c.campaign.length > 0) {
                const camp = await this.campaignModel.findOne({ name: c.campaign }).exec();
                if (camp) {
                    campaignIds.push(camp.id);
                }
                else {
                    await this.loggerService.log(`Campaign not found for name: ${c.campaign}`);
                }
            }
            const contactData = {
                contactName: c.name,
                phoneNumber: normalizedPhone,
                lastCall: c.previousCallTime || null,
                region,
                campaigns: campaignIds,
                updatedAt: new Date(),
                source: 'CRM',
                customerId: c.zohoCRMRecordId || null,
                projectName: c.projectName || null,
                unitNumber: c.unitNumber || null,
                totalPayableAmount: c.totalPayableAmount || null,
                pendingPayableAmount: c.pendingPayableAmount || null,
                dueDate: c.dueDate || null,
                totalInstallments: c.totalInstallments || null,
                paymentType: c.paymentType || null,
                pendingInstallments: c.pendingInstallments || null,
                lastPaymentDate: c.lastPaymentDate || null,
                lastPaymentAmount: c.lastPaymentAmount || null,
                lastPaymentType: c.lastPaymentType || null,
                collectionBucket: c.collectionBucket || null,
                unitPrice: c.unitPrice || null,
                paidAmtIncluding: c.paidAmtIncluding || null,
                eventDate: c.eventDate || null,
                eventLocation: c.eventLocation || null,
                eventTime: c.eventTime || null,
                nameOfRegistrant: c.nameOfRegistrant || null,
            };
            const existingContact = await this.contactModel
                .findOne({ contactName: c.name, phoneNumber: normalizedPhone })
                .exec();
            const existingByCustomerId = !existingContact && c.zohoCRMRecordId
                ? await this.contactModel.findOne({ customerId: c.zohoCRMRecordId }).exec()
                : null;
            let savedContact;
            const toUpdate = existingContact || existingByCustomerId;
            if (toUpdate) {
                savedContact = await this.contactModel
                    .findByIdAndUpdate(toUpdate._id, contactData, { new: true })
                    .exec();
                await this.loggerService.log(`Updated existing contact: ${c.name} (${normalizedPhone}).`);
            }
            else {
                savedContact = await new this.contactModel(contactData).save();
                await this.loggerService.log(`Imported new contact: ${c.name} (${normalizedPhone}).`);
            }
            processedContacts.push(savedContact);
            for (const campId of campaignIds) {
                if (!contactsByCampaign[campId]) {
                    contactsByCampaign[campId] = [];
                }
                contactsByCampaign[campId].push(savedContact);
                await this.campaignModel.updateOne({ _id: campId }, {
                    $addToSet: {
                        contacts: {
                            contactId: savedContact._id,
                            contactName: savedContact.contactName,
                            phoneNumber: savedContact.phoneNumber,
                        },
                    },
                });
                await this.loggerService.log(`Added contact ${savedContact.contactName} to campaign ${campId}`);
            }
        }
        for (const [campId, contacts] of Object.entries(contactsByCampaign)) {
            const campaign = await this.campaignModel.findById(campId).exec();
            if (!campaign)
                continue;
            await this.loggerService.log(`Bulk scheduling ${contacts.length} contacts for campaign ${campaign.name}`);
            await this.campaignService.scheduleCallsForContacts(campaign, contacts);
        }
        return processedContacts;
    }
    async callData(number, name) {
        const normalizedPhone = this.normalizePhoneNumber(number);
        const contact = await this.contactModel
            .findOne({ contactName: name, phoneNumber: normalizedPhone })
            .exec();
        if (!contact) {
            throw new common_1.NotFoundException("Contact not found");
        }
        const totalPayableAmountWords = contact.totalPayableAmount !== null &&
            contact.totalPayableAmount !== undefined
            ? (0, number_to_words_1.toWords)(contact.totalPayableAmount)
            : null;
        const pendingPayableAmountWords = contact.pendingPayableAmount !== null &&
            contact.pendingPayableAmount !== undefined
            ? (0, number_to_words_1.toWords)(contact.pendingPayableAmount)
            : null;
        const lastPaymentAmountWords = contact.lastPaymentAmount !== null &&
            contact.lastPaymentAmount !== undefined
            ? (0, number_to_words_1.toWords)(contact.lastPaymentAmount)
            : null;
        const unitPriceWords = contact.unitPrice !== null && contact.unitPrice !== undefined
            ? (0, number_to_words_1.toWords)(contact.unitPrice)
            : null;
        const paidAmtIncludingWords = contact.paidAmtIncluding !== null &&
            contact.paidAmtIncluding !== undefined
            ? (0, number_to_words_1.toWords)(contact.paidAmtIncluding)
            : null;
        const contactObject = contact.toObject();
        const { dueDate, eventDate, campaigns, updatedAt, _id, __v, ...otherData } = contactObject;
        if (totalPayableAmountWords) {
            otherData.totalPayableAmount = totalPayableAmountWords;
        }
        if (pendingPayableAmountWords) {
            otherData.pendingPayableAmount = pendingPayableAmountWords;
        }
        if (lastPaymentAmountWords) {
            otherData.lastPaymentAmount = lastPaymentAmountWords;
        }
        if (unitPriceWords) {
            otherData.unitPrice = unitPriceWords;
        }
        if (paidAmtIncludingWords) {
            otherData.paidAmtIncluding = paidAmtIncludingWords;
        }
        const FormateddueDate = dueDate
            ? (0, moment_timezone_1.default)(dueDate).format("Do MMMM YYYY")
            : null;
        const currentTime = (0, moment_timezone_1.default)()
            .tz(otherData.region)
            .format("YYYY-MM-DD HH:mm:ss");
        return {
            ...otherData,
            currentTime,
            dueDate: FormateddueDate,
        };
    }
    async importContacts(userName) {
        const accessToken = await this.getToken();
        await this.loggerService.log("Starting import of contacts from CRM...");
        try {
            const leadsResponse = await axios_1.default.get("https://apim-bgt-prd-001.azure-api.net/ai/api/vw_ai_leads", { headers: { Authorization: `Bearer ${accessToken}` } });
            await this.loggerService.log("Retrieved contacts data from CRM.");
            const externalContacts = leadsResponse.data.value;
            const savedContacts = [];
            for (const c of externalContacts) {
                const normalizedPhone = this.normalizePhoneNumber(c.MobileNumber);
                const existingContact = await this.contactModel
                    .findOne({
                    contactName: c.Name,
                    phoneNumber: normalizedPhone,
                })
                    .exec();
                if (!existingContact) {
                    const contactData = {
                        contactName: c.Name,
                        phoneNumber: normalizedPhone,
                        lastCall: c.previousCallTime,
                        campaigns: [],
                        updatedAt: new Date(),
                        addedBy: userName,
                        source: "CRM",
                    };
                    const savedContact = await new this.contactModel(contactData).save();
                    savedContacts.push(savedContact);
                    await this.loggerService.log(`Imported new contact: ${c.Name} (${normalizedPhone}).`);
                }
                else {
                    await this.loggerService.log(`Contact already exists: ${c.Name} (${normalizedPhone}).`);
                }
            }
            await this.loggerService.log(`Import completed. ${savedContacts.length} new contacts imported.`);
            return savedContacts;
        }
        catch (error) {
            await this.loggerService.error("Error importing contacts", error.response?.data || error.message);
            throw new common_1.HttpException("Error importing contacts", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadContacts(contacts, userName) {
        try {
            const results = {
                success: [],
                errors: [],
                successfulContacts: []
            };
            for (const contact of contacts) {
                try {
                    if (!contact.contactName || !contact.phoneNumber) {
                        results.errors.push({
                            contact: contact.contactName || 'Unknown',
                            error: 'Contact name and phone number are required'
                        });
                        continue;
                    }
                    const normalizedPhone = this.normalizePhoneNumber(contact.phoneNumber);
                    const existingContact = await this.contactModel.findOne({
                        contactName: contact.contactName,
                        phoneNumber: normalizedPhone,
                    });
                    if (existingContact) {
                        results.errors.push({
                            contact: contact.contactName,
                            error: 'Contact already exists'
                        });
                        continue;
                    }
                    const newContact = new this.contactModel({
                        contactName: contact.contactName,
                        phoneNumber: normalizedPhone,
                        customerId: contact.customerId || null,
                        campaignNames: contact.campaignNames || [],
                        source: 'file Upload',
                        addedBy: userName,
                        updatedAt: new Date(),
                        ...(contact.region && { region: contact.region })
                    });
                    await newContact.save();
                    results.success.push({
                        contact: contact.contactName,
                        id: newContact._id
                    });
                    results.successfulContacts.push({
                        contactName: contact.contactName,
                        phoneNumber: normalizedPhone,
                        customerId: contact.customerId,
                        region: contact.region,
                        source: 'file Upload',
                        addedBy: userName
                    });
                }
                catch (error) {
                    results.errors.push({
                        contact: contact.contactName || 'Unknown',
                        error: error.message
                    });
                }
            }
            return {
                message: 'Upload processing completed',
                totalProcessed: contacts.length,
                successCount: results.success.length,
                errorCount: results.errors.length,
                successfulContacts: results.successfulContacts,
                results
            };
        }
        catch (error) {
            throw new common_1.HttpException('Error processing upload', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ContactsService = ContactsService;
exports.ContactsService = ContactsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Contact")),
    __param(1, (0, mongoose_1.InjectModel)("Campaign")),
    __param(4, (0, common_1.Inject)((0, common_1.forwardRef)(() => campaign_service_1.CampaignService))),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        logger_service_1.LoggerService,
        scheduled_call_service_1.ScheduledCallService,
        campaign_service_1.CampaignService])
], ContactsService);
//# sourceMappingURL=contacts.service.js.map