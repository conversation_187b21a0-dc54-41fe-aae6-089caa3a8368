import { Injectable, HttpException, HttpStatus, forwardRef, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

import { OrganizationPaymentMethod, OrganizationPaymentMethodDocument } from './schemas/organization-payment-method.schema';
import { OrganizationTransaction, OrganizationTransactionDocument } from './schemas/organization-transaction.schema';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { CreatePaymentIntentDto } from './dto/create-payment-intent.dto';
import { LoggerService } from 'src/logger/logger.service';
import { UsersService } from 'src/users/users.service';
import { OrganizationsService } from 'src/organizations/organizations.service';

@Injectable()
export class BillingService {
  private stripe: Stripe;

  constructor(

    @InjectModel(OrganizationPaymentMethod.name) private orgPaymentMethodModel: Model<OrganizationPaymentMethodDocument>,
    @InjectModel(OrganizationTransaction.name) private orgTransactionModel: Model<OrganizationTransactionDocument>,
    private configService: ConfigService,
    private loggerService: LoggerService,
    private usersService: UsersService,
    @Inject(forwardRef(() => OrganizationsService))
    private organizationsService: OrganizationsService,
  ) {
    this.stripe = new Stripe(this.configService.get<string>('STRIPE_SECRET_KEY'), {
      apiVersion: '2025-04-30.basil',
    });
  }

  async createStripeCustomer(userId: string, email: string, name?: string) {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          userId,
        },
      });

      return customer;
    } catch (error) {
      this.loggerService.error('Error creating Stripe customer', error);
      throw new HttpException(
        'Failed to create customer account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrCreateStripeCustomer(userId: string, email: string, name?: string) {
    try {
      // Search for existing customer by metadata.userId
      const customers = await this.stripe.customers.list({
        limit: 1,
        email,
      });

      let customer = customers.data.find(c => c.metadata.userId === userId);

      // If no customer found, create one
      if (!customer) {
        customer = await this.createStripeCustomer(userId, email, name);
      }

      return customer;
    } catch (error) {
      this.loggerService.error('Error getting or creating Stripe customer', error);
      throw new HttpException(
        'Failed to retrieve or create customer account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createStripeOrganizationCustomer(organizationId: string, email: string, name?: string) {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          organizationId,
        },
      });

      // Update the organization with the Stripe customer ID
      await this.organizationsService.setStripeCustomerId(organizationId, customer.id);

      return customer;
    } catch (error) {
      this.loggerService.error('Error creating Stripe organization customer', error);
      throw new HttpException(
        'Failed to create organization customer account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrCreateStripeOrganizationCustomer(organizationId: string, email: string, name?: string) {
    try {
      // First, check if the organization already has a Stripe customer ID
      const organization = await this.organizationsService.findOne(organizationId);

      if (organization.stripeCustomerId) {
        try {
          // Try to retrieve the customer from Stripe
          const customer = await this.stripe.customers.retrieve(organization.stripeCustomerId);

          // Make sure it's not deleted
          if (customer && !('deleted' in customer)) {
            return customer;
          }
        } catch (error) {
          this.loggerService.error(`Error retrieving Stripe customer for organization ${organizationId}`, error);
          // Continue to create a new customer
        }
      }

      // If we get here, we need to create a new customer
      return await this.createStripeOrganizationCustomer(organizationId, email, name);
    } catch (error) {
      this.loggerService.error('Error getting or creating Stripe organization customer', error);
      throw new HttpException(
        'Failed to retrieve or create organization customer account',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }



  /**
   * Get a user with their organization
   */
  async getUserWithOrganization(userId: string) {
    try {
      const user = await this.usersService.findById(userId);
      if (!user) {
        throw new HttpException(`User with ID ${userId} not found`, HttpStatus.NOT_FOUND);
      }
      return user;
    } catch (error) {
      this.loggerService.error(`Error getting user with organization: ${error.message}`, error);
      throw new HttpException(
        error.message || 'Failed to get user with organization',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }



  // Organization-related methods

  async createOrganizationPaymentIntent(
    organizationId: string,
    userId: string,
    email: string,
    createPaymentIntentDto: CreatePaymentIntentDto,
    name?: string,
  ) {
    try {
      const { amount, currency, paymentMethodId, paymentMethodType, description } = createPaymentIntentDto;

      // Enforce minimum payment amount of $10 (1000 cents)
      if (amount < 1000) {
        throw new HttpException(
          'Minimum payment amount is $10',
          HttpStatus.BAD_REQUEST
        );
      }

      // Get or create customer for the organization
      const customer = await this.getOrCreateStripeOrganizationCustomer(organizationId, email, name);

      // Create payment intent
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount,
        currency,
        customer: customer.id,
        description,
        payment_method_types: [paymentMethodType],
        metadata: {
          organizationId,
          userId, // Track which user initiated the payment
        },
      };

      // If a specific payment method is provided, use it
      if (paymentMethodId) {
        const paymentMethod = await this.orgPaymentMethodModel.findOne({
          _id: paymentMethodId,
          organizationId,
        });

        if (!paymentMethod) {
          throw new HttpException('Payment method not found', HttpStatus.NOT_FOUND);
        }

        paymentIntentParams.payment_method = paymentMethod.stripePaymentMethodId;
        paymentIntentParams.confirm = true;
      }

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

      // Create a transaction record
      const transaction = new this.orgTransactionModel({
        organizationId,
        userId,
        amount: amount / 100, // Convert from cents to dollars for display
        currency,
        status: paymentIntent.status,
        paymentMethodId: paymentMethodId || null,
        stripePaymentIntentId: paymentIntent.id,
        stripeCustomerId: customer.id,
        description,
        email,
        metadata: {
          paymentMethodType,
        },
      });

      await transaction.save();

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        status: paymentIntent.status,
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;

      this.loggerService.error('Error creating organization payment intent', error);
      throw new HttpException(
        'Failed to process payment',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createOrganizationPaymentMethod(organizationId: string, createPaymentMethodDto: CreatePaymentMethodDto) {
    try {
      const newPaymentMethod = new this.orgPaymentMethodModel({
        organizationId,
        ...createPaymentMethodDto,
      });

      // If this is the first payment method or isDefault is true, set it as default
      const existingMethods = await this.orgPaymentMethodModel.find({ organizationId }).exec();
      if (existingMethods.length === 0 || createPaymentMethodDto.isDefault) {
        newPaymentMethod.isDefault = true;

        // If setting this as default, unset any other defaults
        if (existingMethods.length > 0) {
          await this.orgPaymentMethodModel.updateMany(
            { organizationId, isDefault: true },
            { $set: { isDefault: false } }
          );
        }
      }

      return await newPaymentMethod.save();
    } catch (error) {
      this.loggerService.error('Error creating organization payment method', error);
      throw new HttpException(
        'Failed to create organization payment method',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrganizationPaymentMethods(organizationId: string) {
    try {
      return await this.orgPaymentMethodModel.find({ organizationId }).sort({ isDefault: -1 }).exec();
    } catch (error) {
      this.loggerService.error('Error retrieving organization payment methods', error);
      throw new HttpException(
        'Failed to retrieve organization payment methods',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async setDefaultOrganizationPaymentMethod(organizationId: string, paymentMethodId: string) {
    try {
      // Unset any existing default
      await this.orgPaymentMethodModel.updateMany(
        { organizationId, isDefault: true },
        { $set: { isDefault: false } }
      );

      // Set the new default
      const updatedMethod = await this.orgPaymentMethodModel.findOneAndUpdate(
        { _id: paymentMethodId, organizationId },
        { $set: { isDefault: true } },
        { new: true }
      );

      if (!updatedMethod) {
        throw new HttpException('Organization payment method not found', HttpStatus.NOT_FOUND);
      }

      return updatedMethod;
    } catch (error) {
      if (error instanceof HttpException) throw error;

      this.loggerService.error('Error setting default organization payment method', error);
      throw new HttpException(
        'Failed to set default organization payment method',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async removeOrganizationPaymentMethod(organizationId: string, paymentMethodId: string) {
    try {
      const paymentMethod = await this.orgPaymentMethodModel.findOne({
        _id: paymentMethodId,
        organizationId,
      });

      if (!paymentMethod) {
        throw new HttpException('Organization payment method not found', HttpStatus.NOT_FOUND);
      }

      // If it's a Stripe payment method, delete it from Stripe too
      if (paymentMethod.stripePaymentMethodId) {
        await this.stripe.paymentMethods.detach(paymentMethod.stripePaymentMethodId);
      }

      // Delete from our database
      await this.orgPaymentMethodModel.deleteOne({ _id: paymentMethodId });

      // If this was the default and there are other methods, make another one default
      if (paymentMethod.isDefault) {
        const otherMethod = await this.orgPaymentMethodModel.findOne({ organizationId }).exec();
        if (otherMethod) {
          await this.setDefaultOrganizationPaymentMethod(organizationId, otherMethod._id.toString());
        }
      }

      return { success: true, message: 'Organization payment method removed successfully' };
    } catch (error) {
      if (error instanceof HttpException) throw error;

      this.loggerService.error('Error removing organization payment method', error);
      throw new HttpException(
        'Failed to remove organization payment method',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async processOrganizationPayment(
    organizationId: string,
    userId: string,
    email: string,
    paymentMethodId: string,
    paymentDetails: {
      amount: number;
      currency: string;
      description?: string;
    },
    name: string,
    savePaymentMethod: boolean = false,
    setAsDefault: boolean = false,
  ) {
    try {
      // Enforce minimum payment amount of $10 (1000 cents)
      if (paymentDetails.amount < 1000) {
        throw new HttpException(
          'Minimum payment amount is $10',
          HttpStatus.BAD_REQUEST
        );
      }

      // Get or create customer
      const customer = await this.getOrCreateStripeOrganizationCustomer(organizationId, email, name);

      // Retrieve the payment method from Stripe
      const paymentMethod = await this.stripe.paymentMethods.retrieve(paymentMethodId);

      // Attach the payment method to the customer if it's not already attached
      if (paymentMethod.customer !== customer.id) {
        await this.stripe.paymentMethods.attach(paymentMethodId, {
          customer: customer.id,
        });
      }

      // Create and confirm the payment intent in one step
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: paymentDetails.amount,
        currency: paymentDetails.currency,
        customer: customer.id,
        payment_method: paymentMethodId,
        description: paymentDetails.description,
        confirm: true, // Confirm the payment immediately
        metadata: {
          organizationId,
          userId, // Track which user initiated the payment
        },
        // Disable redirect-based payment methods
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'never',
        },
        // Fallback return URL in case a redirect happens
        return_url: `${this.configService.get<string>('FRONTEND_URL')}/billing`,
      });

      // Handle payment method saving
      let dbPaymentMethod = null;

      if (savePaymentMethod) {
        // Check if the payment method already exists in our database
        dbPaymentMethod = await this.orgPaymentMethodModel.findOne({
          stripePaymentMethodId: paymentMethodId,
          organizationId,
        });

        if (!dbPaymentMethod) {
          // Extract card details from the Stripe payment method
          const card = paymentMethod.card;

          dbPaymentMethod = new this.orgPaymentMethodModel({
            organizationId,
            type: 'card',
            stripePaymentMethodId: paymentMethodId,
            last4: card.last4,
            expMonth: card.exp_month.toString().padStart(2, '0'),
            expYear: card.exp_year.toString().slice(-2),
            cardholderName: paymentMethod.billing_details.name || name,
            brand: card.brand,
            isDefault: false,
          });

          // Check if this is the first payment method or if setAsDefault is true
          if (setAsDefault) {
            // Unset any existing default payment methods
            await this.orgPaymentMethodModel.updateMany(
              { organizationId, isDefault: true },
              { $set: { isDefault: false } }
            );

            dbPaymentMethod.isDefault = true;
          } else {
            // If no payment methods exist, set this one as default
            const existingMethods = await this.orgPaymentMethodModel.find({ organizationId }).exec();
            if (existingMethods.length === 0) {
              dbPaymentMethod.isDefault = true;
            }
          }

          await dbPaymentMethod.save();
        } else if (setAsDefault && !dbPaymentMethod.isDefault) {
          // If the payment method exists and setAsDefault is true, update it
          await this.orgPaymentMethodModel.updateMany(
            { organizationId, isDefault: true },
            { $set: { isDefault: false } }
          );

          dbPaymentMethod.isDefault = true;
          await dbPaymentMethod.save();
        }
      } else {
        // Even if we're not saving the payment method, we need the ID for the transaction
        dbPaymentMethod = await this.orgPaymentMethodModel.findOne({
          stripePaymentMethodId: paymentMethodId,
          organizationId,
        });
      }

      // Create a transaction record
      const transaction = new this.orgTransactionModel({
        organizationId,
        userId, // Track which user initiated the payment
        amount: paymentDetails.amount / 100, // Convert from cents to dollars for display
        currency: paymentDetails.currency,
        status: paymentIntent.status,
        paymentMethodId: dbPaymentMethod ? dbPaymentMethod._id : null,
        stripePaymentIntentId: paymentIntent.id,
        stripeCustomerId: customer.id,
        description: paymentDetails.description,
        email,
        metadata: {
          paymentMethodType: 'card',
        },
      });

      await transaction.save();

      // If payment was successful, update organization credits immediately
      if (paymentIntent.status === 'succeeded') {
        try {
          // Get the organization
          const organization = await this.organizationsService.findOne(organizationId);

          // Calculate new credits (current credits + payment amount)
          const currentCredits = organization.credits || 0;
          const amountInDollars = paymentDetails.amount / 100;
          const newCredits = currentCredits + amountInDollars;

          // Update organization credits
          await this.organizationsService.updateBilling(organizationId, { credits: newCredits });

          this.loggerService.log(`Updated credits for organization ${organizationId}: ${currentCredits} + ${amountInDollars} = ${newCredits}`);
        } catch (orgError) {
          this.loggerService.error(`Error updating organization credits for payment ${paymentIntent.id}`, orgError);
        }
      }

      // Return the payment result
      return {
        success: paymentIntent.status === 'succeeded',
        status: paymentIntent.status,
        transactionId: transaction._id,
        paymentIntentId: paymentIntent.id,
        paymentMethodId: dbPaymentMethod ? dbPaymentMethod._id : null,
        clientSecret: paymentIntent.client_secret,
        savedPaymentMethod: !!dbPaymentMethod,
      };
    } catch (error) {
      this.loggerService.error('Error processing organization payment', error);

      // Handle Stripe errors with more specific messages
      if (error.type) {
        switch (error.type) {
          case 'StripeCardError':
            throw new HttpException(
              error.message || 'Your card was declined',
              HttpStatus.BAD_REQUEST,
            );
          case 'StripeInvalidRequestError':
            throw new HttpException(
              'Invalid payment information',
              HttpStatus.BAD_REQUEST,
            );
          default:
            throw new HttpException(
              error.message || 'Payment processing failed',
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
      }

      throw new HttpException(
        'Failed to process organization payment',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrganizationTransactionHistory(organizationId: string, page = 1, limit = 10) {
    try {
      const skip = (page - 1) * limit;

      const transactions = await this.orgTransactionModel
        .find({ organizationId })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec();

      const total = await this.orgTransactionModel.countDocuments({ organizationId });

      return {
        transactions,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.loggerService.error('Error retrieving organization transaction history', error);
      throw new HttpException(
        'Failed to retrieve organization transaction history',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }






}
