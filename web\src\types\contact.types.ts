/* eslint-disable @typescript-eslint/no-explicit-any */


export interface Contact {
  _id: string;
  customerId?: string;
  contactName: string;
  phoneNumber: string;
  lastCall?: string;
  campaigns?: any[]; 
  campaignNames?: string[]; // For storing campaign names directly
  region?: string;
  updatedAt?: Date;

  // Project details
  projectName?: string;
  unitNumber?: string;

  // Payment details
  totalPayableAmount?: number | string;
  pendingPayableAmount?: number | string;
  dueDate?: string;
  totalInstallments?: number | string;
  paymentType?: string;
  pendingInstallments?: number | string;

  // Last payment info
  lastPaymentDate?: string;
  lastPaymentAmount?: number | string;
  lastPaymentType?: string;
  collectionBucket?: string;
  type?: string;

  // Collections specific fields
  unitPrice?: number | string;
  paidAmtIncluding?: number | string;

  // Event related fields
  eventDate?: string;
  eventLocation?: string;
  eventTime?: string;
  nameOfRegistrant?: string;
  createdAt: Date;
  addedBy?: string;
  source?: string;
  totalContacts?: number;
  filteredContacts?: number;
}