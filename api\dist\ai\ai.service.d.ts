import { HttpService } from '@nestjs/axios';
import { AgentService } from '../agent/agent.service';
export declare class AiService {
    private readonly httpService;
    private readonly agentsService;
    constructor(httpService: HttpService, agentsService: AgentService);
    streamChatWithAgentGenerator(agentId: string, userMessage: string, conversationHistory?: Array<{
        role: string;
        content: string;
    }>): AsyncGenerator<string>;
    getChatResponse(agentId: string, userMessage: string, conversationHistory?: Array<{
        role: string;
        content: string;
    }>): Promise<string>;
    transcribeAudio(provider: string, audioBuffer: Buffer, options?: {
        language?: string;
    }): Promise<string>;
    textToSpeech(provider: string, text: string, voiceSettings?: any): Promise<Buffer>;
}
