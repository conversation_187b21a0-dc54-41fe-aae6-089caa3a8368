import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsOptional, IsBoolean } from 'class-validator';

export class ProcessPaymentDto {
  @ApiProperty({ description: 'Stripe payment method ID' })
  @IsNotEmpty()
  @IsString()
  paymentMethodId: string;

  @ApiProperty({ description: 'Amount to charge (in cents)' })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Currency code (e.g., usd)' })
  @IsNotEmpty()
  @IsString()
  currency: string;

  @ApiProperty({ description: 'Description of the payment' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Whether to save the payment method for future use' })
  @IsOptional()
  @IsBoolean()
  savePaymentMethod?: boolean;

  @ApiProperty({ description: 'Whether to set this payment method as default' })
  @IsOptional()
  @IsBoolean()
  setAsDefault?: boolean;
}
