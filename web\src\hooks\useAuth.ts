
import { useState, useEffect } from 'react';

interface UserProfile {
  email: string;
  fullName: string;
  role: string;
}


export function useAuth() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [authIsLoading, setAuthIsLoading] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchUserProfile() {
      try {
        const token = localStorage.getItem('access_token');
        if (!token) throw new Error("No access token available");

        const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) throw new Error(`Failed to fetch user profile: ${response.status}`);
        
        const userData = await response.json();
        setUser({
          email: userData.email,
          fullName: userData.fullName,
          role: userData.role,
        });
      } catch (err) {
        setAuthError(err instanceof Error ? err.message : "Failed to load user profile");
      } finally {
        setAuthIsLoading(false);
      }
    }

    fetchUserProfile();
  }, []);

  // For backward compatibility and convenience
  const userRole = user?.role || null;

  return { user, userRole, authIsLoading, authError };
}