import { Schema, Types } from 'mongoose';
export declare const CampaignSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "paused" | "completed";
    contacts: Types.DocumentArray<{
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }, Types.Subdocument<unknown, any, {
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }> & {
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }>;
    concurrentCalls: number;
    dailyCost: number;
    successRate: number;
    sentiment: "positive" | "neutral" | "negative";
    recallHours: number;
    maxRecalls: number;
    followUpDays: string[];
    instantCall: boolean;
    batchIntervalMinutes: number;
    startDate?: NativeDate;
    endDate?: string | NativeDate;
    agentId?: string;
    createdBy?: string;
    callSchedule?: {
        timezone: string;
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
        callTime: string;
    };
    callWindow?: {
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
    };
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "paused" | "completed";
    contacts: Types.DocumentArray<{
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }, Types.Subdocument<unknown, any, {
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }> & {
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }>;
    concurrentCalls: number;
    dailyCost: number;
    successRate: number;
    sentiment: "positive" | "neutral" | "negative";
    recallHours: number;
    maxRecalls: number;
    followUpDays: string[];
    instantCall: boolean;
    batchIntervalMinutes: number;
    startDate?: NativeDate;
    endDate?: string | NativeDate;
    agentId?: string;
    createdBy?: string;
    callSchedule?: {
        timezone: string;
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
        callTime: string;
    };
    callWindow?: {
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
    };
}>> & import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
    name: string;
    status: "active" | "inactive" | "paused" | "completed";
    contacts: Types.DocumentArray<{
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }, Types.Subdocument<unknown, any, {
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }> & {
        _id?: unknown;
        contactName?: string;
        phoneNumber?: string;
        contactId?: {
            prototype?: Types.ObjectId;
            isValid?: {};
            cacheHexString?: unknown;
            generate?: {};
            createFromTime?: {};
            createFromHexString?: {};
            createFromBase64?: {};
        };
    }>;
    concurrentCalls: number;
    dailyCost: number;
    successRate: number;
    sentiment: "positive" | "neutral" | "negative";
    recallHours: number;
    maxRecalls: number;
    followUpDays: string[];
    instantCall: boolean;
    batchIntervalMinutes: number;
    startDate?: NativeDate;
    endDate?: string | NativeDate;
    agentId?: string;
    createdBy?: string;
    callSchedule?: {
        timezone: string;
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
        callTime: string;
    };
    callWindow?: {
        startTime: string;
        endTime: string;
        daysOfWeek: string[];
    };
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
