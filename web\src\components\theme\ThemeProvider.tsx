"use client";

import { ThemeProvider as NextThemesProvider, type ThemeProviderProps } from "next-themes";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
    return (
        <NextThemesProvider 
          {...props}
          enableSystem
          attribute="class"
          defaultTheme="system"
          disableTransitionOnChange={false}
        >
          {children}
        </NextThemesProvider>
      );
}