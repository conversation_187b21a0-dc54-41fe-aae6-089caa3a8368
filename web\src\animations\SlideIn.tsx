"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface SlideInProps {
  children: ReactNode;
  duration?: number;
  delay?: number;
  className?: string;
  direction?: "left" | "right" | "up" | "down";
  distance?: number;
  once?: boolean;
  viewOffset?: number;
}

export default function SlideIn({
  children,
  duration = 0.6,
  delay = 0,
  direction = "left",
  distance = 100,
  className = "",
  once = true,
  viewOffset = 0.1,
}: SlideInProps) {
  // Calculate initial position based on direction
  const initialPosition = {
    x: direction === "left" ? -distance : direction === "right" ? distance : 0,
    y: direction === "up" ? distance : direction === "down" ? -distance : 0,
  };

  return (
    <motion.div
      initial={{
        ...initialPosition,
        opacity: 0,
      }}
      whileInView={{
        x: 0,
        y: 0,
        opacity: 1,
      }}
      transition={{
        duration: duration,
        delay: delay,
        ease: [0.25, 0.1, 0.25, 1], // Cubic bezier for a nice smooth effect
      }}
      viewport={{ 
        once, 
        amount: viewOffset 
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}