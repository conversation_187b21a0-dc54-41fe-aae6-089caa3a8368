import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CampaignService } from './campaign.service';
import { CampaignController } from './campaign.controller';
import { CampaignSchema } from './schemas/campaign.schema';
import { ContactsModule } from 'src/contacts/contacts.module';
import { ScheduledCallModule } from 'src/scheduled-call/scheduled-call.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'Campaign', schema: CampaignSchema }]),
    forwardRef(() => ContactsModule),
    forwardRef(() => ScheduledCallModule),
  ],
  controllers: [CampaignController],
  providers: [CampaignService],
  exports: [CampaignService, MongooseModule],
})
export class CampaignModule {}