"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrganizationBillingDto = exports.UpdateOrganizationDto = exports.CreateOrganizationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateOrganizationDto {
}
exports.CreateOrganizationDto = CreateOrganizationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Acme Corporation', description: 'Name of the organization' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'A global leader in technology solutions', description: 'Description of the organization', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'active', description: 'Status of the organization', enum: ['active', 'inactive', 'suspended'] }),
    (0, class_validator_1.IsEnum)(['active', 'inactive', 'suspended']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrganizationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['60d0fe4f5311236168a109ca'], description: 'IDs of admin users', required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateOrganizationDto.prototype, "adminUsers", void 0);
class UpdateOrganizationDto {
}
exports.UpdateOrganizationDto = UpdateOrganizationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Acme Corporation', description: 'Name of the organization', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'A global leader in technology solutions', description: 'Description of the organization', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'active', description: 'Status of the organization', enum: ['active', 'inactive', 'suspended'], required: false }),
    (0, class_validator_1.IsEnum)(['active', 'inactive', 'suspended']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateOrganizationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['60d0fe4f5311236168a109ca'], description: 'IDs of admin users', required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateOrganizationDto.prototype, "adminUsers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['60d0fe4f5311236168a109cb'], description: 'IDs of regular users', required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateOrganizationDto.prototype, "users", void 0);
class UpdateOrganizationBillingDto {
}
exports.UpdateOrganizationBillingDto = UpdateOrganizationBillingDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 100, description: 'Credits amount', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationBillingDto.prototype, "credits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Auto-recharge enabled', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateOrganizationBillingDto.prototype, "autoRechargeEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1.0, description: 'Auto-recharge threshold', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationBillingDto.prototype, "autoRechargeThreshold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 10.0, description: 'Auto-recharge amount', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationBillingDto.prototype, "autoRechargeAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0.10, description: 'Price per minute for calls', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0.01),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationBillingDto.prototype, "callPricePerMinute", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1.0, description: 'Minimum credits threshold to block calls', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationBillingDto.prototype, "minimumCreditsThreshold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 500, description: 'Monthly minutes allowance for this organization', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationBillingDto.prototype, "monthlyMinutesAllowance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1, description: 'Day of month (1-28) when monthly credits reset', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(28),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateOrganizationBillingDto.prototype, "monthlyResetDate", void 0);
//# sourceMappingURL=organization.dto.js.map