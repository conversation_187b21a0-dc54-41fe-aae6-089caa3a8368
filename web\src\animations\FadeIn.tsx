"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface FadeInProps {
  children: ReactNode;
  duration?: number;
  delay?: number;
  className?: string;
  direction?: "up" | "down" | "left" | "right" | "none";
  distance?: number;
  once?: boolean;
  viewOffset?: number;
}

export default function FadeIn({
  children,
  duration = 0.5,
  delay = 0,
  direction = "up",
  distance = 30,
  className = "",
  once = true,
  viewOffset = 0.1,
}: FadeInProps) {
  let initialY = 0;
  let initialX = 0;

  // Set initial position based on direction
  if (direction === "up") initialY = distance;
  if (direction === "down") initialY = -distance;
  if (direction === "left") initialX = distance;
  if (direction === "right") initialX = -distance;

  return (
    <motion.div
      initial={{
        y: initialY,
        x: initialX,
        opacity: 0,
      }}
      whileInView={{
        y: 0,
        x: 0,
        opacity: 1,
      }}
      transition={{
        duration: duration,
        delay: delay,
        ease: "easeOut",
      }}
      viewport={{
        once,
        amount: viewOffset,
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
