import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  Headers,
  RawBodyRequest,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { BillingService } from './billing.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { CreatePaymentIntentDto } from './dto/create-payment-intent.dto';
import { ProcessPaymentDto } from './dto/process-payment.dto';
import { LoggerService } from 'src/logger/logger.service';
import { Request } from 'express';

// Extend the Express Request type to include user property
interface RequestWithUser extends Request {
  user: {
    userId: string;
    email: string;
    name?: string;
  };
}

@ApiTags('Billing')
@Controller('billing')
export class BillingController {
  constructor(
    private readonly billingService: BillingService,
    private readonly loggerService: LoggerService,
  ) {}

  @Post('payment-methods')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new payment method for the user\'s organization' })
  @ApiResponse({ status: 201, description: 'Payment method created successfully' })
  @ApiBody({ type: CreatePaymentMethodDto })
  async createPaymentMethod(@Req() req: RequestWithUser, @Body() createPaymentMethodDto: CreatePaymentMethodDto) {
    try {
      const userId = req.user.userId;

      // Get user to find their organization
      const user = await this.billingService.getUserWithOrganization(userId);

      if (!user.organizationId) {
        throw new HttpException(
          'User does not belong to an organization',
          HttpStatus.BAD_REQUEST
        );
      }

      return await this.billingService.createOrganizationPaymentMethod(
        user.organizationId.toString(),
        createPaymentMethodDto
      );
    } catch (error) {
      this.loggerService.error('Error creating payment method', error);
      throw new HttpException(
        error.message || 'Failed to create payment method',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('payment-methods')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all payment methods for the user\'s organization' })
  @ApiResponse({ status: 200, description: 'List of payment methods' })
  async getPaymentMethods(@Req() req: RequestWithUser) {
    try {
      const userId = req.user.userId;

      // Get user to find their organization
      const user = await this.billingService.getUserWithOrganization(userId);

      if (!user.organizationId) {
        throw new HttpException(
          'User does not belong to an organization',
          HttpStatus.BAD_REQUEST
        );
      }

      return await this.billingService.getOrganizationPaymentMethods(user.organizationId.toString());
    } catch (error) {
      this.loggerService.error('Error retrieving payment methods', error);
      throw new HttpException(
        error.message || 'Failed to retrieve payment methods',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('payment-methods/:id/default')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Set a payment method as default for the user\'s organization' })
  @ApiResponse({ status: 200, description: 'Payment method set as default' })
  async setDefaultPaymentMethod(@Req() req: RequestWithUser, @Param('id') id: string) {
    try {
      const userId = req.user.userId;

      // Get user to find their organization
      const user = await this.billingService.getUserWithOrganization(userId);

      if (!user.organizationId) {
        throw new HttpException(
          'User does not belong to an organization',
          HttpStatus.BAD_REQUEST
        );
      }

      return await this.billingService.setDefaultOrganizationPaymentMethod(
        user.organizationId.toString(),
        id
      );
    } catch (error) {
      this.loggerService.error('Error setting default payment method', error);
      throw new HttpException(
        error.message || 'Failed to set default payment method',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('payment-methods/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove a payment method from the user\'s organization' })
  @ApiResponse({ status: 200, description: 'Payment method removed successfully' })
  async removePaymentMethod(@Req() req: RequestWithUser, @Param('id') id: string) {
    try {
      const userId = req.user.userId;

      // Get user to find their organization
      const user = await this.billingService.getUserWithOrganization(userId);

      if (!user.organizationId) {
        throw new HttpException(
          'User does not belong to an organization',
          HttpStatus.BAD_REQUEST
        );
      }

      return await this.billingService.removeOrganizationPaymentMethod(
        user.organizationId.toString(),
        id
      );
    } catch (error) {
      this.loggerService.error('Error removing payment method', error);
      throw new HttpException(
        error.message || 'Failed to remove payment method',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('payment-intent')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a payment intent for the user\'s organization' })
  @ApiResponse({ status: 201, description: 'Payment intent created successfully' })
  @ApiBody({ type: CreatePaymentIntentDto })
  async createPaymentIntent(@Req() req: RequestWithUser, @Body() createPaymentIntentDto: CreatePaymentIntentDto) {
    try {
      const userId = req.user.userId;
      const email = req.user.email;
      const name = req.user.name;

      // Get user to find their organization
      const user = await this.billingService.getUserWithOrganization(userId);

      if (!user.organizationId) {
        throw new HttpException(
          'User does not belong to an organization',
          HttpStatus.BAD_REQUEST
        );
      }

      return await this.billingService.createOrganizationPaymentIntent(
        user.organizationId.toString(),
        userId,
        email,
        createPaymentIntentDto,
        name,
      );
    } catch (error) {
      this.loggerService.error('Error creating payment intent', error);
      throw new HttpException(
        error.message || 'Failed to create payment intent',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('transactions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get transaction history for the user\'s organization' })
  @ApiResponse({ status: 200, description: 'Transaction history' })
  async getTransactionHistory(
    @Req() req: RequestWithUser,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    try {
      const userId = req.user.userId;

      // Get user to find their organization
      const user = await this.billingService.getUserWithOrganization(userId);

      if (!user.organizationId) {
        throw new HttpException(
          'User does not belong to an organization',
          HttpStatus.BAD_REQUEST
        );
      }

      return await this.billingService.getOrganizationTransactionHistory(
        user.organizationId.toString(),
        page,
        limit
      );
    } catch (error) {
      this.loggerService.error('Error retrieving transaction history', error);
      throw new HttpException(
        error.message || 'Failed to retrieve transaction history',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('process-payment')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Process a payment directly (server-side) for the user\'s organization' })
  @ApiResponse({ status: 201, description: 'Payment processed successfully' })
  @ApiBody({ type: ProcessPaymentDto })
  async processPayment(@Req() req: RequestWithUser, @Body() processPaymentDto: ProcessPaymentDto) {
    try {
      const userId = req.user.userId;
      const email = req.user.email;
      const name = req.user.name;

      // Get user to find their organization
      const user = await this.billingService.getUserWithOrganization(userId);

      if (!user.organizationId) {
        throw new HttpException(
          'User does not belong to an organization',
          HttpStatus.BAD_REQUEST
        );
      }

      const {
        paymentMethodId,
        amount,
        currency,
        description,
        savePaymentMethod,
        setAsDefault
      } = processPaymentDto;

      return await this.billingService.processOrganizationPayment(
        user.organizationId.toString(),
        userId,
        email,
        paymentMethodId,
        {
          amount,
          currency,
          description,
        },
        name,
        savePaymentMethod,
        setAsDefault
      );
    } catch (error) {
      this.loggerService.error('Error processing payment', error);
      throw new HttpException(
        error.message || 'Failed to process payment',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


}
