events {
  worker_connections 1024;
}

http {
  upstream backend {
<<<<<<< HEAD
    server app_orova_nestjs_backend:4000;
  }

  upstream frontend {
    server app_orova_nextjs_frontend:3000;
  }

  upstream webhook {
    server app_orova_webhook:5000;
=======
    server demo_orova_nestjs_backend:4000;
  }

  upstream frontend {
    server demo_orova_nextjs_frontend:3000;
  }

  upstream webhook {
    server demo_orova_webhook:5000;
>>>>>>> origin/demo
  }

  server {
    listen 80;
    server_name app.orova.ai;

    return 301 https://$host$request_uri;
  }

  # HTTPS Server
  server {
    listen 443 ssl;
    server_name app.orova.ai;

    ssl_certificate /etc/letsencrypt/live/orova.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/orova.ai/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    location / {
      proxy_pass http://frontend;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api {
      rewrite ^/api$ /api/ permanent;
      proxy_pass http://backend;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /webhook {
      proxy_pass http://webhook;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
  }
}

