import { UsersService } from './users.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { AutoRechargeService } from '../credit/auto-recharge.service';
interface RequestWithUser extends Request {
    user: {
        userId: string;
        email: string;
        name?: string;
        role?: string;
    };
}
declare class AutoRechargeSettingsDto {
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
}
export declare class UsersController {
    private usersService;
    private autoRechargeService;
    constructor(usersService: UsersService, autoRechargeService: AutoRechargeService);
    register(userDto: {
        email: string;
        password: string;
        fullName: string;
    }): Promise<string>;
    approveUser(id: string): Promise<{
        message: string;
    }>;
    getAllUsers(): Promise<import("./interfaces/user.interface").User[]>;
    getUserById(id: string): Promise<import("./interfaces/user.interface").UserDocument>;
    updateUser(id: string, updateDto: UpdateUserDto): Promise<{
        message: string;
    }>;
    deleteUser(id: string): Promise<{
        message: string;
    }>;
    getUserCredits(req: RequestWithUser): Promise<{
        freeCreditsRemaining: number;
        paidCredits: number;
        totalAvailable: number;
        usingFreeCredits: boolean;
        freeMinutesRemaining: number;
        paidMinutes: number;
        totalMinutesAvailable: number;
        callPricePerMinute: number;
        monthlyResetDate: number;
        monthlyAllowance: number;
        minimumCreditsThreshold: number;
        credits: number;
        minutes: number;
    }>;
    getAutoRechargeSettings(req: RequestWithUser): Promise<{
        autoRechargeEnabled: boolean;
        autoRechargeThreshold: number;
        autoRechargeAmount: number;
    }>;
    updateAutoRechargeSettings(req: RequestWithUser, updateDto: AutoRechargeSettingsDto): Promise<{
        message: string;
        settings: {
            autoRechargeEnabled: boolean;
            autoRechargeThreshold: number;
            autoRechargeAmount: number;
        };
    }>;
    testAutoRecharge(req: RequestWithUser): Promise<{
        message: string;
        beforeTest: {
            autoRechargeEnabled: boolean;
            autoRechargeThreshold: number;
            autoRechargeAmount: number;
            currentCredits: number;
        };
        afterTest: {
            autoRechargeEnabled: boolean;
            autoRechargeThreshold: number;
            autoRechargeAmount: number;
            currentCredits: number;
        };
        autoRechargeTriggered: boolean;
    }>;
}
export {};
