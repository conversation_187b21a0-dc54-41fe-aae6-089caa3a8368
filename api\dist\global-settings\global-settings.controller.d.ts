import { GlobalSettingsService } from './global-settings.service';
import { UpdateGlobalSettingsDto } from './dto/update-global-settings.dto';
interface RequestWithUser extends Request {
    user: {
        userId: string;
        email: string;
        role: string;
    };
}
export declare class GlobalSettingsController {
    private readonly globalSettingsService;
    constructor(globalSettingsService: GlobalSettingsService);
    getGlobalSettings(): Promise<import("./interfaces/global-settings.interface").GlobalSettingsDocument>;
    updateGlobalSettings(updateGlobalSettingsDto: UpdateGlobalSettingsDto, req: RequestWithUser): Promise<import("./interfaces/global-settings.interface").GlobalSettingsDocument>;
}
export {};
