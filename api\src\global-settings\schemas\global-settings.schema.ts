import { Schema } from 'mongoose';

export const GlobalSettingsSchema = new Schema({
  // System settings (billing settings moved to organization level)
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastUpdatedBy: { type: String }, // User ID who last updated settings
});

GlobalSettingsSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});
