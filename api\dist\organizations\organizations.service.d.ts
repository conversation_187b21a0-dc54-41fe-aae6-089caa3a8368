import { OnModuleInit } from '@nestjs/common';
import { Model } from 'mongoose';
import { Organization, OrganizationDocument } from './interfaces/organization.interface';
import { CreateOrganizationDto, UpdateOrganizationDto, UpdateOrganizationBillingDto } from './dto/organization.dto';
import { UsersService } from '../users/users.service';
import { ModuleRef } from '@nestjs/core';
import { MonthlyCreditsService } from './monthly-credits.service';
import { NotificationsService } from '../notifications/notifications.service';
export declare class OrganizationsService implements OnModuleInit {
    private organizationModel;
    private usersService;
    private moduleRef;
    private monthlyCreditsService;
    private notificationsService;
    private websocketGateway;
    private creditGateway;
    constructor(organizationModel: Model<OrganizationDocument>, usersService: UsersService, moduleRef: ModuleRef, monthlyCreditsService: MonthlyCreditsService, notificationsService: NotificationsService);
    onModuleInit(): void;
    create(createOrganizationDto: CreateOrganizationDto): Promise<Organization>;
    findAll(): Promise<Organization[]>;
    findOne(id: string): Promise<Organization>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto): Promise<Organization>;
    updateBilling(id: string, updateBillingDto: UpdateOrganizationBillingDto): Promise<Organization>;
    addCredits(id: string, amount: number): Promise<Organization>;
    deductCredits(id: string, amount: number): Promise<Organization>;
    getAvailableCredits(id: string): Promise<{
        freeCreditsRemaining: number;
        paidCredits: number;
        totalAvailable: number;
        usingFreeCredits: boolean;
    }>;
    addUserToOrganization(organizationId: string, userId: string, isAdmin?: boolean): Promise<Organization>;
    removeUserFromOrganization(organizationId: string, userId: string): Promise<Organization>;
    delete(id: string): Promise<void>;
    findByUser(userId: string): Promise<Organization[]>;
    findByAdmin(adminId: string): Promise<Organization[]>;
    setStripeCustomerId(id: string, stripeCustomerId: string): Promise<Organization>;
    initializeMonthlyCreditsForOrganization(id: string): Promise<Organization>;
    updateSettings(id: string, updateSettingsDto: any): Promise<Organization>;
    updateAllOrganizationsMonthlyCredits(newMonthlyMinutesAllowance: number): Promise<void>;
}
