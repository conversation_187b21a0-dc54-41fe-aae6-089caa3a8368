import { Modu<PERSON> } from '@nestjs/common';
import { LoggerService } from './logger.service';
import { MongooseModule } from '@nestjs/mongoose';
import { LogSchema } from './schemas/log.schema';
import { LogsController } from './logger.controller';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'Log', schema: LogSchema }]),
  ],
  controllers: [LogsController],
  providers: [LoggerService],
  exports: [LoggerService],
})
export class LoggerModule {}
