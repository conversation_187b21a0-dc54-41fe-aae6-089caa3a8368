export declare class CreateOrganizationDto {
    name: string;
    description?: string;
    status?: 'active' | 'inactive' | 'suspended';
    adminUsers?: string[];
}
export declare class UpdateOrganizationDto {
    name?: string;
    description?: string;
    status?: 'active' | 'inactive' | 'suspended';
    adminUsers?: string[];
    users?: string[];
}
export declare class UpdateOrganizationBillingDto {
    credits?: number;
    autoRechargeEnabled?: boolean;
    autoRechargeThreshold?: number;
    autoRechargeAmount?: number;
    callPricePerMinute?: number;
    minimumCreditsThreshold?: number;
    monthlyMinutesAllowance?: number;
    monthlyResetDate?: number;
}
