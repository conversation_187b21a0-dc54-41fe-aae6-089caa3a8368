import { authFetch } from '@/lib/authFetch';

const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';

export interface OrganizationSettings {
  monthlyResetDate: number; // Day of month (1-28) when credits reset
  fullName?: string; // Client's full name for email personalization
  email?: string; // Email address to send credit notifications to
}

export interface UpdateOrganizationSettingsParams {
  monthlyResetDate?: number;
  fullName?: string;
  email?: string;
}

/**
 * Update organization settings
 */
export const updateOrganizationSettings = async (
  organizationId: string,
  params: UpdateOrganizationSettingsParams
): Promise<OrganizationSettings> => {
  const response = await authFetch(`${API_URL}/api/organizations/${organizationId}/settings`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update organization settings');
  }

  return response.json();
};
