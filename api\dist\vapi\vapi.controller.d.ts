import { Response } from "express";
import { VapiService } from "./vapi.service";
import { UsersService } from "src/users/users.service";
export declare class VapiController {
    private readonly vapiService;
    private readonly usersService;
    constructor(vapiService: VapiService, usersService: UsersService);
    callContacts(payload: {
        contacts: any[];
        agentId: string;
        region: string;
    }, req: any, res: Response): Promise<Response<any, Record<string, any>>>;
    vapiWebhook(body: any, res: Response): Promise<Response<any, Record<string, any>>>;
}
