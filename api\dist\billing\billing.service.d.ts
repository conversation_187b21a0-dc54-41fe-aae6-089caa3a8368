import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { OrganizationPaymentMethod, OrganizationPaymentMethodDocument } from './schemas/organization-payment-method.schema';
import { OrganizationTransaction, OrganizationTransactionDocument } from './schemas/organization-transaction.schema';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { CreatePaymentIntentDto } from './dto/create-payment-intent.dto';
import { LoggerService } from 'src/logger/logger.service';
import { UsersService } from 'src/users/users.service';
import { OrganizationsService } from 'src/organizations/organizations.service';
export declare class BillingService {
    private orgPaymentMethodModel;
    private orgTransactionModel;
    private configService;
    private loggerService;
    private usersService;
    private organizationsService;
    private stripe;
    constructor(orgPaymentMethodModel: Model<OrganizationPaymentMethodDocument>, orgTransactionModel: Model<OrganizationTransactionDocument>, configService: ConfigService, loggerService: LoggerService, usersService: UsersService, organizationsService: OrganizationsService);
    createStripeCustomer(userId: string, email: string, name?: string): Promise<Stripe.Response<Stripe.Customer>>;
    getOrCreateStripeCustomer(userId: string, email: string, name?: string): Promise<Stripe.Customer>;
    createStripeOrganizationCustomer(organizationId: string, email: string, name?: string): Promise<Stripe.Response<Stripe.Customer>>;
    getOrCreateStripeOrganizationCustomer(organizationId: string, email: string, name?: string): Promise<Stripe.Response<Stripe.Customer>>;
    getUserWithOrganization(userId: string): Promise<import("../users/interfaces/user.interface").UserDocument>;
    createOrganizationPaymentIntent(organizationId: string, userId: string, email: string, createPaymentIntentDto: CreatePaymentIntentDto, name?: string): Promise<{
        clientSecret: string;
        paymentIntentId: string;
        status: Stripe.PaymentIntent.Status;
    }>;
    createOrganizationPaymentMethod(organizationId: string, createPaymentMethodDto: CreatePaymentMethodDto): Promise<import("mongoose").Document<unknown, {}, OrganizationPaymentMethodDocument> & OrganizationPaymentMethod & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    getOrganizationPaymentMethods(organizationId: string): Promise<(import("mongoose").Document<unknown, {}, OrganizationPaymentMethodDocument> & OrganizationPaymentMethod & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    setDefaultOrganizationPaymentMethod(organizationId: string, paymentMethodId: string): Promise<import("mongoose").Document<unknown, {}, OrganizationPaymentMethodDocument> & OrganizationPaymentMethod & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    removeOrganizationPaymentMethod(organizationId: string, paymentMethodId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    processOrganizationPayment(organizationId: string, userId: string, email: string, paymentMethodId: string, paymentDetails: {
        amount: number;
        currency: string;
        description?: string;
    }, name: string, savePaymentMethod?: boolean, setAsDefault?: boolean): Promise<{
        success: boolean;
        status: Stripe.PaymentIntent.Status;
        transactionId: unknown;
        paymentIntentId: string;
        paymentMethodId: any;
        clientSecret: string;
        savedPaymentMethod: boolean;
    }>;
    getOrganizationTransactionHistory(organizationId: string, page?: number, limit?: number): Promise<{
        transactions: (import("mongoose").Document<unknown, {}, OrganizationTransactionDocument> & OrganizationTransaction & import("mongoose").Document<unknown, any, any> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        pagination: {
            total: number;
            page: number;
            limit: number;
            pages: number;
        };
    }>;
}
