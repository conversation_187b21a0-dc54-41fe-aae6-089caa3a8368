import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { UserSchema } from './schemas/user.schema';
import { GlobalSettingsModule } from '../global-settings/global-settings.module';
import { OrganizationsModule } from '../organizations/organizations.module';
import { CreditModule } from '../credit/credit.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'User', schema: UserSchema }]),
    GlobalSettingsModule,
    forwardRef(() => OrganizationsModule),
    forwardRef(() => CreditModule),
  ],
  providers: [UsersService],
  controllers: [UsersController],
  exports: [UsersService],
})
export class UsersModule {}