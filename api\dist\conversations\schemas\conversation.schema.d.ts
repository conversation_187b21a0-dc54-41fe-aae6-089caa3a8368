import { Schema } from 'mongoose';
export declare const MessageSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    role: "user" | "assistant";
    timestamp: NativeDate;
    content: string;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    role: "user" | "assistant";
    timestamp: NativeDate;
    content: string;
}>> & import("mongoose").FlatRecord<{
    role: "user" | "assistant";
    timestamp: NativeDate;
    content: string;
}> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export declare const ConversationSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    type: "call" | "chat";
    status: "active" | "ended";
    userId: import("mongoose").Types.ObjectId;
    messages: import("mongoose").Types.DocumentArray<{
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }> & {
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }>;
    agentId: import("mongoose").Types.ObjectId;
    startedAt: NativeDate;
    endedAt?: NativeDate;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    type: "call" | "chat";
    status: "active" | "ended";
    userId: import("mongoose").Types.ObjectId;
    messages: import("mongoose").Types.DocumentArray<{
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }> & {
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }>;
    agentId: import("mongoose").Types.ObjectId;
    startedAt: NativeDate;
    endedAt?: NativeDate;
}>> & import("mongoose").FlatRecord<{
    type: "call" | "chat";
    status: "active" | "ended";
    userId: import("mongoose").Types.ObjectId;
    messages: import("mongoose").Types.DocumentArray<{
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }> & {
        role: "user" | "assistant";
        timestamp: NativeDate;
        content: string;
    }>;
    agentId: import("mongoose").Types.ObjectId;
    startedAt: NativeDate;
    endedAt?: NativeDate;
}> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
