import { Model } from 'mongoose';
import { AiService } from '../ai/ai.service';
import { ConversationDocument } from './interfaces/conversation.interface';
export declare class ConversationsService {
    private readonly conversationModel;
    private readonly aiService;
    constructor(conversationModel: Model<ConversationDocument>, aiService: AiService);
    startConversation(agentId: string, userId: string, type: 'chat' | 'call'): Promise<import("mongoose").Document<unknown, {}, ConversationDocument> & import("./interfaces/conversation.interface").Conversation & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    getConversationById(id: string): Promise<import("mongoose").Document<unknown, {}, ConversationDocument> & import("./interfaces/conversation.interface").Conversation & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    addUserMessage(conversationId: string, message: string): Promise<string>;
    endConversation(conversationId: string): Promise<import("mongoose").Document<unknown, {}, ConversationDocument> & import("./interfaces/conversation.interface").Conversation & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
}
