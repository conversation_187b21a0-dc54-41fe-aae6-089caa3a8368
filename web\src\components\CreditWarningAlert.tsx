"use client";

import { <PERSON><PERSON><PERSON>riangle, X } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useCredits } from "@/contexts/CreditContext";
import { useState, useEffect } from "react";

interface CreditWarningAlertProps {
  credits?: number;
  threshold?: number;
}

export function CreditWarningAlert({ credits: propCredits, threshold: propThreshold }: CreditWarningAlertProps) {
  const router = useRouter();
  const {
    totalAvailable,
    organizationCreditThreshold,
    isLoading,
    totalMinutesAvailable,
    callPricePerMinute,
    monthlyAllowance,
    isConnected,
    hasValidData,
    lastSuccessfulFetch
  } = useCredits();

  const [isDismissed, setIsDismissed] = useState(false);

  // Use props if provided, otherwise use context values
  const credits = propCredits !== undefined ? propCredits : totalAvailable;
  // Use threshold prop if provided, otherwise use organization threshold from context
  const threshold = propThreshold !== undefined ? propThreshold : organizationCreditThreshold;

  // Warning threshold is 2x the minimum threshold
  const warningThreshold = threshold * 2;

  // Check if this warning was dismissed for this threshold level
  useEffect(() => {
    const dismissedKey = `credit_warning_dismissed_${warningThreshold}`;
    const dismissedData = localStorage.getItem(dismissedKey);

    let isDismissedAndValid = false;
    if (dismissedData) {
      try {
        const { timestamp } = JSON.parse(dismissedData);
        const twoHoursInMs = 2 * 60 * 60 * 1000; // 2 hours in milliseconds
        const now = Date.now();

        // Check if dismissal is still valid (less than 2 hours old)
        isDismissedAndValid = (now - timestamp) < twoHoursInMs;

        // If expired, remove from localStorage
        if (!isDismissedAndValid) {
          localStorage.removeItem(dismissedKey);
        }
      } catch {
        // If parsing fails, remove the invalid entry
        localStorage.removeItem(dismissedKey);
      }
    }

    setIsDismissed(isDismissedAndValid);

    // Debug logging
    console.log('CreditWarningAlert Debug:', {
      credits,
      threshold,
      warningThreshold,
      isDismissed: isDismissedAndValid,
      dismissedKey,
      dismissedData,
      shouldShow: !isLoading && !isDismissedAndValid && credits < warningThreshold && credits >= threshold
    });
  }, [warningThreshold, credits, threshold, isLoading]);

  // Don't show anything while loading
  if (isLoading) {
    console.log('CreditWarningAlert: Not showing - still loading');
    return null;
  }

  // Don't show alert if we don't have valid data or connection issues
  if (!hasValidData) {
    console.log('CreditWarningAlert: Not showing - no valid data');
    return null;
  }

  // If connection is lost and we have recent data, don't show scary alerts
  const isRecentData = lastSuccessfulFetch && (Date.now() - lastSuccessfulFetch < 2 * 60 * 1000); // 2 minutes
  if (!isConnected && !isRecentData) {
    console.log('CreditWarningAlert: Not showing - connection lost and data is stale');
    return null;
  }

  // Don't show if dismissed
  if (isDismissed) {
    console.log('CreditWarningAlert: Not showing - dismissed');
    return null;
  }

  // Only show the warning if credits are below warning threshold but above critical threshold
  if (credits >= warningThreshold || credits < threshold) {
    console.log('CreditWarningAlert: Not showing - outside threshold range', {
      credits,
      threshold,
      warningThreshold,
      belowWarning: credits < warningThreshold,
      aboveCritical: credits >= threshold
    });
    return null;
  }

  console.log('CreditWarningAlert: Showing warning alert');

  // Convert credits to minutes for display
  const minutesRemaining = callPricePerMinute > 0 ? credits / callPricePerMinute : 0;
  const hasMonthlyAllowance = monthlyAllowance > 0;

  const handleDismiss = () => {
    const dismissedKey = `credit_warning_dismissed_${warningThreshold}`;
    const dismissalData = {
      timestamp: Date.now(),
      warningThreshold
    };
    localStorage.setItem(dismissedKey, JSON.stringify(dismissalData));
    setIsDismissed(true);
  };

  return (
    <div className="flex justify-center mb-3">
      <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/10 dark:border-yellow-800 w-auto p-2">
        <AlertDescription className="text-yellow-800 dark:text-yellow-200">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
            <div>
              <span className="text-sm font-medium">
                {hasMonthlyAllowance
                  ? `${Math.floor(totalMinutesAvailable)} minutes remaining`
                  : `$${credits.toFixed(2)} balance (${Math.floor(minutesRemaining)} min)`
                }
              </span>
              <span className="text-xs text-yellow-600 dark:text-yellow-400 ml-2">
                Credits running low - consider adding funds soon
              </span>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                size="sm"
                variant="outline"
                className="h-7 px-3 text-xs border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/20"
                onClick={() => router.push('/billing')}
              >
                Add Funds
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0 text-yellow-600 hover:bg-yellow-100 dark:text-yellow-400 dark:hover:bg-yellow-900/20"
                onClick={handleDismiss}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
}
