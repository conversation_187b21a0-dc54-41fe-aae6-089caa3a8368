{"version": 3, "file": "ai.service.js", "sourceRoot": "", "sources": ["../../src/ai/ai.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,yCAA4C;AAC5C,+BAAsC;AACtC,0DAAsD;AACtD,0DAAiC;AAG1B,IAAM,SAAS,GAAf,MAAM,SAAS;IACpB,YACmB,WAAwB,EACxB,aAA2B;QAD3B,gBAAW,GAAX,WAAW,CAAa;QACxB,kBAAa,GAAb,aAAa,CAAc;IAC3C,CAAC;IAKJ,KAAK,CAAC,CAAC,4BAA4B,CACjC,OAAe,EACf,WAAmB,EACnB,sBAAgE,EAAE;QAElE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;QACjD,CAAC;QAGD,MAAM,cAAc,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAGrD,MAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,EAAE,QAAQ,IAAI,SAAS,CAAC;QAC5D,MAAM,aAAa,GACjB,KAAK,CAAC,MAAM;YACZ,WAAW,KAAK,CAAC,IAAI,uBAAuB,QAAQ,GAAG,CAAC;QAE1D,MAAM,QAAQ,GAAG;YACf,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE;YAC1C,GAAG,cAAc;YACjB,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE;SACvC,CAAC;QAGF,MAAM,wBAAwB,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QAC3E,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,QAAQ,CACzD,KAAK,CAAC,aAAa,CAAC,KAAK,CAC1B,CAAC;QAEF,MAAM,OAAO,GAAG;YACd,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK;YAChC,QAAQ;YACR,WAAW,EAAE,KAAK,CAAC,aAAa,CAAC,WAAW;YAC5C,MAAM,EAAE,iBAAiB;SAC1B,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CACrC,4CAA4C,EAC5C,OAAO,EACP;YACE,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,aAAa,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;aACtD;YACD,YAAY,EAAE,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;SACpD,CACF,CAAC;QAEF,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EAAC,SAAS,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC7B,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACjC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBAC3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBACrC,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;4BACzB,OAAO;wBACT,CAAC;wBACD,IAAI,CAAC;4BACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;4BACnC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC;4BAC/C,IAAI,KAAK,EAAE,CAAC;gCACV,MAAM,KAAK,CAAC;4BACd,CAAC;wBACH,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAC;wBAC5C,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EAAC,SAAS,CAAC,CAAC;YACjD,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QACjD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,OAAe,EACf,WAAmB,EACnB,sBAAgE,EAAE;QAElE,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,4BAA4B,CACzD,OAAO,EACP,WAAW,EACX,mBAAmB,CACpB,EAAE,CAAC;YACF,YAAY,IAAI,KAAK,CAAC;QACxB,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAMD,KAAK,CAAC,eAAe,CACnB,QAAgB,EAChB,WAAmB,EACnB,OAA+B;QAE/B,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,IAAI,mBAAQ,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE;gBAC/B,QAAQ,EAAE,YAAY;gBACtB,WAAW,EAAE,YAAY;aAC1B,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAGlC,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gDAAgD,EAAE,IAAI,EAAE;gBAC5E,OAAO,EAAE;oBACP,GAAG,IAAI,CAAC,UAAU,EAAE;oBACpB,aAAa,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;iBACtD;aACF,CAAC,CACH,CAAC;YACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QAC5B,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;IAOD,KAAK,CAAC,YAAY,CAChB,QAAgB,EAChB,IAAY,EACZ,gBAAqB,EAAE;QAEvB,IAAI,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,OAAO,GACX,aAAa,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QACD,MAAM,GAAG,GAAG,+CAA+C,OAAO,EAAE,CAAC;QAErE,MAAM,OAAO,GAAG;YACd,IAAI;YACJ,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,uBAAuB;YAC3D,cAAc,EAAE;gBACd,SAAS,EAAE,aAAa,CAAC,SAAS,IAAI,GAAG;gBACzC,gBAAgB,EAAE,aAAa,CAAC,eAAe,IAAI,IAAI;gBAEvD,YAAY,EAAE,aAAa,CAAC,WAAW,IAAI,CAAC;gBAC5C,cAAc,EAAE,aAAa,CAAC,cAAc,IAAI,GAAG;gBACnD,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,UAAU;gBAC9C,wBAAwB,EAAE,aAAa,CAAC,uBAAuB,IAAI,GAAG;gBACtE,gBAAgB,EAAE,aAAa,CAAC,eAAe,IAAI,QAAQ;aAC5D;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE;gBAClC,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;oBAC5C,MAAM,EAAE,YAAY;iBACrB;gBACD,YAAY,EAAE,aAAa;gBAC3B,OAAO,EAAE,KAAK;aACf,CAAC,CACH,CAAC;YACF,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;CACF,CAAA;AApMY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;qCAGqB,mBAAW;QACT,4BAAY;GAHnC,SAAS,CAoMrB"}