import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Zap } from "lucide-react";

export default function PricingSection() {
  return (
    <section className="py-20">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 className="text-3xl font-bold text-center mb-12">
        Simple, Transparent Pricing
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Basic Plan */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-2">Starter</h3>
            <div className="text-4xl font-bold mb-4">$29</div>
            <p className="text-gray-600 mb-6">Perfect for getting started</p>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>3 AI Agents</span>
              </li>
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>Basic Templates</span>
              </li>
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>Community Support</span>
              </li>
            </ul>
            <Button className="w-full">Get Started</Button>
          </CardContent>
        </Card>
        {/* Pro Plan */}
        <Card className="border-[#a855f7] shadow-lg">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-2">Professional</h3>
            <div className="text-4xl font-bold mb-4">$99</div>
            <p className="text-gray-600 mb-6">For growing teams</p>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>Unlimited Agents</span>
              </li>
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>All Templates</span>
              </li>
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>Priority Support</span>
              </li>
            </ul>
            <Button
              className="w-full bg-gradient-to-r from-[#4157ea] to-[#a855f7] text-white"
            >
              Get Started
            </Button>
          </CardContent>
        </Card>
        {/* Enterprise Plan */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-2">Enterprise</h3>
            <div className="text-4xl font-bold mb-4">Custom</div>
            <p className="text-gray-600 mb-6">For large organizations</p>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>Custom Solutions</span>
              </li>
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>Dedicated Support</span>
              </li>
              <li className="flex items-center">
                <Zap className="h-5 w-5 text-green-500 mr-2" />
                <span>SLA Guarantee</span>
              </li>
            </ul>
            <Button variant="outline" className="w-full">
              Contact Sales
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  </section>
  )
}
