"use client";

import { useState } from "react";
import Link from "next/link";
import {  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@/components/ui/card";
import { ArrowLeft, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function ForgotPassword() {
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call for password reset
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitted(true);
      // Don't redirect - we'll show a success message instead
    }, 1500);
  };

  return (
    <>
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-xl">
        <CardHeader className="space-y-2">
          <CardTitle className="text-center text-3xl font-bold bg-gradient-to-r from-[#383D73] to-[#74546D] bg-clip-text text-transparent">
            Reset Password
          </CardTitle>
          <p className="text-center text-gray-500 dark:text-gray-400">
            {!isSubmitted 
              ? "Enter your email and we'll send you a link to reset your password" 
              : "Check your email for a reset link"}
          </p>
        </CardHeader>
        <CardContent>
          {!isSubmitted ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Input
                  type="email"
                  placeholder="Email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="h-12"
                />
              </div>
              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-[#383D73] to-[#74546D] hover:from-[#312E56] hover:to-[#BE8FB8] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2" />
                    Sending...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <Send className="mr-2 h-5 w-5" />
                    Send Reset Link
                  </div>
                )}
              </Button>
            </form>
          ) : (
            <div className="text-center py-6 space-y-4">
              <div className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 p-4 rounded-md">
                <p>We have sent a password reset link to:</p>
                <p className="font-medium">{email}</p>
                <p className="text-sm mt-2">Please check your inbox and follow the instructions.</p>
              </div>
              <Button
                className="mt-4 bg-transparent text-[#383D73] dark:text-[#BE8FB8] hover:bg-gray-100 dark:hover:bg-gray-700"
                variant="ghost"
                onClick={() => {
                  setEmail("");
                  setIsSubmitted(false);
                }}
              >
                Try another email
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center border-t border-gray-100 dark:border-gray-700 pt-4">
          <Link href="/login">
            <Button 
              variant="ghost" 
              className="text-[#383D73] dark:text-[#BE8FB8] hover:bg-transparent hover:text-[#74546D] transition-all duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sign In
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
    </>
  );
}