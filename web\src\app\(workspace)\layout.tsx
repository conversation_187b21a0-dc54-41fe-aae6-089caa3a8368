"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { useTheme } from "next-themes";
import {LayoutDashboard,Bot,Settings,ScrollText,Brain,MessageSquare,Sun,Moon,Loader2,ChartLine,Users,Calendar,Building2,Wallet} from "lucide-react";
import LogoBlack from "@/assets/img/OROVA-PURPLE.png";
import LogoWhite from "@/assets/img/OROVA-WHITE.png";
import { ProfileMenu } from "@/components/ProfileMenu";
import FadeIn from "@/animations/FadeIn";
import { UserInfo } from "../(auth)/actions/auth";
import { authFetch } from "@/lib/authFetch";
import { setupTokenRefresh } from "@/lib/auth-client";
import ScaleIn from "@/animations/ScaleIn";
import { CreditProvider } from "@/contexts/CreditContext";
import { CreditDisplay } from "@/components/CreditDisplay";
// import { LanguageSwitcher } from "@/components/LanguageSwitcher";

const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

export default function WorkspaceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [settingsExpanded, setSettingsExpanded] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Add state for user data
  const [user, setUser] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch user data when component mounts
  useEffect(() => {
    async function fetchUserData() {
      try {
        // Use authFetch instead of regular fetch
        const response = await authFetch(`${API_BASE_URL}/api/auth/me`);

        if (!response.ok) {
          router.push("/login");
          return;
        }

        const userData = await response.json();

        // More flexible check - look for any ID field and email
        const fullName = userData.fullName || userData.name;
        const userId = userData.userId || userData._id || userData.id;
        const email = userData.email;
        const organizationId = userData.organizationId;

        if (userId && email) {
          // Normalize user data structure
          const normalizedUserData = {
            fullName: fullName || email.split("@")[0],
            userId: userId,
            _id: userId, // Add _id for compatibility with CreditContext
            email: email,
            role: userData.role || "user",
            organizationId: organizationId || null,
          };

          // Store the complete user data in localStorage for other components to use
          localStorage.setItem('user_data', JSON.stringify(normalizedUserData));

          setUser(normalizedUserData);
        } else {
          router.push("/login");
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        router.push("/login");
      } finally {
        setLoading(false);
      }
    }

    fetchUserData();
  }, [router]);

  useEffect(() => {
    // Set up automatic token refresh every 50 minutes
    const cleanupTokenRefresh = setupTokenRefresh(50);

    // Cleanup function when component unmounts
    return cleanupTokenRefresh;
  }, []);

  // No need to fetch credits here anymore, it's handled by CreditContext

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    // Set initial state based on screen size
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleLinkClick = () => {
    // Only close the sidebar on mobile devices
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  // Format user data for the ProfileMenu component
  const userData = user
    ? {
        fullName: user.fullName || user.email.split("@")[0], // Ensure fullName is always a string
        name: user.email.split("@")[0], // Use part of email as name if no name provided
        email: user.email,
        avatar: "",
        role: user.role,
      }
    : {
        fullName: "Loading...", // Add fullName to match the expected type
        name: "Loading...",
        email: "",
        avatar: "",
        role: "",
      };

  // Navigation sections with grouped links
  const navigationSections = [
    {
      title: "Overview",
      links: [
        { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
        { name: "Agents", href: "/agents", icon: Bot },
        { name: "Campaigns", href: "/campaign", icon: ChartLine },
        { name: "History", href: "/history", icon: ScrollText },
        { name: "Schedule", href: "/schedule", icon: Calendar },
      ],
    },
    {
      title: "Resources",
      links: [
        { name: "Brain", href: "/brain", icon: Brain },
        { name: "Contacts", href: "/contacts", icon: MessageSquare },
      ],
    },
  ];

  // Configuration section moved to bottom
  const configSection = {
    title: "Configuration",
    links: [
      // Settings now becomes a parent with sublinks
      {
        name: "Settings",
        href: "/settings",
        icon: Settings,
        isParent: true,
        subLinks: [
          { name: "General", href: "/settings", icon: Settings },
          { name: "Users", href: "/users", icon: Users },
          { name: "Billing", href: "/billing", icon: Wallet },
          // { name: "Voices", href: "/voices", icon: Mic },
          // { name: "Phone Number", href: "/phonenumber", icon: Phone },
          // { name: "Integration", href: "/integration", icon: Plug },
          // Only show Organizations link for superadmins
          ...(user?.role === 'superadmin' ? [{ name: "Workspaces", href: "/workspaces", icon: Building2 }] : []),
        ],
      },
    ],
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Show loading state until mounted and user data is fetched
  if (!mounted || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-lg font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  return (
      <CreditProvider creditThreshold={1}>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Sidebar */}
        <div
          className={`fixed inset-y-0 left-0 z-550 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-500 ease-in-out ${
            sidebarOpen
              ? "w-64 translate-x-0"
              : "w-16 md:translate-x-0 -translate-x-full"
          }`}
        >
          <div className="flex-shrink-0">
            {/* Logo */}
            <div className="flex items-center justify-start h-16 px-4 border-b border-gray-200 dark:border-gray-700">
              <div
                className={`flex items-center  ${
                  sidebarOpen ? "gap-6 ml-3" : "justify-center w-full"
                }`}
              >
                <Link href="/">
                  {sidebarOpen && (
                    <ScaleIn delay={0.2}>
                      <Image
                        src={theme === "dark" ? LogoWhite : LogoBlack}
                        alt="Orova AI"
                        className="h-5 w-auto"
                      />
                    </ScaleIn>
                  )}

                  {!sidebarOpen && (
                    <ScaleIn delay={0.1}>
                      <div className="h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center text-white font-bold">
                        O
                      </div>
                    </ScaleIn>
                  )}
                </Link>
              </div>
            </div>
          </div>

          {/* Scrollable navigation area */}
          <div className="flex-grow overflow-hidden">
            {/* Navigation with Sections */}
            <nav className={`px-4 py-4 space-y-6 ${!sidebarOpen && "px-2"}`}>
              {navigationSections.map((section, index) => (
                <div key={index}>
                  {sidebarOpen && (
                    <h3 className="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold mb-2 px-3">
                      {section.title}
                    </h3>
                  )}
                  <div className="space-y-1">
                    {section.links.map((item) => {
                      const Icon = item.icon;
                      const isActive = pathname.includes(item.href);
                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          onClick={handleLinkClick}
                          className={`flex items-center ${
                            sidebarOpen ? "px-3" : "px-0 justify-center"
                          } py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ${
                            isActive ? "bg-gray-100 dark:bg-gray-700" : ""
                          }`}
                          title={!sidebarOpen ? item.name : ""}
                        >
                          <Icon className="h-5 w-5" />
                          {sidebarOpen && item.name}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              ))}
            </nav>
          </div>

          {/* Configuration Section - Fixed at bottom */}
          <div
            className={`flex-shrink-0 border-t border-gray-200 dark:border-gray-700 px-4 py-4 ${
              !sidebarOpen && "px-2"
            }`}
          >
            {/* Billing Card - Now inside configuration section */}

            {/* Credit Display Component */}
            {sidebarOpen && <CreditDisplay />}

            <div className="space-y-3">
              {configSection.links.map((item) => {
                const Icon = item.icon;
                const isActive = pathname.includes(item.href);
                const isSettingsActive =
                  item.isParent &&
                  (isActive ||
                    item.subLinks?.some((subLink) =>
                      pathname.includes(subLink.href)
                    ));

                return (
                  <div key={item.name}>
                    {item.isParent ? (
                      <>
                        <button
                          onClick={() => setSettingsExpanded(!settingsExpanded)}
                          className={`w-full cursor-pointer flex items-center ${
                            sidebarOpen
                              ? "justify-between px-3"
                              : "justify-center px-0"
                          } py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ${
                            isSettingsActive
                              ? "bg-gray-100 dark:bg-gray-700"
                              : ""
                          }`}
                          title={!sidebarOpen ? item.name : ""}
                        >
                          <div className="flex items-center gap-3">
                            <Icon className="h-5 w-5" />
                            {sidebarOpen && item.name}
                          </div>
                          {sidebarOpen && (
                            <svg
                              className={`w-4 h-4 transition-transform duration-300 ease-in-out ${
                                settingsExpanded ? "rotate-180" : "rotate-0"
                              }`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 9l-7 7-7-7"
                              />
                            </svg>
                          )}
                        </button>

                        {/* Sublinks - only visible when sidebar is expanded */}
                        {sidebarOpen && (
                          <div
                            className={`ml-8 space-y-1 mt-1 mb-2 overflow-hidden transition-all duration-700 ease-in-out ${
                              settingsExpanded
                                ? "max-h-[200px] opacity-100 transform-none"
                                : "max-h-0 opacity-0 transform translate-y-2"
                            }`}
                          >
                            {item.subLinks?.map((subLink) => {
                              const SubIcon = subLink.icon;
                              const isSubActive = pathname === subLink.href;

                              return (
                                <Link
                                  key={subLink.name}
                                  href={subLink.href}
                                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300  hover:translate-x-1 hover:scale-[1.02] ${
                                    isSubActive
                                      ? "bg-gray-100 dark:bg-gray-700"
                                      : ""
                                  }`}
                                >
                                  <SubIcon className="h-4 w-4" />
                                  {subLink.name}
                                </Link>
                              );
                            })}
                          </div>
                        )}
                      </>
                    ) : (
                      <Link
                        href={item.href}
                        className={`flex items-center ${
                          sidebarOpen ? "px-3" : "px-0 justify-center"
                        } py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300 hover:translate-x-1 hover:scale-[1.02] ${
                          isActive ? "bg-gray-100 dark:bg-gray-700" : ""
                        }`}
                        title={!sidebarOpen ? item.name : ""}
                      >
                        <Icon className="h-5 w-5" />
                        {sidebarOpen && item.name}
                      </Link>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Settings link (last item in config section) */}
            {/* {configSection.links.slice(3).map((item) => {
                const Icon = item.icon;
                const isActive = pathname.includes(item.href);
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200 hover:shadow-sm hover:translate-x-1 hover:scale-[1.02] ${
                      isActive ? "bg-gray-100 dark:bg-gray-700" : ""
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    {item.name}
                  </Link>
                );
              })} */}
            {/* </div> */}
          </div>
        </div>

        {/* Mobile Sidebar Toggle Button */}

        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-gray-800 bg-opacity-50 z-10 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <div
          className={`transition-all duration-300 ${
            sidebarOpen ? "md:pl-60 pl-0" : "md:pl-16 pl-0"
          }`}
        >
          {/* Header */}
          <header className="bg-white sticky top-0 z-500 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 w-full">
            <div className="h-16 px-4 md:px-8 flex items-center justify-between">
              {/* Left section with menu button and welcome message */}
              <div className="flex items-center space-x-4">
                {/* Hamburger menu button */}
                <button
                  onClick={toggleSidebar}
                  className="p-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </button>

                {/* Welcome message */}
                <h2 className="text-xl md:text-2xl font-bold text-gray-800 dark:text-gray-100 hidden sm:block">
                  Welcome Back, {user?.fullName || "Guest"}
                </h2>
              </div>

              {/* Right section with profile and theme controls */}
              <div className="flex items-center space-x-2">
                <ProfileMenu user={userData} />
                <div className="h-6 w-px bg-gray-200 dark:bg-gray-700" />
                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-lg text-gray-800 dark:text-white"
                >
                  {theme === "dark" ? <Sun size={18} /> : <Moon size={18} />}
                </button>
              </div>
            </div>
          </header>

          {/* Page Content */}
          <FadeIn>
            <main className="p-4 md:p-10">{children}</main>
          </FadeIn>
        </div>
      </div>
    </CreditProvider>
  );
}
