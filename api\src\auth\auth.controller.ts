import {
  Controller,
  Post,
  Body,
  UnauthorizedException,
  Get,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import {
  FullUserType,
  UserRedactionInterceptor,
} from './interceptors/user-redaction.interceptor';
import { User } from './decorators/user.decorator';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { LoginDto } from './dto/login.dto';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Get('/me')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(UserRedactionInterceptor)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get the currently logged in user' })
  @ApiResponse({ status: 200, description: 'Returns the current user' })
  async getLoggedInUser(@User() user: FullUserType) {
    return user;
  }

  @Post('login')
  @ApiOperation({ summary: 'Log in a user' })
  @ApiResponse({ status: 201, description: 'User successfully logged in' })
  @ApiBody({ type: LoginDto })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Post('refresh')
  @ApiOperation({ summary: 'getting access token' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        refreshToken: {
          type: 'string',
          example:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.zIlAv30VJBP8X225h4R1y2kkbpuNXwhAGOvw7RZQwRM',
        },
      },
      required: ['refreshToken'],
    },
  })
  async refresh(@Body() body: { refreshToken: string }) {
    return this.authService.refreshToken(body.refreshToken);
  }
}