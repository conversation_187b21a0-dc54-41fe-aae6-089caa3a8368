import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class OrganizationTransaction {
  @Prop({ required: true })
  organizationId: string;

  @Prop()
  userId?: string; // User who initiated the transaction

  @Prop({ required: true })
  amount: number;

  @Prop({ required: true })
  currency: string;

  @Prop({ required: true })
  status: string;

  @Prop()
  paymentMethodId?: string;

  @Prop({ required: true })
  stripePaymentIntentId: string;

  @Prop({ required: true })
  stripeCustomerId: string;

  @Prop()
  description?: string;

  @Prop({ required: true })
  email: string;

  @Prop({ type: Object })
  metadata?: Record<string, any>;
}

export type OrganizationTransactionDocument = OrganizationTransaction & Document;
export const OrganizationTransactionSchema = SchemaFactory.createForClass(OrganizationTransaction);
