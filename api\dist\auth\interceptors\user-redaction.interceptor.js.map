{"version": 3, "file": "user-redaction.interceptor.js", "sourceRoot": "", "sources": ["../../../src/auth/interceptors/user-redaction.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAK0B;AAExB,8CAAqC;AAYrC,SAAS,iBAAiB,CAAC,IAAkB;IAC3C,MAAM,QAAQ,GAA2B,EAAE,CAAC;IAE5C,SAAS,OAAO,CAAC,GAA2B;QAC1C,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;YACtB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;gBACnD,IACE,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ;oBAC5B,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC;oBAC3B,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI;oBACjB,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EACxB,CAAC;oBACD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACpB,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,CAAC,IAAI,CAAC,CAAC;IACd,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,aAAa,GAAG,CAAC,IAAkB,EAAE,EAAE;IAC3C,MAAM,QAAQ,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzC,MAAM,EACJ,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,SAAS,EACT,GAAG,IAAI,EACR,GAAG,QAAQ,CAAC;IACb,OAAO;QACL,GAAG,IAAI;QACP,QAAQ,EAAE,QAAQ,CAAC,QAAQ;KAC5B,CAAC;AACJ,CAAC,CAAC;AAGK,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,SAAS,CAAC,QAA0B,EAAE,IAAiB;QACrD,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,OAAO,EAAE,EAAE;YACd,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;YACnD,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,OAAO;oBACL,GAAG,OAAO;oBACV,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;wBACvB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;wBACvD,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;iBACxB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;oBACxB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAkB,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBACvD,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AApBY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;GACA,wBAAwB,CAoBpC"}