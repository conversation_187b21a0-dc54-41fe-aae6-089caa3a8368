import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class TokenService {
  private readonly TWILIO_ACCOUNT_SID: string;
  private readonly TWILIO_AUTH_TOKEN: string;

  constructor(private readonly configService: ConfigService) {
    this.TWILIO_ACCOUNT_SID = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    this.TWILIO_AUTH_TOKEN = this.configService.get<string>('TWILIO_AUTH_TOKEN');
  }

  async getTwilioCredits(): Promise<{ balance: string; currency: string }> {
    try {
      const response = await axios.get(
        `https://api.twilio.com/2010-04-01/Accounts/${this.TWILIO_ACCOUNT_SID}/Balance.json`,
        {
          auth: {
            username: this.TWILIO_ACCOUNT_SID,
            password: this.TWI<PERSON><PERSON>_AUTH_TOKEN,
          },
        },
      );

      return {
        balance: response.data.balance,
        currency: response.data.currency,
      };
    } catch (error) {
      throw new Error(`Failed to fetch Twilio credits: ${error.message}`);
    }
  }
}