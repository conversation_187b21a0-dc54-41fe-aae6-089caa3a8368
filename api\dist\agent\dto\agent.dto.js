"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentDto = exports.VoiceDto = exports.ModelDto = exports.ActionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class ActionDto {
}
exports.ActionDto = ActionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Send Confirmation SMS', description: 'Name of the action' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ActionDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'sendSMS',
        enum: ['sendSMS', 'sendEmail', 'transferCall', 'endCall', 'extractInfo', 'calendar'],
        description: 'Type of action to perform',
    }),
    (0, class_validator_1.IsEnum)(['sendSMS', 'sendEmail', 'transferCall', 'endCall', 'extractInfo', 'calendar']),
    __metadata("design:type", String)
], ActionDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'both',
        enum: ['inbound', 'outbound', 'both'],
        default: 'both',
        description: 'Type of call this action applies to',
    }),
    (0, class_validator_1.IsEnum)(['inbound', 'outbound', 'both']),
    __metadata("design:type", String)
], ActionDto.prototype, "callType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'I want to talk to a real assistant',
        required: false,
        description: 'Trigger phrase or condition for the action',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ActionDto.prototype, "trigger", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '+1234567890', required: false, description: 'Phone number for SMS or call actions' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ActionDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Hello, your appointment is confirmed!',
        required: false,
        description: 'Content of the SMS message',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ActionDto.prototype, "smsContent", void 0);
class ModelDto {
}
exports.ModelDto = ModelDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'openai', description: 'Provider of the language model' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ModelDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'gpt-4o-mini', description: 'Specific model identifier' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ModelDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 0.7,
        description: 'Temperature setting for the model (0.0 to 1.0)',
        default: 0.7,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ModelDto.prototype, "temperature", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: [{ role: 'system', content: 'Call script goes here' }],
        description: 'Array of message objects with role and content',
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => Object),
    __metadata("design:type", Array)
], ModelDto.prototype, "messages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 300, required: false, description: 'Maximum tokens for the model' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ModelDto.prototype, "maxTokens", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'd28775f5-604d-4399-b9f7-3c56ee956d4a', required: false, description: 'Knowledge base identifier' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ModelDto.prototype, "knowledgeBaseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Flag to enable emotion recognition' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ModelDto.prototype, "emotionRecognitionEnabled", void 0);
class VoiceDto {
}
exports.VoiceDto = VoiceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'eleven_turbo_v2_5', description: 'Voice model identifier' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], VoiceDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0.2, description: 'Voice style factor' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], VoiceDto.prototype, "style", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'sfmzdbhtmFeQb5D4tEio', description: 'Unique voice identifier' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], VoiceDto.prototype, "voiceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '11labs', description: 'Voice provider' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], VoiceDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0.5, description: 'Voice stability' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], VoiceDto.prototype, "stability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0.75, description: 'Voice similarity boost' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], VoiceDto.prototype, "similarityBoost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Flag to use speaker boost' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], VoiceDto.prototype, "useSpeakerBoost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 15, description: 'Minimum number of characters for input' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], VoiceDto.prototype, "inputMinCharacters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: [".", "،", "?", "!", ";", ",", "，", "۔"],
        description: 'Punctuation boundaries for input',
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], VoiceDto.prototype, "inputPunctuationBoundaries", void 0);
class AgentDto {
}
exports.AgentDto = AgentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'd15e4f0e-9307-43c7-8dcf-9fc2e9de5dea', description: 'Unique agent identifier' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AgentDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'c86e3479-4fce-42fd-920d-a5ec15b866a2', description: 'Organization identifier' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AgentDto.prototype, "orgId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Lisa', description: 'Name of the agent' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AgentDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => VoiceDto, description: 'Voice configuration for the agent' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => VoiceDto),
    __metadata("design:type", VoiceDto)
], AgentDto.prototype, "voice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2025-03-12T14:25:46.595Z', description: 'Creation timestamp' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Date)
], AgentDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2025-03-18T17:07:52.138Z', description: 'Last update timestamp' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Date)
], AgentDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => ModelDto, description: 'Language model configuration' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ModelDto),
    __metadata("design:type", ModelDto)
], AgentDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Whether recording is enabled' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AgentDto.prototype, "recordingEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Hey, is this {{customer.name}}?', description: 'First message of the agent' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentDto.prototype, "firstMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: "Hey, this is Lisa from Binghatti Properties. Could you please call me back when you're free?",
        description: 'Voicemail message',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentDto.prototype, "voicemailMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Flag to enable end call function' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AgentDto.prototype, "endCallFunctionEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Thank you for contacting us. Have a great day!', description: 'End call message' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentDto.prototype, "endCallMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { model: 'nova-2-phonecall', language: 'en', numerals: false, provider: 'deepgram' },
        description: 'Transcriber configuration',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentDto.prototype, "transcriber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ['function-call', 'metadata', 'transcript', 'transfer-update'],
        description: 'List of client messages',
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AgentDto.prototype, "clientMessages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['end-of-call-report'], description: 'List of server messages' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AgentDto.prototype, "serverMessages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'https://dev.orova.ai/webhook/', description: 'Server URL for webhook callbacks' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentDto.prototype, "serverUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['goodbye'], description: 'List of phrases that trigger end call' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AgentDto.prototype, "endCallPhrases", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: false, description: 'HIPAA compliance flag' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AgentDto.prototype, "hipaaEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5219, description: 'Maximum duration of the call in seconds' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], AgentDto.prototype, "maxDurationSeconds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'office', description: 'Background sound setting' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentDto.prototype, "backgroundSound", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Flag for backchanneling' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AgentDto.prototype, "backchannelingEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: {
            summaryPrompt: 'You are an expert note-taker. ...',
            structuredDataPrompt: 'Extract the following information from the call transcript: ...',
            structuredDataSchema: {},
            structuredDataRequestTimeoutSeconds: 10,
            successEvaluationPrompt: 'You are an expert call evaluator. ...',
            successEvaluationRubric: 'PassFail',
        },
        description: 'Analysis plan configuration',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentDto.prototype, "analysisPlan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: { provider: 'twilio' }, description: 'Voicemail detection configuration' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentDto.prototype, "voicemailDetection", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Background denoising flag' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AgentDto.prototype, "backgroundDenoisingEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { idleMessages: ['Are you still there?'], idleMessageMaxSpokenCount: 9, idleTimeoutSeconds: 9 },
        description: 'Message plan configuration',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentDto.prototype, "messagePlan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: { waitSeconds: 0.3 }, description: 'Start speaking plan configuration' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentDto.prototype, "startSpeakingPlan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: { numWords: 2 }, description: 'Stop speaking plan configuration' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentDto.prototype, "stopSpeakingPlan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { hipaaEnabled: false, pciEnabled: false },
        description: 'Compliance plan configuration',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AgentDto.prototype, "compliancePlan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Flag to indicate if the server URL secret is set' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AgentDto.prototype, "isServerUrlSecretSet", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => [ActionDto],
        required: false,
        description: 'List of actions the agent can perform',
        default: [],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ActionDto),
    __metadata("design:type", Array)
], AgentDto.prototype, "actions", void 0);
//# sourceMappingURL=agent.dto.js.map