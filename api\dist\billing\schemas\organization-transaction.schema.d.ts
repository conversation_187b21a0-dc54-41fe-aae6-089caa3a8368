import { Document } from 'mongoose';
export declare class OrganizationTransaction {
    organizationId: string;
    userId?: string;
    amount: number;
    currency: string;
    status: string;
    paymentMethodId?: string;
    stripePaymentIntentId: string;
    stripeCustomerId: string;
    description?: string;
    email: string;
    metadata?: Record<string, any>;
}
export type OrganizationTransactionDocument = OrganizationTransaction & Document;
export declare const OrganizationTransactionSchema: import("mongoose").Schema<OrganizationTransaction, import("mongoose").Model<OrganizationTransaction, any, any, any, Document<unknown, any, OrganizationTransaction> & OrganizationTransaction & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, OrganizationTransaction, Document<unknown, {}, import("mongoose").FlatRecord<OrganizationTransaction>> & import("mongoose").FlatRecord<OrganizationTransaction> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
