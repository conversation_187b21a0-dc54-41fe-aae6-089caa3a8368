import { authFetch } from '@/lib/authFetch';

const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';

export interface GlobalSettings {
  // Billing settings moved to organization level
  createdAt: string;
  updatedAt: string;
  lastUpdatedBy: string;
}

export interface UpdateGlobalSettingsParams {
  // Billing settings moved to organization level
  // This interface is kept for future system-level settings
  [key: string]: unknown;
}

/**
 * Fetch global settings
 */
export const getGlobalSettings = async (): Promise<GlobalSettings> => {
  const response = await authFetch(`${API_URL}/api/global-settings`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch global settings');
  }

  return response.json();
};

/**
 * Update global settings
 */
export const updateGlobalSettings = async (
  params: UpdateGlobalSettingsParams
): Promise<GlobalSettings> => {
  const response = await authFetch(`${API_URL}/api/global-settings`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update global settings');
  }

  return response.json();
};
