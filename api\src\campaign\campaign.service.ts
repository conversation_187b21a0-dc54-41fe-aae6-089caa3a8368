import { Injectable, NotFoundException, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { CampaignDto, UpdateCampaignStatusDto } from "./dto/campaign.dto";
import { Campaign, CampaignDocument } from "./interfaces/campaign.interface";
import { ContactDocument } from "../contacts/interfaces/contact.interface";
import { ScheduledCallService } from "../scheduled-call/scheduled-call.service";
import moment from "moment-timezone";

@Injectable()
export class CampaignService {
  private readonly logger = new Logger(CampaignService.name);

  constructor(
    @InjectModel("Campaign")
    private readonly campaignModel: Model<CampaignDocument>,
    @InjectModel("Contact")
    private readonly contactModel: Model<ContactDocument>,
    private readonly scheduledCallService: ScheduledCallService
  ) {}

  async create(createCampaignDto: CampaignDto): Promise<CampaignDocument> {
    this.logger.log(`Creating campaign with createdBy: ${createCampaignDto.createdBy}`);

    const createdCampaign = new this.campaignModel(createCampaignDto);

    if (createCampaignDto.contacts && createCampaignDto.contacts.length > 0) {
      // Extract contact IDs from the contacts array - handle both string IDs and objects
      const contactIds = createCampaignDto.contacts.map((contact: any) => {
        if (typeof contact === 'string') {
          return contact;
        } else if (contact && typeof contact === 'object' && 'contactId' in contact) {
          return contact.contactId;
        } else {
          this.logger.warn(`Invalid contact format: ${JSON.stringify(contact)}`);
          return null;
        }
      }).filter((id: any) => id !== null); // Remove any null values

      // Update the contacts to include this campaign
      if (contactIds.length > 0) {
        // First, update the contacts to include this campaign
        await this.contactModel.updateMany(
          { _id: { $in: contactIds } },
          { $addToSet: { campaigns: createdCampaign._id } }
        );

        // Then, get the full contact details to add to the campaign
        const contactsToAdd = await this.contactModel
          .find({ _id: { $in: contactIds } })
          .exec();

        // For each contact, prepare the contact object to add to the campaign
        const contactObjects = contactsToAdd.map(contact => ({
          contactId: contact._id,
          contactName: contact.contactName,
          phoneNumber: contact.phoneNumber
        }));

        // Add the contact objects to the campaign
        (createdCampaign as any).contacts = contactObjects;
      } else {
        this.logger.warn('No valid contact IDs found in the contacts array');
      }

      const savedCampaign = await createdCampaign.save();

      try {
        await this.scheduleCallsForCampaign(savedCampaign);
      } catch (error) {
        this.logger.error(`Error scheduling calls for campaign ${savedCampaign.name}:`, error.message);
      }

      return savedCampaign;
    }

    return createdCampaign.save();
  }

  async findAll(statusFilter?: string): Promise<CampaignDocument[]> {
    const query = statusFilter
      ? { status: statusFilter }
      : {};
    return this.campaignModel.find(query).populate("contacts", "contactName").exec();
  }

  async findById(id: string): Promise<CampaignDocument> {
    const campaign = await this.campaignModel
      .findById(id)
      .populate("contacts", "contactName")
      .exec();
    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }
    return campaign;
  }

  async update(
    id: string,
    updateCampaignDto: any
  ): Promise<CampaignDocument> {
    const existingCampaign = await this.campaignModel.findById(id).exec();
    if (!existingCampaign) throw new NotFoundException("Campaign not found");

    // Get existing contacts in the campaign
    const existingContacts = await this.contactModel
      .find({ campaigns: existingCampaign._id })
      .exec();

    // Update contacts if provided
    if (updateCampaignDto.contacts && Array.isArray(updateCampaignDto.contacts)) {
      // Extract contact IDs from the new contacts array
      const newContactIds = updateCampaignDto.contacts
        .map((contact: any) => {
          if (typeof contact === 'string') {
            return contact;
          } else if (contact && typeof contact === 'object' && 'contactId' in contact) {
            return contact.contactId;
          }
          return null;
        })
        .filter((id: any) => id !== null);

      // Get IDs of existing contacts
      const existingContactIds = existingContacts.map(contact => contact._id.toString());

      // Find contacts that were removed (in existing but not in new)
      const removedContactIds = existingContactIds.filter(
        (id: string) => !newContactIds.includes(id)
      );

      // Find contacts that were added (in new but not in existing)
      const addedContactIds = newContactIds.filter(
        (id: string) => !existingContactIds.includes(id)
      );

      this.logger.log(`Campaign update: ${addedContactIds.length} contacts added, ${removedContactIds.length} contacts removed`);

      // Handle removed contacts - cancel their pending scheduled calls
      if (removedContactIds.length > 0) {
        try {
          // Get the contact details for the removed contacts
          const removedContacts = existingContacts.filter(
            contact => removedContactIds.includes(contact._id.toString())
          );

          // Extract contact names and phone numbers for cancellation
          const contactNames = removedContacts.map(contact => contact.contactName);
          const contactPhones = removedContacts.map(contact => contact.phoneNumber);

          // Cancel pending scheduled calls for these contacts
          const cancelledCount = await this.scheduledCallService.cancelPendingCallsForContacts(
            contactNames,
            contactPhones
          );

          this.logger.log(`Cancelled ${cancelledCount} pending scheduled calls for removed contacts`);
        } catch (error) {
          this.logger.error(`Error cancelling scheduled calls for removed contacts:`, error.message);
        }
      }

      // Remove the campaign from all contacts first
      await this.contactModel.updateMany(
        { campaigns: existingCampaign._id },
        { $pull: { campaigns: existingCampaign._id } }
      );

      // Add the campaign to the new contacts
      if (newContactIds.length > 0) {
        // First, update the contacts to include this campaign
        await this.contactModel.updateMany(
          { _id: { $in: newContactIds } },
          { $addToSet: { campaigns: existingCampaign._id } }
        );

        // Then, get the full contact details to add to the campaign
        const contactsToAdd = await this.contactModel
          .find({ _id: { $in: newContactIds } })
          .exec();

        // For each contact, add it to the campaign's contacts array with the proper format
        for (const contact of contactsToAdd) {
          await this.campaignModel.updateOne(
            { _id: existingCampaign._id },
            {
              $addToSet: {
                contacts: {
                  contactId: contact._id,
                  contactName: contact.contactName,
                  phoneNumber: contact.phoneNumber
                }
              }
            }
          );
        }
      }

      // Check if agent ID has changed
      const existingCampaignData = existingCampaign.toObject() as any;
      const existingAgentId = existingCampaignData.agentId;
      const agentIdChanged = updateCampaignDto.agentId && existingAgentId !== updateCampaignDto.agentId;

      // Check if concurrentCalls has changed
      const existingConcurrentCalls = existingCampaignData.concurrentCalls || 1;
      const newConcurrentCalls = updateCampaignDto.concurrentCalls || existingConcurrentCalls;
      const concurrentCallsChanged = newConcurrentCalls !== existingConcurrentCalls;

      // Update the campaign first
      const updatedCampaign = await this.campaignModel
        .findByIdAndUpdate(id, updateCampaignDto, { new: true })
        .exec();

      // If agent ID has changed, update all pending scheduled calls for contacts in this campaign
      if (agentIdChanged && updateCampaignDto.agentId) {
        try {
          // Get all contacts for this campaign
          const allCampaignContacts = await this.contactModel
            .find({ campaigns: existingCampaign._id })
            .exec();

          if (allCampaignContacts && allCampaignContacts.length > 0) {
            // Extract contact names and phone numbers
            const contactNames = allCampaignContacts.map(contact => contact.contactName);
            const contactPhones = allCampaignContacts.map(contact => contact.phoneNumber);

            // Update agent ID for pending scheduled calls
            const updatedCount = await this.scheduledCallService.updateAgentForPendingCalls(
              contactNames,
              contactPhones,
              updateCampaignDto.agentId
            );

            this.logger.log(`Updated agent ID to ${updateCampaignDto.agentId} for ${updatedCount} pending scheduled calls`);
          }
        } catch (error) {
          this.logger.error(`Error updating agent ID for scheduled calls:`, error.message);
        }
      }

      // If concurrentCalls has changed, reschedule all pending calls for this campaign
      if (concurrentCallsChanged && newConcurrentCalls > 0) {
        try {
          // Get the updated campaign data
          const updatedCampaignData = updatedCampaign.toObject() as any;

          // Get campaign settings for rescheduling
          const agentId = updatedCampaignData.agentId;

          // Get call window settings
          const callWindow = {
            startTime: updatedCampaignData.callWindow?.startTime || '09:00',
            endTime: updatedCampaignData.callWindow?.endTime || '17:00',
            daysOfWeek: updatedCampaignData.callWindow?.daysOfWeek || ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
          };

          // Reschedule all pending calls for this campaign
          const rescheduledCount = await this.scheduledCallService.rescheduleCampaignCalls(
            agentId,
            newConcurrentCalls,
            updatedCampaignData.batchIntervalMinutes || 3,
            callWindow
          );

          this.logger.log(`Rescheduled ${rescheduledCount} calls for campaign ${updatedCampaign.name} with new concurrency level ${newConcurrentCalls}`);
        } catch (error) {
          this.logger.error(`Error rescheduling calls for campaign:`, error.message);
        }
      }

      // Schedule calls for newly added contacts
      if (addedContactIds.length > 0) {
        try {
          const addedContacts = await this.contactModel
            .find({ _id: { $in: addedContactIds } })
            .exec();

          if (addedContacts.length > 0) {
            this.logger.log(`Scheduling calls for ${addedContacts.length} newly added contacts`);
            await this.scheduleCallsForContacts(updatedCampaign, addedContacts);
          }
        } catch (error) {
          this.logger.error(`Error scheduling calls for new contacts in campaign ${updatedCampaign.name}:`, error.message);
        }
      }

      return updatedCampaign;
    }

    // If no contacts were provided, just update the campaign
    return this.campaignModel
      .findByIdAndUpdate(id, updateCampaignDto, { new: true })
      .exec();
  }

  async updateStatus(
    id: string,
    statusDto: UpdateCampaignStatusDto
  ): Promise<CampaignDocument> {
    const campaign = await this.campaignModel.findById(id).exec();

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    // If campaign is being paused, cancel all pending scheduled calls
    if (statusDto.status === 'paused' && campaign.status !== 'paused') {
      this.logger.log(`Campaign ${campaign.name} is being paused, cancelling pending scheduled calls`);

      try {
        // Get all contacts for this campaign
        const contacts = await this.contactModel
          .find({ campaigns: campaign._id })
          .exec();

        if (contacts && contacts.length > 0) {
          // Extract contact names and phone numbers for cancellation
          const contactNames = contacts.map(contact => contact.contactName);
          const contactPhones = contacts.map(contact => contact.phoneNumber);

          // Cancel pending scheduled calls for these contacts
          const cancelledCount = await this.scheduledCallService.cancelPendingCallsForContacts(
            contactNames,
            contactPhones
          );

          this.logger.log(`Cancelled ${cancelledCount} pending scheduled calls for campaign ${campaign.name}`);
        }
      } catch (error) {
        this.logger.error(`Error cancelling scheduled calls for campaign ${campaign.name}:`, error.message);
      }
    }

    campaign.status = statusDto.status;
    return campaign.save();
  }

  async remove(id: string): Promise<void> {
    const campaign = await this.campaignModel.findById(id).exec();

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    // Get all contacts for this campaign before deleting it
    const contacts = await this.contactModel
      .find({ campaigns: campaign._id })
      .exec();

    // Delete the campaign
    await this.campaignModel.findByIdAndDelete(id).exec();

    // Remove the campaign from all contacts
    await this.contactModel.updateMany(
      { campaigns: campaign._id },
      { $pull: { campaigns: campaign._id } }
    );

    // Cancel all pending scheduled calls for contacts in this campaign
    if (contacts && contacts.length > 0) {
      try {
        // Extract contact names and phone numbers for cancellation
        const contactNames = contacts.map(contact => contact.contactName);
        const contactPhones = contacts.map(contact => contact.phoneNumber);

        // Cancel pending scheduled calls for these contacts
        const cancelledCount = await this.scheduledCallService.cancelPendingCallsForContacts(
          contactNames,
          contactPhones
        );

        this.logger.log(`Cancelled ${cancelledCount} pending scheduled calls for deleted campaign ${campaign.name}`);
      } catch (error) {
        this.logger.error(`Error cancelling scheduled calls for deleted campaign ${campaign.name}:`, error.message);
      }
    }
  }

  async search(term: string): Promise<CampaignDocument[]> {
    const searchRegex = new RegExp(term, "i");

    return this.campaignModel
      .find({
        name: { $regex: searchRegex },
      })
      .populate("contacts", "contactName")
      .exec();
  }

  async calculatePerformanceMetrics(id: string): Promise<any> {
    const campaign = await this.findById(id);

    // Placeholder for future implementation
    return {
      id: campaign._id,
      successRate: campaign.successRate,
      sentiment: campaign.sentiment,
    };
  }


  private async scheduleCallsForCampaign(campaign: CampaignDocument): Promise<void> {
    // Get all contacts for this campaign
    const contacts = await this.contactModel
      .find({ campaigns: campaign._id })
      .exec();

    if (!contacts || contacts.length === 0) {
      this.logger.warn(`No contacts found for campaign ${campaign.name}`);
      return;
    }

    // Schedule calls for these contacts
    await this.scheduleCallsForContacts(campaign, contacts);
  }



  public async scheduleCallsForContactInCampaign(
    campaign: CampaignDocument,
    contact: ContactDocument
  ): Promise<void> {
    const data       = campaign.toObject() as any;
    const agentId    = data.agentId;
    const concurrent = data.concurrentCalls || 1;
    const window     = data.callWindow;
    const region     = contact.region || 'UTC';
    if (!agentId) return;

    // 1) Fetch all pending calls (so we honor existing slots)
    const pending = await this.scheduledCallService.getPendingCallsForCampaign(agentId);

    // 2) Build slot→usage map
    const slotCounts = new Map<string, number>();
    pending.forEach(call => {
      const key = call.scheduledTime.toISOString();
      slotCounts.set(key, (slotCounts.get(key) || 0) + call.contacts.length);
    });

    // 3) Parse your window & make roll helper
    const [wSH, wSM] = window.startTime.split(':').map(Number);
    const [wEH, wEM] = window.endTime.split(':').map(Number);
    const allowed    = window.daysOfWeek.map((d: string) => d.toLowerCase());
    const batchIntervalMinutes = data.batchIntervalMinutes || 3;
    const INTERVAL   = batchIntervalMinutes * 60 * 1000; // Convert minutes to milliseconds

    // Use moment-timezone to handle time zone conversions
    const rollIntoWindow = (d: Date) => {
      // Convert the input date to the contact's time zone
      let momentDate = moment.utc(d).tz(region);

      // Check if before window start in contact's time zone
      if (momentDate.hours() < wSH || (momentDate.hours() === wSH && momentDate.minutes() < wSM)) {
        momentDate.hours(wSH).minutes(wSM).seconds(0).milliseconds(0);
      }
      // Check if after window end in contact's time zone
      else if (momentDate.hours() > wEH || (momentDate.hours() === wEH && momentDate.minutes() > wEM)) {
        momentDate.add(1, 'days').hours(wSH).minutes(wSM).seconds(0).milliseconds(0);
      }

      // Get the current day of week in lowercase in contact's time zone
      const currentDay = momentDate.format('dddd').toLowerCase();

      // Skip to allowed weekday
      if (!allowed.includes(currentDay)) {
        this.logger.log(`Day ${currentDay} not in allowed days: ${allowed.join(', ')}. Finding next allowed day.`);
        let daysChecked = 0;
        while (!allowed.includes(momentDate.format('dddd').toLowerCase()) && daysChecked < 7) {
          momentDate.add(1, 'days').hours(wSH).minutes(wSM).seconds(0).milliseconds(0);
          daysChecked++;
        }
        this.logger.log(`Adjusted to ${momentDate.format('dddd')} in ${region} time zone`);
      }

      // Return the date in UTC for storage
      return momentDate.toDate();
    };

    // 4) Compute initial slot exactly like batch method
    // Get current time in contact's time zone
    const now = moment().tz(region).toDate();
    let slot: Date;
    if (data.instantCall) {
      // For instant calls, use current time + 5 minutes instead of 1 hour
      slot = rollIntoWindow(new Date(now.getTime() + 5*60000));
    } else {
      // Create tomorrow's date in contact's time zone
      const tmr = moment(now).tz(region).add(1, 'days').toDate();
      slot = rollIntoWindow(tmr);
    }

    // 5) If there’s a later existing scheduled call, bump from there
    try {
      const latest = await this.scheduledCallService.getLatestScheduledCallForCampaign(agentId);
      if (latest.length) {
        const latestTime = new Date(latest[0].scheduledTime);
        const bumped = new Date(latestTime.getTime() + INTERVAL);

        // For instant calls, only use the latest time if it's later than our calculated slot
        if (data.instantCall) {
          if (bumped > slot) {
            slot = rollIntoWindow(bumped);
            this.logger.log(`Instant call adjusted to after latest call: ${slot.toISOString()} in ${region} time zone`);
          }
        } else {
          // For non-instant calls, always schedule after the latest call
          slot = rollIntoWindow(bumped);
          this.logger.log(`Adjusted start slot to after latest call: ${slot.toISOString()} in ${region} time zone`);
        }
      }
    } catch (e) {
      this.logger.error(`Error fetching latest call: ${e.message}`);
    }

    // 6) Find first free slot under concurrency limit
    while ((slotCounts.get(slot.toISOString()) || 0) >= concurrent) {
      slot = rollIntoWindow(new Date(slot.getTime() + INTERVAL));
    }

    // 7) Schedule the new contact there
    // Convert the slot time to a string in the contact's time zone
    const slotInContactTz = moment(slot).tz(region).format();

    await this.scheduledCallService.createScheduledCall({
      agentId,
      contacts: [{ Name: contact.contactName, MobileNumber: contact.phoneNumber }],
      scheduledTime: slotInContactTz,
      region: region,
      scheduledByName: data.createdBy || 'system',
    });
    this.logger.log(`Scheduled ${contact.contactName} at ${slotInContactTz} (${region} time zone)`);

    // 8) Mark that slot now used
    slotCounts.set(slot.toISOString(), (slotCounts.get(slot.toISOString())||0) + 1);
  }


  public async scheduleCallsForContacts(
    campaign: CampaignDocument,
    contacts: ContactDocument[]
  ): Promise<void> {
    if (!campaign.startDate) {
      this.logger.warn(
        `Campaign ${campaign.name} has no start date, skipping call scheduling`
      );
      return;
    }

    this.logger.log(
      `Scheduling ${contacts.length} contacts for campaign ${campaign.name}`
    );

    const now = new Date();
    const data = campaign.toObject() as any;
    const agentId = data.agentId;
    const concurrent = data.concurrentCalls || 1;
    const window = data.callWindow || {
      startTime: '09:00',
      endTime: '17:00',
      daysOfWeek: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    };

    if (!agentId) {
      this.logger.warn(
        `No agent ID for campaign ${campaign.name}, skipping`);
      return;
    }

    // Parse window boundaries
    const [wStartH, wStartM] = window.startTime.split(':').map(Number);
    const [wEndH, wEndM] = window.endTime.split(':').map(Number);
    const allowedDays = window.daysOfWeek.map((d: string) => d.toLowerCase());

    // Roll date into window and allowed days - with timezone support
    const rollIntoWindow = (d: Date, contactRegion: string): Date => {
      // Convert the input date to the contact's time zone
      let momentDate = moment.utc(d).tz(contactRegion);

      // Check if before window start in contact's time zone
      if (momentDate.hours() < wStartH || (momentDate.hours() === wStartH && momentDate.minutes() < wStartM)) {
        momentDate.hours(wStartH).minutes(wStartM).seconds(0).milliseconds(0);
      }
      // Check if after window end in contact's time zone
      else if (momentDate.hours() > wEndH || (momentDate.hours() === wEndH && momentDate.minutes() > wEndM)) {
        momentDate.add(1, 'days').hours(wStartH).minutes(wStartM).seconds(0).milliseconds(0);
      }

      // Get the current day of week in lowercase in contact's time zone
      const currentDay = momentDate.format('dddd').toLowerCase();

      // Skip to allowed weekday
      if (!allowedDays.includes(currentDay)) {
        this.logger.log(`Day ${currentDay} not in allowed days: ${allowedDays.join(', ')}. Finding next allowed day.`);
        let daysChecked = 0;
        while (!allowedDays.includes(momentDate.format('dddd').toLowerCase()) && daysChecked < 7) {
          momentDate.add(1, 'days').hours(wStartH).minutes(wStartM).seconds(0).milliseconds(0);
          daysChecked++;
        }
        this.logger.log(`Adjusted to ${momentDate.format('dddd')} in ${contactRegion} time zone`);
      }

      // Return the date in UTC for storage
      return momentDate.toDate();
    };

    // Determine starting slot
    let slot: Date;
    // Use UTC for initial slot calculation - we'll adjust for each contact's timezone later
    const defaultRegion = 'UTC';
    if (data.instantCall) {
      // For instant calls, use current time + 5 minutes instead of 1 hour
      slot = new Date(now.getTime() + 5 * 60000); // +5min
      slot = rollIntoWindow(slot, defaultRegion);
      this.logger.log(`Instant call on, first slot: ${slot.toISOString()}`);
    } else {
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(wStartH, wStartM, 0, 0);
      slot = rollIntoWindow(tomorrow, defaultRegion);
      this.logger.log(`Instant call off, first slot: ${slot.toISOString()}`);
    }

    // If there are existing calls, always bump from the latest
    try {
      const latest = await this.scheduledCallService.getLatestScheduledCallForCampaign(
        agentId
      );
      if (latest && latest.length > 0) {
        const latestTime = new Date(latest[0].scheduledTime);
        const batchIntervalMinutes = data.batchIntervalMinutes || 3;
        const bumped = new Date(latestTime.getTime() + batchIntervalMinutes * 60000);

        // For instant calls, only use the latest time if it's later than our calculated slot
        if (data.instantCall) {
          if (bumped > slot) {
            slot = rollIntoWindow(bumped, defaultRegion);
            this.logger.log(`Instant call adjusted to after latest call: ${slot.toISOString()}`);
          }
        } else {
          // For non-instant calls, always schedule after the latest call
          slot = rollIntoWindow(bumped, defaultRegion);
          this.logger.log(`Adjusted start slot to after latest call: ${slot.toISOString()}`);
        }
      }
    } catch (err) {
      this.logger.error(
        `Failed fetching latest call time: ${err.message}`
      );
    }

       // Schedule batches spaced by configurable interval (default 3 minutes)
       const batchIntervalMinutes = data.batchIntervalMinutes || 3;
       const INTERVAL_MS = batchIntervalMinutes * 60 * 1000;  // Convert minutes to milliseconds
       let current = slot;
       for (let i = 0; i < contacts.length; i += concurrent) {
         const batch = contacts.slice(i, i + concurrent);
         for (const ct of batch) {
           // Use each contact's region for scheduling
           const contactRegion = ct.region || 'UTC';

           // Calculate the appropriate time in the contact's time zone
           const contactSlot = rollIntoWindow(current, contactRegion);

           // Convert to a string in the contact's time zone
           const slotInContactTz = moment(contactSlot).tz(contactRegion).format();

           await this.scheduledCallService.createScheduledCall({
             agentId,
             contacts: [{ Name: ct.contactName, MobileNumber: ct.phoneNumber }],
             scheduledTime: slotInContactTz,
             region: contactRegion,
             scheduledByName: data.name || 'system',
           });
           this.logger.log(`Scheduled ${ct.contactName} at ${slotInContactTz} (${contactRegion} time zone)`);
         }

         // Bump by the configured interval then roll into your window/days‐of‐week
         current = new Date(current.getTime() + INTERVAL_MS);
         current = rollIntoWindow(current, defaultRegion);
       }


    this.logger.log(
      `Finished scheduling calls for campaign ${campaign.name}`
    );
  }


}
