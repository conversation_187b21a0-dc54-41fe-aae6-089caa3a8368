import { BadRequestException, Injectable, NotFoundException, forwardRef, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import { User, UserDocument } from './interfaces/user.interface';
import { UpdateUserDto } from './dto/update-user.dto';

import { GlobalSettingsService } from '../global-settings/global-settings.service';
import { OrganizationsService } from '../organizations/organizations.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel('User') private userModel: Model<UserDocument>,
    private globalSettingsService: GlobalSettingsService,
    @Inject(forwardRef(() => OrganizationsService))
    private organizationsService: OrganizationsService
  ) {}

  async create(userDto: any): Promise<User> {
    // Check if email exists
    const existingUser = await this.userModel.findOne({ email: userDto.email }).exec();
    if (existingUser) {
      throw new BadRequestException('Email already in use');
    }

    const hashedPassword = await bcrypt.hash(userDto.password, 12);
    const newUser = new this.userModel({
      ...userDto,
      password: hashedPassword,
    });
    return newUser.save();
  }

  async findAll(): Promise<User[]> {
    // Simply return all users with password excluded
    return this.userModel.find().select('-password').exec();
  }

  async findOne(email: string): Promise<UserDocument | undefined> {
    return this.userModel.findOne({ email }).exec();
  }

  async findById(id: string): Promise<UserDocument> {
    const user = await this.userModel.findById(id).select('-password').exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async findByName(fullName: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ fullName }).select('-password').exec();
  }

  async updateUser(id: string, updateDto: UpdateUserDto): Promise<UserDocument> {
    const user = await this.userModel.findById(id).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Check if email is being changed and is unique
    if (updateDto.email && updateDto.email !== user.email) {
      const existingUser = await this.userModel.findOne({ email: updateDto.email }).exec();
      if (existingUser) {
        throw new BadRequestException('Email already in use');
      }
    }

    // Handle password update if provided
    if (updateDto.password) {
      updateDto.password = await bcrypt.hash(updateDto.password, 12);
    } else {
      // Don't modify password if not provided
      delete updateDto.password;
    }

    // Update user
    Object.assign(user, {
      ...updateDto,
      updatedAt: new Date()
    });

    return user.save();
  }

  async approveUser(id: string): Promise<UserDocument> {
    const user = await this.userModel.findById(id).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    user.isApproved = true;
    user.updatedAt = new Date();
    return user.save();
  }


  async deleteUser(id: string): Promise<void> {
    const result = await this.userModel.deleteOne({ _id: id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
  }

  async hasSufficientCredits(userId: string, threshold: number = 1): Promise<boolean> {
    const user = await this.userModel.findById(userId).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // For system user, assume they have sufficient credits
    if (userId === 'system') {
      return true;
    }

    try {
      // Check if user belongs to an organization first
      if (!user.organizationId) {
        console.error('User has no organization assigned');
        return false;
      }

      // Get organization settings for minimum credits threshold
      const organization = await this.organizationsService.findOne(user.organizationId.toString());
      const minimumCreditsThreshold = organization.minimumCreditsThreshold || 1.0;

      // Use the higher of the two thresholds
      const effectiveThreshold = Math.max(threshold, minimumCreditsThreshold);

      console.log(`Credit check for user ${userId}: threshold=${threshold}, orgMinThreshold=${minimumCreditsThreshold}, effectiveThreshold=${effectiveThreshold}`);

      // Check organization credits using the new monthly credits system
      try {
        const availableCredits = await this.organizationsService.getAvailableCredits(user.organizationId.toString());

        console.log(`Available credits: free=${availableCredits.freeCreditsRemaining}, paid=${availableCredits.paidCredits}, total=${availableCredits.totalAvailable}`);

        // Always use the effective threshold (which considers organization minimum)
        // regardless of whether we have free credits or not
        const hasSufficient = availableCredits.totalAvailable >= effectiveThreshold;

        console.log(`Credit check result: ${hasSufficient} (${availableCredits.totalAvailable} >= ${effectiveThreshold})`);

        return hasSufficient;
      } catch (orgError) {
        console.error('Error checking organization credits:', orgError);
        // If organization check fails, assume insufficient credits
        return false;
      }
    } catch (error) {
      console.error('Error getting organization settings for credit check:', error);
      // Fallback to assuming insufficient credits if there's an error
      return false;
    }
  }

  async deductCredits(userId: string, amount: number): Promise<UserDocument> {
    const user = await this.userModel.findById(userId).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Check if user belongs to an organization
    if (!user.organizationId) {
      throw new BadRequestException('User has no organization to deduct credits from');
    }

    // Deduct from organization credits
    try {
      await this.organizationsService.deductCredits(user.organizationId.toString(), amount);
      return user;
    } catch (orgError) {
      console.error('Error deducting organization credits:', orgError);
      throw new BadRequestException('Failed to deduct credits from organization');
    }
  }



  // Helper method to get user's organization
  async getUserOrganization(userId: string) {
    const user = await this.userModel.findById(userId).exec();
    if (!user || !user.organizationId) {
      return null;
    }

    try {
      return await this.organizationsService.findOne(user.organizationId.toString());
    } catch (error) {
      console.error('Error getting user organization:', error);
      return null;
    }
  }

  // Getter for organizationsService to allow access from controller
  getOrganizationsService() {
    return this.organizationsService;
  }

  // Getter for globalSettingsService to allow access from controller
  getGlobalSettingsService() {
    return this.globalSettingsService;
  }
}