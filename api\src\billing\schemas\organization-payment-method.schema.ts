import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class OrganizationPaymentMethod {
  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  stripePaymentMethodId: string;

  @Prop()
  last4?: string;

  @Prop()
  expMonth?: string;

  @Prop()
  expYear?: string;

  @Prop()
  cardholderName?: string;

  @Prop({ default: false })
  isDefault: boolean;

  @Prop()
  brand?: string;
}

export type OrganizationPaymentMethodDocument = OrganizationPaymentMethod & Document;
export const OrganizationPaymentMethodSchema = SchemaFactory.createForClass(OrganizationPaymentMethod);
