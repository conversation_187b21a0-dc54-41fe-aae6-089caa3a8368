"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { APIKeyInput } from "../APIKeyInput";

export function VoiceSettings() {
  const [elevenlabsKey, setElevenlabsKey] = useState("");
  const [deepgramKey, setDeepgramKey] = useState("");
  const [assemblyai<PERSON><PERSON>, setAssemblyaiKey] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const testElevenLabs = async () => {
    // Implement actual API test
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return true;
  };

  const testDeepgram = async () => {
    // Implement actual API test
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return true;
  };

  const testAssemblyAI = async () => {
    // Implement actual API test
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return true;
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);
    // Implement API key saving
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSaving(false);
    // You could show a success toast here
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Voice APIs</h3>
        <div className="grid gap-6">
          <APIKeyInput
            label="ElevenLabs API Key"
            description="Required for high-quality text-to-speech"
            value={elevenlabsKey}
            onChange={setElevenlabsKey}
            onTest={testElevenLabs}
          />

          <APIKeyInput
            label="Deepgram API Key"
            description="Required for real-time speech recognition"
            value={deepgramKey}
            onChange={setDeepgramKey}
            onTest={testDeepgram}
          />

          <APIKeyInput
            label="AssemblyAI API Key"
            description="Required for advanced speech recognition and analysis"
            value={assemblyaiKey}
            onChange={setAssemblyaiKey}
            onTest={testAssemblyAI}
          />
        </div>
      </div>

      <div className="flex justify-end">
        <Button 
          onClick={handleSaveChanges}
          disabled={isSaving}
          className="bg-primary text-primary-foreground hover:bg-primary/90"
        >
          {isSaving ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </div>
  );
}