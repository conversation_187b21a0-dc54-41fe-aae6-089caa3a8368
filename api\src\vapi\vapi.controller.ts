import { Controller, Get, Post, Body, Res, HttpStatus, UseGuards, Req } from "@nestjs/common";
import { Response } from "express";
import { VapiService } from "./vapi.service";
import { JwtAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { RolesGuard } from "src/auth/guards/roles.guard";
import { UsersService } from "src/users/users.service";

@Controller("vapi")
export class VapiController {
  constructor(
    private readonly vapiService: VapiService,
    private readonly usersService: UsersService
  ) {}

  @Post("call-contacts")
  @UseGuards(JwtAuthGuard,RolesGuard)
  async callContacts(
    @Body() payload: { contacts: any[]; agentId: string,region: string},
    @Req() req: any,
    @Res() res: Response
  ) {
    try {
      const { contacts, agentId, region } = payload;
      const userId = req.user.userId;

      // Check if user has sufficient credits
      const hasSufficientCredits = await this.usersService.hasSufficientCredits(userId, 1);

      if (!hasSufficientCredits) {
        return res.status(HttpStatus.PAYMENT_REQUIRED).json({
          error: "Insufficient credits. Please add funds to your account.",
        });
      }

      const results = await this.vapiService.callContacts(contacts, agentId, region, userId);

      // Deduct credits for the call (1 credit per call)
      await this.usersService.deductCredits(userId, 1);

      return res.status(HttpStatus.OK).json({
        message: "Calls processed",
        results,
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: error.message,
      });
    }
  }

  @Post("webhook")
  async vapiWebhook(@Body() body: any, @Res() res: Response) {
    try {
      const result = await this.vapiService.processWebhook(body);
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: "Failed to process webhook and send data to Callback API",
      });
    }
  }
}
