/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * Simple date utility functions to replace date-fns
 */

export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * Add weeks to a date
 * @param date The date to add weeks to
 * @param weeks Number of weeks to add (can be negative)
 * @returns A new Date with weeks added
 */
export function addWeeks(date: Date, weeks: number): Date {
  return addDays(date, weeks * 7);
}

/**
 * Add months to a date
 * @param date The date to add months to
 * @param months Number of months to add (can be negative)
 * @returns A new Date with months added
 */
export function addMonths(date: Date, months: number): Date {
  const result = new Date(date);
  result.setMonth(result.getMonth() + months);
  return result;
}

/**
 * Add years to a date
 * @param date The date to add years to
 * @param years Number of years to add (can be negative)
 * @returns A new Date with years added
 */
export function addYears(date: Date, years: number): Date {
  const result = new Date(date);
  result.setFullYear(result.getFullYear() + years);
  return result;
}

/**
 * Format a date to YYYY-MM-DD
 * @param date The date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Format a date to YYYY-MM-DDTHH:MM (for datetime-local inputs)
 * @param date The date to format
 * @returns Formatted datetime string
 */
export function formatDateTimeLocal(date: Date): string {
  const dateStr = formatDate(date);
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${dateStr}T${hours}:${minutes}`;
}

/**
 * Check if a date is in the past
 * @param date The date to check
 * @returns True if the date is in the past
 */
export function isPast(date: Date): boolean {
  return date < new Date();
}

/**
 * Get current date formatted for datetime-local input
 * @returns Current date formatted as YYYY-MM-DDTHH:MM
 */
export function getCurrentDateTimeLocal(): string {
  const now = new Date();
  // Add 1 minute to ensure we're in the future
  now.setMinutes(now.getMinutes() + 1);
  return formatDateTimeLocal(now);
}

/**
 * Check if a date is a weekend (Saturday or Sunday)
 * @param date The date to check
 * @returns True if the date is a weekend
 */
export function isWeekend(date: Date): boolean {
  const day = date.getDay();
  return day === 0 || day === 6; // 0 is Sunday, 6 is Saturday
}

/**
 * Add business days to a date (skipping weekends)
 * @param date The date to add business days to
 * @param days Number of business days to add
 * @returns A new Date with business days added
 */
export function addBusinessDays(date: Date, days: number): Date {
  let result = new Date(date);
  let remainingDays = days;
  
  while (remainingDays !== 0) {
    result = addDays(result, remainingDays > 0 ? 1 : -1);
    if (!isWeekend(result)) {
      remainingDays += remainingDays > 0 ? -1 : 1;
    }
  }
  
  return result;
}


export function format(date: Date, formatStr: string): string {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  
  // Handle common format patterns
  switch (formatStr) {
    case "HH:mm":
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    case "yyyy-MM-dd":
      return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    case "yyyy-MM-dd'T'HH:mm":
      return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    case "PPP":
      // Example: "April 29, 2023"
      const monthNames = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
      ];
      return `${monthNames[date.getMonth()]} ${day}, ${year}`;
    default:
      return date.toLocaleString();
  }
}




export function parse(dateStr: string, formatStr: string, defaultDate: Date): Date {
  if (!dateStr) return defaultDate;
  
  try {
    // Handle the specific format used in the app
    if (formatStr === "yyyy-MM-dd'T'HH:mm") {
      // This format is ISO-like, so we can use Date constructor
      return new Date(dateStr);
    }
    
    // For other formats, we would need more complex parsing
    // For now, just try standard parsing
    const parsedDate = new Date(dateStr);
    return isValid(parsedDate) ? parsedDate : defaultDate;
  } catch (e) {
    return defaultDate;
  }
}

export function isValid(date: Date): boolean {
  return !isNaN(date.getTime());
}



export const getOrdinalSuffix = (day: number): string => {
  if (day > 3 && day < 21) return 'th';
  switch (day % 10) {
    case 1: return 'st';
    case 2: return 'nd';
    case 3: return 'rd';
    default: return 'th';
  }
};

// Custom date formatter with ordinal day
export const formatCustomDate = (date: Date, timezone?: string): string => {
  try {
    // Create date object in the specified timezone if provided
    const options: Intl.DateTimeFormatOptions = {
      ...(timezone && { timeZone: timezone }),
      weekday: 'long',
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    };
    
    // Get the formatted parts
    const formatter = new Intl.DateTimeFormat('en-US', options);
    const parts = formatter.formatToParts(date);
    
    // Extract the individual components
    const weekday = parts.find(part => part.type === 'weekday')?.value || '';
    const day = parseInt(parts.find(part => part.type === 'day')?.value || '0');
    const month = parts.find(part => part.type === 'month')?.value || '';
    const year = parts.find(part => part.type === 'year')?.value || '';
    const hour = parts.find(part => part.type === 'hour')?.value || '';
    const minute = parts.find(part => part.type === 'minute')?.value || '';
    const dayPeriod = parts.find(part => part.type === 'dayPeriod')?.value || '';
    
    // Format as "Friday 4th Apr, 2025 8:00 PM"
    return `${weekday} ${day}${getOrdinalSuffix(day)} ${month}, ${year} ${hour}:${minute} ${dayPeriod}`;
  } catch (error) {
    // Fallback to basic formatting if there's an error
    return date.toLocaleString('en-US');
  }
};