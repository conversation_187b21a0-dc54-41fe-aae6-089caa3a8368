/* eslint-disable @next/next/no-img-element */
import { useState } from 'react';
import { Agent } from '@/types/agent.types';
import { X, Send, Loader2 } from 'lucide-react';
import agentLisa from "@/assets/img/Binghatti-Lisa.jpeg";
import Image from "next/image";
import { Button } from '../ui/button';
import { Input } from '../ui/input';

interface ChatWidgetProps {
  agent: Agent | null;
  isOpen: boolean;
  onClose: () => void;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
}

export function AgentChatWidget({ agent, isOpen, onClose }: ChatWidgetProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  // Dummy responses
  const dummyResponses = [
    "I understand what you're saying. Let me help you with that.",
    "That's interesting! Could you tell me more?",
    "I'm processing your request. Here's what I think...",
    "Based on what you've told me, I would suggest...",
    "Let me check that for you quickly.",
  ];

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate agent typing
    setTimeout(() => {
      const randomResponse = dummyResponses[Math.floor(Math.random() * dummyResponses.length)];
      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: randomResponse,
        sender: 'agent',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, agentMessage]);
      setIsTyping(false);
    }, 1000);
  };

  // Send initial message when chat opens
  useState(() => {
    if (isOpen && messages.length === 0) {
      const initialMessage: Message = {
        id: 'initial',
        content: `Hello! My name is ${agent?.name}. How can I assist you today?`,
        sender: 'agent',
        timestamp: new Date()
      };
      setMessages([initialMessage]);
    }
  });

  return (
    <div className={`fixed right-0 top-19 h-[90vh] border-2 w-96 bg-white dark:bg-gray-800 shadow-xl transition-all duration-200 ${
      isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
    } z-50 rounded-lg`}>
      {/* Header */}
      <div className="border-b p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="relative h-10 w-10 rounded-full overflow-hidden">
            {agent?.avatar ? (
              <img 
                src={agent.avatar}
                alt={`${agent.name} avatar`}
                className="h-full w-full object-cover"
              />
            ) : (
              <Image 
                src={agentLisa}
                alt={`${agent?.name} avatar`}
                className="object-cover"
                fill
              />
            )}
          </div>
          <div>
            <h3 className="font-semibold">{agent?.name}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">{agent?.role}</p>
          </div>
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-8 w-8 p-0"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Messages */}
      <div className="h-[calc(100%-8rem)] overflow-y-auto p-4 space-y-4">
        {messages.map(message => (
          <div 
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`max-w-[80%] rounded-lg p-3 ${
              message.sender === 'user' 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-gray-100 dark:bg-gray-700'
            }`}>
              {message.content}
            </div>
          </div>
        ))}
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
              <Loader2 className="h-4 w-4 animate-spin" />
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="absolute bottom-0 left-0 right-0 p-4 bg-white dark:bg-gray-800 border-t">
        <form 
          onSubmit={(e) => {
            e.preventDefault();
            sendMessage(inputMessage);
          }}
          className="flex gap-2"
        >
          <Input
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Type a message..."
            className="flex-1"
          />
          <Button type="submit" size="icon">
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
}