"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const email_service_1 = require("../email/email.service");
let NotificationsService = class NotificationsService {
    constructor(emailService, configService, organizationModel) {
        this.emailService = emailService;
        this.configService = configService;
        this.organizationModel = organizationModel;
    }
    shouldSendCreditRunoutNotification(organization) {
        const minimumThreshold = organization.minimumCreditsThreshold || 1.0;
        const hasCreditsIssue = organization.credits <= minimumThreshold;
        const hasEmailSettings = !!organization.email && !!organization.fullName;
        const lastSent = organization.lastRunoutEmailSent;
        const canSendAgain = !lastSent || (Date.now() - new Date(lastSent).getTime()) > 24 * 60 * 60 * 1000;
        console.log(`Credit runout check for org ${organization._id}:`, {
            credits: organization.credits,
            minimumThreshold,
            hasCreditsIssue,
            hasEmailSettings,
            email: organization.email,
            fullName: organization.fullName,
            lastSent,
            canSendAgain,
            shouldSend: hasCreditsIssue && hasEmailSettings && canSendAgain
        });
        return hasCreditsIssue && hasEmailSettings && canSendAgain;
    }
    shouldSendCreditWarningNotification(organization) {
        const warningThreshold = organization.minimumCreditsThreshold * 2;
        const hasCreditsWarning = organization.credits > 0 && organization.credits <= warningThreshold;
        const hasEmailSettings = !!organization.email && !!organization.fullName;
        const lastSent = organization.lastWarningEmailSent;
        const canSendAgain = !lastSent || (Date.now() - new Date(lastSent).getTime()) > 24 * 60 * 60 * 1000;
        console.log(`Credit warning check for org ${organization._id}:`, {
            credits: organization.credits,
            minimumThreshold: organization.minimumCreditsThreshold,
            warningThreshold,
            hasCreditsWarning,
            hasEmailSettings,
            email: organization.email,
            fullName: organization.fullName,
            lastSent,
            canSendAgain,
            shouldSend: hasCreditsWarning && hasEmailSettings && canSendAgain
        });
        return hasCreditsWarning && hasEmailSettings && canSendAgain;
    }
    async sendCreditRunoutNotification(organization) {
        if (!this.shouldSendCreditRunoutNotification(organization)) {
            return false;
        }
        const frontendUrl = this.configService.get('FRONTEND_URL');
        const fundingUrl = `${frontendUrl}/billing`;
        try {
            const success = await this.emailService.sendCreditRunoutNotification(organization.fullName, organization.email, organization.credits, fundingUrl);
            if (success) {
                await this.organizationModel.findByIdAndUpdate(organization._id, {
                    lastRunoutEmailSent: new Date()
                });
                console.log(`Credit runout notification sent to ${organization.fullName} (${organization.email})`);
            }
            else {
                console.error(`Failed to send credit runout notification to ${organization.fullName} (${organization.email})`);
            }
            return success;
        }
        catch (error) {
            console.error('Error sending credit runout notification:', error);
            return false;
        }
    }
    async sendCreditWarningNotification(organization) {
        if (!this.shouldSendCreditWarningNotification(organization)) {
            return false;
        }
        const frontendUrl = this.configService.get('FRONTEND_URL');
        const fundingUrl = `${frontendUrl}/billing`;
        try {
            const success = await this.emailService.sendCreditWarningNotification(organization.fullName, organization.email, organization.credits, fundingUrl);
            if (success) {
                await this.organizationModel.findByIdAndUpdate(organization._id, {
                    lastWarningEmailSent: new Date()
                });
                console.log(`Credit warning notification sent to ${organization.fullName} (${organization.email})`);
            }
            else {
                console.error(`Failed to send credit warning notification to ${organization.fullName} (${organization.email})`);
            }
            return success;
        }
        catch (error) {
            console.error('Error sending credit warning notification:', error);
            return false;
        }
    }
    async processCreditNotifications(organization) {
        try {
            const warningThreshold = organization.minimumCreditsThreshold * 2;
            if (organization.credits > warningThreshold && organization.lastWarningEmailSent) {
                await this.organizationModel.findByIdAndUpdate(organization._id, {
                    lastWarningEmailSent: null
                });
            }
            const minimumThreshold = organization.minimumCreditsThreshold || 1.0;
            if (organization.credits > minimumThreshold && organization.lastRunoutEmailSent) {
                await this.organizationModel.findByIdAndUpdate(organization._id, {
                    lastRunoutEmailSent: null
                });
            }
            if (this.shouldSendCreditRunoutNotification(organization)) {
                await this.sendCreditRunoutNotification(organization);
            }
            else if (this.shouldSendCreditWarningNotification(organization)) {
                await this.sendCreditWarningNotification(organization);
            }
        }
        catch (error) {
            console.error('Error processing credit notifications:', error);
        }
    }
    getCreditStatus(organization) {
        const minimumThreshold = organization.minimumCreditsThreshold || 1.0;
        const warningThreshold = minimumThreshold * 2;
        if (organization.credits <= minimumThreshold) {
            return {
                status: 'depleted',
                shouldNotify: this.shouldSendCreditRunoutNotification(organization),
                message: 'Credits have reached minimum threshold. Service may be limited.'
            };
        }
        if (organization.credits <= warningThreshold) {
            return {
                status: 'warning',
                shouldNotify: this.shouldSendCreditWarningNotification(organization),
                message: `Credits are running low. Current balance: $${organization.credits.toFixed(2)}`
            };
        }
        return {
            status: 'normal',
            shouldNotify: false,
            message: `Credit balance is healthy: $${organization.credits.toFixed(2)}`
        };
    }
};
exports.NotificationsService = NotificationsService;
exports.NotificationsService = NotificationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, mongoose_1.InjectModel)('Organization')),
    __metadata("design:paramtypes", [email_service_1.EmailService,
        config_1.ConfigService,
        mongoose_2.Model])
], NotificationsService);
//# sourceMappingURL=notifications.service.js.map