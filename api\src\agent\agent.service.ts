
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { join } from 'path';
import * as fs from 'fs';

@Injectable()
export class AgentService {
  private readonly VAPI_API_TOKEN = process.env.VAPI_API_TOKEN || '';
  
  constructor(
    @InjectModel('Agent') private readonly agentModel: Model<any>,
  ) {}

  async create(createAgentDto: any): Promise<any> {
    try {

      const systemMessage = createAgentDto.model?.messages?.find(m => m.role === 'system');
      const systemContent = systemMessage?.content || '';

      // Create a payload for VAPI
      const vapiPayload = {
        name: createAgentDto.name || '',
        firstMessage: createAgentDto.firstMessage || '',
        voicemailMessage: createAgentDto.voicemailMessage || '',
        model: {
          provider: createAgentDto.model?.provider || 'openai', 
          model: createAgentDto.model?.model || 'gpt-4o', 
          maxTokens: createAgentDto.model?.maxTokens,
          temperature: createAgentDto.model?.temperature,
          messages: [
            {
              role: 'system',
              content: systemContent
            }
          ]
        },
        server: {
          url: createAgentDto?.server?.url || 'https://testurl.com',
        },
        backgroundDenoisingEnabled: createAgentDto.backgroundDenoisingEnabled || false,
      };
      

      // Create the agent in VAPI
      let vapiAgent;
      try {
        const response = await fetch('https://api.vapi.ai/assistant', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(vapiPayload)
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`VAPI API request failed with status ${response.status}: ${errorText}`);
          throw new Error(`Failed to create agent: ${errorText}`);
        }
        
        vapiAgent = await response.json();
      } catch (vapiError) {
        console.error('Error creating agent:', vapiError);
        throw vapiError;
      }
      
      // Now create the agent in our database with the VAPI ID
      const newAgent = new this.agentModel({
        ...createAgentDto,
        id: vapiAgent.id, // Use the ID from VAPI
      });
      
      const savedAgent = await newAgent.save();
      return savedAgent;
    } catch (error) {
      console.error('Error in create method:', error);
      throw error;
    }
  }


  async findAll(): Promise<any[]> {
    try {
      // Get agents from VAPI API
      const response = await fetch('https://api.vapi.ai/assistant', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const vapiAgents = await response.json();
  
      // Get all existing agents from our database
      const existingAgents = await this.agentModel.find().exec();
      
      // Create a map of VAPI agent IDs for quick lookup
      const vapiAgentIds = new Set(vapiAgents.map(agent => agent.id));
      
      // Delete agents from database that don't exist in VAPI
      for (const dbAgent of existingAgents) {
        if (!vapiAgentIds.has(dbAgent.id)) {
          await this.agentModel.findByIdAndDelete(dbAgent._id).exec();
        }
      }
  
      // Sync VAPI agents with our database
      for (const vapiAgent of vapiAgents) {
        // Check if agent already exists in our database
        const existingAgent = await this.agentModel.findOne({ id: vapiAgent.id }).exec();
        
        if (!existingAgent) {
          // If agent doesn't exist, create new one with default values
          const newAgent = new this.agentModel({
            ...vapiAgent,
            role: 'assistant', // Set default role for new agents
            avatar: null, // Set default avatar to null
            status: 'active' 
          });
          await newAgent.save();
        } else {
          // If agent exists, update it with latest VAPI data but preserve our custom fields
          await this.agentModel.findOneAndUpdate(
            { id: vapiAgent.id },
            { 
              ...vapiAgent,
              role: existingAgent.role || 'assistant', // Preserve existing role or set default
              avatar: existingAgent.avatar, // Preserve existing avatar
              status: existingAgent.status || 'active' 
            },
            { new: true }
          ).exec();
        }
      }
  
      // Return agents from our database (now fully synced with VAPI)
      return this.agentModel.find().exec();
      
    } catch (error) {
      console.error('Error syncing agents:', error.message);
      // If VAPI sync fails, return what we have in database
      return this.agentModel.find().exec();
    }
  }

  async findById(id: string): Promise<any> {
    // Only look for the agent in our database since we've already synced with VAPI
    let agent;
    
    try {
      // Try to find by MongoDB ObjectId
      agent = await this.agentModel.findById(id).exec();
    } catch (error) {
      // If error (likely invalid ObjectId format), try to find by 'id' field
      agent = await this.agentModel.findOne({ id: id }).exec();
    }
    
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    
    // Return the agent from our database with all custom fields
    return agent;
  }

  
  async update(id: string, updateAgentDto: any): Promise<any> {
    try {
      // First, find the existing agent
      const existingAgent = await this.agentModel.findOne({ id }).exec() || 
                            await this.agentModel.findById(id).exec();
      
      if (!existingAgent) {
        throw new NotFoundException('Agent not found');
      }
      
      // Create a payload for VAPI with only the fields VAPI expects
      const vapiPayload = {
        name: updateAgentDto.name,
        firstMessage: updateAgentDto.firstMessage,
        voicemailMessage: updateAgentDto.voicemailMessage,
        model: {
          provider: updateAgentDto.model?.provider,
          model: updateAgentDto.model?.model,
          maxTokens: updateAgentDto.model?.maxTokens,
          temperature: updateAgentDto.model?.temperature,
          messages: updateAgentDto.model?.messages
        },
        server: {
          url: updateAgentDto.server?.url,
        },
        backgroundDenoisingEnabled: updateAgentDto.backgroundDenoisingEnabled,
      };
      
      if (existingAgent && existingAgent.id) {
        // Update in VAPI with only VAPI-related fields
        const response = await fetch(`https://api.vapi.ai/assistant/${existingAgent.id}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(vapiPayload)
        });
        
        if (!response.ok) {
          console.error(`VAPI API request failed with status ${response.status}`);
        }
      }
      
      // Update in our database with all fields (both VAPI and custom)
      // Try MongoDB ObjectId first
      const updatedAgent = await this.agentModel
        .findByIdAndUpdate(id, updateAgentDto, { new: true })
        .exec();
      
      if (updatedAgent) return updatedAgent;
      
      // If not found, try UUID format
      const agentByUuid = await this.agentModel
        .findOneAndUpdate({ id: id }, updateAgentDto, { new: true })
        .exec();
      
      if (!agentByUuid) throw new NotFoundException('Agent not found');
      return agentByUuid;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      
      // Try the alternative lookup if first attempt fails
      try {
        const agentByUuid = await this.agentModel
          .findOneAndUpdate({ id: id }, updateAgentDto, { new: true })
          .exec();
        
        if (!agentByUuid) throw new NotFoundException('Agent not found');
        return agentByUuid;
      } catch (secondError) {
        throw new NotFoundException('Agent not found');
      }
    }
  }

  async remove(id: string): Promise<void> {
    try {
      // First, delete from VAPI
      const response = await fetch(`https://api.vapi.ai/assistant/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`VAPI API request failed with status ${response.status}`);
      }
      
      // Try to find by 'id' field first (for VAPI UUID format)
      let result = await this.agentModel.findOneAndDelete({ id: id }).exec();
      
      // If not found by 'id', try MongoDB ObjectId
      if (!result) {
        result = await this.agentModel.findByIdAndDelete(id).exec();
      }
      
      if (!result) {
        throw new NotFoundException('Agent not found');
      }
    } catch (error) {
      console.error('Error deleting agent:', error);
      throw error;
    }
  }

  async getUploadedFiles(): Promise<{ files: string[], count: number }> {
  try {
    // Check both upload directories
    const distUploadDir = join(__dirname, '../uploads');
    const rootUploadDir = join(__dirname, '../../uploads');
    
    let allFiles: string[] = [];
    
    // Check dist/uploads if it exists
    if (fs.existsSync(distUploadDir)) {
      const distFiles = fs.readdirSync(distUploadDir)
        .filter(file => {
          // Filter out directories and hidden files
          const filePath = join(distUploadDir, file);
          return fs.statSync(filePath).isFile() && !file.startsWith('.');
        })
        .map(file => `/api/uploads/${file}`);
      
      allFiles = [...allFiles, ...distFiles];
    }
    
    // Check root/uploads if it exists
    if (fs.existsSync(rootUploadDir)) {
      const rootFiles = fs.readdirSync(rootUploadDir)
        .filter(file => {
          // Filter out directories and hidden files
          const filePath = join(rootUploadDir, file);
          return fs.statSync(filePath).isFile() && !file.startsWith('.');
        })
        .map(file => `/api/uploads/${file}`);
      
      // Add files that aren't already in the list (avoid duplicates)
      rootFiles.forEach(file => {
        if (!allFiles.includes(file)) {
          allFiles.push(file);
        }
      });
    }
    
    return {
      files: allFiles,
      count: allFiles.length
    };
  } catch (error) {
    console.error('Error listing uploaded files:', error);
    throw error;
  }
}

async getVolumeFiles(): Promise<{ files: string[], count: number, error?: string, volumePath: string, isVolumeAccessible: boolean }> {
  try {
    const volumePath = '/usr/src/app/uploads';
    let volumeFiles: string[] = [];

    if (fs.existsSync(volumePath)) {
      volumeFiles = fs.readdirSync(volumePath)
        .filter(file => {
          const filePath = join(volumePath, file);
          return fs.statSync(filePath).isFile() && !file.startsWith('.');
        })
        .map(file => `/api/uploads/${file}`);
    }

    // Also check if files are actually accessible
    const accessibleFiles = await Promise.all(
      volumeFiles.map(async (file) => {
        try {
          await fs.promises.access(join(volumePath, file.split('/').pop()!));
          return file;
        } catch {
          return null;
        }
      })
    );

    const validFiles = accessibleFiles.filter(file => file !== null) as string[];

    return {
      files: validFiles,
      count: validFiles.length,
      volumePath, // Include the volume path for debugging
      isVolumeAccessible: fs.existsSync(volumePath)
    };
  } catch (error) {
    console.error('Error listing volume files:', error);
    return {
      files: [],
      count: 0,
      error: error.message,
      volumePath: '/usr/src/app/uploads',
      isVolumeAccessible: false
    };
  }
}

}


