import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  UseGuards,
  Query,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { LoggerService } from "./logger.service";
import { LogDocument } from "./interfaces/log.interface";
import { JwtAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { RolesGuard } from "src/auth/guards/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";

@Controller("logs")
export class LogsController {
  constructor(
    private readonly loggerService: LoggerService,
    @InjectModel("Log") private readonly logModel: Model<LogDocument>
  ) {}

  // @UseGuards(JwtAuthGuard, RolesGuard)
  // @Roles("superadmin")
  @Get()
  async getAllLogs(
    @Query('level') level?: string,
    @Query('search') search?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50,
  ): Promise<{ logs: LogDocument[], total: number, page: number, totalPages: number }> {
    // Build query filters
    const filter: any = {};

    // Filter by log level
    if (level && ['INFO', 'WARN', 'ERROR'].includes(level.toUpperCase())) {
      filter.level = level.toUpperCase();
    }

    // Filter by date range
    if (startDate || endDate) {
      filter.timestamp = {};
      if (startDate) {
        filter.timestamp.$gte = new Date(startDate);
      }
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // Set to end of day
        filter.timestamp.$lte = endDateTime;
      }
    }

    // Search in message or trace
    if (search) {
      filter.$or = [
        { message: { $regex: search, $options: 'i' } },
        { trace: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query with pagination
    const logs = await this.logModel.find(filter)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .exec();

    // Get total count for pagination
    const total = await this.logModel.countDocuments(filter).exec();
    const totalPages = Math.ceil(total / limit);

    return {
      logs,
      total,
      page,
      totalPages
    };
  }

  @Get('cleanup')
  async cleanupOldLogs(): Promise<{ message: string, deletedCount: number }> {
    const deletedCount = await this.loggerService.deleteOldLogs();
    return {
      message: `Successfully deleted ${deletedCount} logs older than 4 days`,
      deletedCount
    };
  }

  @Post()
  async addLog(
    @Body() body: { level: string; message: string; trace?: any }
  ): Promise<{ message: string }> {
    const { level, message, trace } = body;

    if (!level || !message) {
      throw new HttpException(
        "Missing required fields: level and message are required.",
        HttpStatus.BAD_REQUEST
      );
    }

    switch (level.toUpperCase()) {
      case "INFO":
        await this.loggerService.log(message);
        break;
      case "WARN":
        await this.loggerService.warn(message);
        break;
      case "ERROR":
        await this.loggerService.error(message, trace);
        break;
      default:
        throw new HttpException(
          "Invalid log level provided. Use INFO, WARN, or ERROR.",
          HttpStatus.BAD_REQUEST
        );
    }

    return { message: "Log entry created successfully" };
  }
}
