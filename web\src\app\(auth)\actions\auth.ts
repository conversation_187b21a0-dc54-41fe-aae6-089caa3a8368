/* eslint-disable @typescript-eslint/no-unused-vars */
"use server";
import { cookies } from "next/headers";
import { registerSchema, loginSchema } from "@/lib/validations/authSchema";



export type ActionResult = {
  success: boolean;
  message: string;
  fieldErrors?: Record<string, string[]>;
  redirect?: string;
  tokens?: {
    access_token: string;
    refresh_token: string;
  };
};
 
export type UserInfo = {
  fullName: string;
  userId: string;
  email: string;
  role: string;
};
 
const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";
 
 
export async function registerUser(formData: FormData): Promise<ActionResult> {
    // Extract data from form
    const rawData = {
      fullName: formData.get("fullName"),
      email: formData.get("email"),
      password: formData.get("password"),
    };
 
    // Validate with Zod
    const validationResult = registerSchema.safeParse(rawData);
   
    if (!validationResult.success) {
      // Return validation errors
      return {
        success: false,
        message: "Validation failed",
        fieldErrors: validationResult.error.flatten().fieldErrors,
      };
    }
 
    // Validation passed, destructure the validated data
    const { fullName, email, password } = validationResult.data;
 
    try {
      // Make API request to registration endpoint
      const response = await fetch(`${API_BASE_URL}/api/users/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fullName,
          email,
          password,
        }),
        cache: "no-store",
      });
 
      // First check if response is ok
      if (!response.ok) {
        try {
          // Try to parse as JSON for error details
          const errorData = await response.json();
          return {
            success: false,
            message: errorData.message || "Registration failed. Please try again.",
          };
        } catch {
          // If not JSON, get text or use statusText
          const errorText = await response.text().catch(() => response.statusText);
          return {
            success: false,
            message: errorText || "Registration failed. Please try again.",
          };
        }
      }
 
      // For successful responses, get the text content
      const successText = await response.text();
     
      return {
        success: true,
        message: successText || "User registered successfully! Awaiting admin approval."
      };
 
    } catch (error) {
      console.error("Registration error:", error);
      return {
        success: false,
        message: "An unexpected error occurred. Please try again later."
      };
    }
  }
 
export async function loginUser(formData: FormData): Promise<ActionResult> {
    const rawData = {
      email: formData.get("email"),
      password: formData.get("password"),
    };
 
    // Validate with Zod
    const validationResult = loginSchema.safeParse(rawData);
   
    if (!validationResult.success) {
      // Return validation errors
      return {
        success: false,
        message: "Validation failed",
        fieldErrors: validationResult.error.flatten().fieldErrors,
      };
    }
 
    // Validation passed, destructure the validated data
    const { email, password } = validationResult.data;
 
    try {
      // Make API request to login endpoint
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
        }),
        cache: "no-store",
      });
 
      // Check if the response is OK
      if (!response.ok) {
        try {
          const errorData = await response.json();
          return {
            success: false,
            message: errorData.message || "Login failed. Please check your credentials.",
          };
        } catch {
          return {
            success: false,
            message: response.statusText || "Login failed. Please try again.",
          };
        }
      }
 
      // Parse the successful response to get tokens
      try {
        const data = await response.json();
       
        if (data.access_token && data.refresh_token) {
          // Return tokens in the response rather than setting cookies
          return {
            success: true,
            message: "Login successful!",
            redirect: "/dashboard",
            tokens: {
              access_token: data.access_token,
              refresh_token: data.refresh_token
            }
          };
        } else {
          return {
            success: false,
            message: "Invalid response from server. Missing authentication tokens.",
          };
        }
      } catch (jsonError) {
        return {
          success: false,
          message: "Failed to process login response.",
        };
      }
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        message: "An unexpected error occurred. Please try again later."
      };
    }
  }
 
  // Add a refresh token function for later use
  export async function refreshAccessToken(): Promise<{
    success: boolean;
    newAccessToken?: string;
  }> {
   
    // Get the refresh token from cookies
    const refreshToken = (await cookies()).get("refresh_token")?.value;
 
   
    if (!refreshToken) {
      // Without a refresh token, we can't refresh the session
      return { success: false };
    }
 
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${refreshToken}`,
        },
        body: JSON.stringify({ refreshToken }),
        cache: "no-store",
      });
 
      if (!response.ok) {
        // If the refresh token is invalid or expired, user needs to log in again
        return { success: false };
      }
 
      const data = await response.json();
     
      if (data.access_token) {
        // Update the access token in cookies
        (await
          // Update the access token in cookies
          cookies()).set("access_token", data.access_token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          path: "/",
          sameSite: "lax",
          maxAge: 60 * 60, // 1 hour
        });
 
        return {
          success: true,
          newAccessToken: data.access_token,
        };
      }
     
      return { success: false };
    } catch (error) {
      console.error("Token refresh error:", error);
      return { success: false };
    }
  }
 
// Function to get current user information
export async function getCurrentUser(): Promise<{
  success: boolean;
  user?: UserInfo;
  error?: string;
}> {
  let accessToken;
 
  if (typeof window !== 'undefined') {
    // We're in a browser
    accessToken = localStorage.getItem('access_token');
  } else {
    // We're on the server
    accessToken = (await cookies()).get("access_token")?.value;
  }
 
  if (!accessToken) {
    console.log("No access token found");
    return {
      success: false,
      error: "Not authenticated"
    };
  }
 
  try {
 
   
    const response = await fetch(`${API_BASE_URL}/api/auth/me`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-store",
    });
 
 
    if (!response.ok) {
      // If response is 401 or 403, try to refresh the token
      if (response.status === 401 || response.status === 403) {
        // Attempt to refresh the token
        const refreshResult = await refreshAccessToken();
       
        if (refreshResult.success) {
          // Retry with new token
          const retryResponse = await fetch(`${API_BASE_URL}/api/auth/me`, {
            method: "GET",
            headers: {
              "Authorization": `Bearer ${refreshResult.newAccessToken}`,
            },
            cache: "no-store",
          });
 
          if (retryResponse.ok) {
            const userData = await retryResponse.json();
           
            // Check if we got actual user data back (not empty object)
            if (userData && userData.userId && userData.email) {
              return {
                success: true,
                user: userData
              };
            } else {
              return {
                success: false,
                error: "Account not authorized or pending approval"
              };
            }
          }
        }
       
        // If refresh failed or retry failed
        return {
          success: false,
          error: "Not authorized"
        };
      }
     
      // For other errors
      return {
        success: false,
        error: `Error: ${response.status}`
      };
    }
 
    // If first attempt was successful
    const userData = await response.json();
    // This confirms the user has proper approval, not just valid tokens
    if (userData && userData.userId && userData.email) {
      return {
        success: true,
        user: userData
      };
    } else {
      // User has valid tokens but no data - likely pending approval
      return {
        success: false,
        error: "Account not authorized or pending approval"
      };
    }
 
  } catch (error) {
    console.error("Error fetching user data:", error);
    return {
      success: false,
      error: "An error occurred while fetching user data"
    };
  }
}
 
export async function logoutUser(): Promise<ActionResult> {
  try {
    // Clear the cookies
    const cookieStore = await cookies();
    cookieStore.delete("access_token");
    cookieStore.delete("refresh_token");
   
    return {
      success: true,
      message: "Logged out successfully",
      redirect: "/"
    };
  } catch (error) {
    console.error("Logout error:", error);
    return {
      success: false,
      message: "An error occurred during logout"
    };
  }
}