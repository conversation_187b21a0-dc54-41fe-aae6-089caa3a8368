
"use client";


import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Wand2 } from "lucide-react";
import { useState } from "react";
import { Input } from "../ui/input";
import { AgentTabProps } from "@/types/agent.types";


export default function AgentRoleTab({ agent, setAgent }: AgentTabProps) {
  const [selectedTemplate, setSelectedTemplate] = useState("customer-service");


  const generateFirstMessage = () => {
    // Generate a random first message
    const messages = [
      `Hello there! I'm ${agent.name || "your friend"}, your dedicated ${agent.role || "assistant"}. I'm here to help with any questions or concerns you might have today.`,
      `Welcome! This is ${agent.role || "your assistant"}. I'm ready to assist you with anything you need!`,
      `Hi, thanks for reaching out! I'm ${agent.name || "your assitant"}, and I'll be your ${agent.role || "guide and advisor"} today. How may I help you?`
    ];
    const newMessage = messages[Math.floor(Math.random() * messages.length)];

    setAgent({ ...agent, firstMessage: newMessage });
  };


  const handleFirstMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setAgent({ ...agent, firstMessage: e.target.value });
  };

  const handleSystemContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    
    // Initialize model structure if needed
    const currentModel = agent.model || { messages: [] };
    const currentMessages = currentModel.messages || [];
    
    // Create or update system message
    let updatedMessages;
    const systemMessage = currentMessages.find(m => m.role === "system");
    
    if (systemMessage) {
      // Update existing system message
      updatedMessages = currentMessages.map(m => 
        m.role === "system" ? { ...m, content: newContent } : m
      );
    } else {
      // Add new system message
      updatedMessages = [...currentMessages, { role: "system", content: newContent }];
    }
    // Update agent with new model structure
    setAgent({
      ...agent,
      model: {
        ...currentModel,
        messages: updatedMessages
      }
    });
  };

  const handleVoicemailMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAgent({ ...agent, voicemailMessage: e.target.value });
  };

  return (
    <>
    <div className="space-y-6 p-6">
      {/* Welcome Message */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
          <h3 className="text-lg font-semibold">Welcome Message</h3>
          <Button variant="outline" size="sm" onClick={generateFirstMessage}>
            <Wand2 className="h-4 w-4 mr-2" />
            Generate
          </Button>
        </div>
        <Card className="p-4">
          <Textarea
            className="min-h-[100px] resize-y"
            placeholder="Enter a welcome message that the agent will use to start conversations..."
            value={agent?.firstMessage || ""}
            onChange={handleFirstMessageChange}
          />
        </Card>
      </div>

     {/* System Prompt */}
     <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
          <h3 className="text-lg font-semibold">Role Instructions</h3>
          <div className="flex items-center space-x-2">
            <Select 
              value={selectedTemplate} 
              onValueChange={setSelectedTemplate}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="customer-service">Customer Service</SelectItem>
                <SelectItem value="sales">Sales Agent</SelectItem>
                <SelectItem value="support">Technical Support</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Wand2 className="h-4 w-4 mr-2" />
              Generate
            </Button>
          </div>
        </div>

        <Card className="p-4">
          <Textarea
            id="systemContent"          
            className="max-h-[500px] resize-y"
            placeholder="Describe the role and responsibilities of this agent..."
            value={agent?.model?.messages?.find(m => m.role === "system")?.content || ""}
            onChange={handleSystemContentChange}
          />
        </Card>
      </div>

       {/* Voicemail Message */}
       <div className="space-y-4">
        <h3 className="text-lg font-semibold">Voicemail Message</h3>
        <Card className="p-4">
          <Input
            placeholder="Enter a message to leave when reaching voicemail..."
            value={agent?.voicemailMessage || ""}
            onChange={handleVoicemailMessageChange}
          />
        </Card>
      </div>

      
    </div>
    </>
  );
}