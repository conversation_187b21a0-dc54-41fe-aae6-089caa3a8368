import { ConversationsService } from './conversations.service';
export declare class ConversationsController {
    private readonly conversationsService;
    constructor(conversationsService: ConversationsService);
    start(body: {
        agentId: string;
        userId: string;
        type: 'chat' | 'call';
    }): Promise<import("mongoose").Document<unknown, {}, import("./interfaces/conversation.interface").ConversationDocument> & import("./interfaces/conversation.interface").Conversation & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    sendMessage(conversationId: string, body: {
        message: string;
    }): Promise<string>;
    end(conversationId: string): Promise<import("mongoose").Document<unknown, {}, import("./interfaces/conversation.interface").ConversationDocument> & import("./interfaces/conversation.interface").Conversation & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
}
