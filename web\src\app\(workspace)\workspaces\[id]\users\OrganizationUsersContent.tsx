/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, Plus, ArrowLeft, UserPlus, Shield, ShieldOff, Trash2 } from 'lucide-react';
import { toast } from "sonner";
import { getOrganization, addUserToOrganization, removeUserFromOrganization, Organization } from '@/app/api/organizations';
import { getUsers, User } from '@/app/api/users';

interface OrganizationUsersContentProps {
  organizationId: string;
}

export default function OrganizationUsersContent({ organizationId }: OrganizationUsersContentProps) {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [removeUserDialogOpen, setRemoveUserDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [isAdmin, setIsAdmin] = useState(false);
  const [userToRemove, setUserToRemove] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const router = useRouter();

  useEffect(() => {
    fetchData();
  }, [organizationId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [orgData, usersData] = await Promise.all([
        getOrganization(organizationId),
        getUsers()
      ]);
      setOrganization(orgData);
      setUsers(usersData);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch workspace data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async () => {
    if (!selectedUser) return;

    try {
      setIsSubmitting(true);
      const updated = await addUserToOrganization(organizationId, selectedUser, isAdmin);
      setOrganization(updated);
      setAddUserDialogOpen(false);
      setSelectedUser('');
      setIsAdmin(false);
      toast.success('User added to workspace successfully');
    } catch (error) {
      console.error('Error adding user to Workspace:', error);
      toast.error('Failed to add user to workspace');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveUser = async () => {
    if (!userToRemove) return;

    try {
      setIsSubmitting(true);
      const updated = await removeUserFromOrganization(organizationId, userToRemove);
      setOrganization(updated);
      setRemoveUserDialogOpen(false);
      setUserToRemove(null);
      toast.success('User removed from workspace successfully');
    } catch (error) {
      console.error('Error removing user from Workspace:', error);
      toast.error('Failed to remove user from workspace');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleToggleAdmin = async (userId: string, makeAdmin: boolean) => {
    try {
      setIsSubmitting(true);
      const updated = await addUserToOrganization(organizationId, userId, makeAdmin);
      setOrganization(updated);
      toast.success(`User ${makeAdmin ? 'promoted to admin' : 'demoted from admin'} successfully`);
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAvailableUsers = () => {
    if (!organization) return [];

    const orgUserIds = [
      ...(organization.users || []),
      ...(organization.adminUsers || [])
    ];

    return users.filter(user =>
      !orgUserIds.includes(user._id) &&
      (searchTerm === '' ||
        user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  };

  const getUserById = (userId: string) => {
    return users.find(user => user._id === userId);
  };

  // Helper function to check if a user is an admin
  // This is used in the UI to determine which users are admins

  if (loading) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-8 text-gray-500">
          Workspace not found.
        </div>
      </div>
    );
  }

  const availableUsers = getAvailableUsers();
  const orgUsers = [
    ...(organization.adminUsers || []).map(id => ({ id, isAdmin: true })),
    ...(organization.users || []).map(id => ({ id, isAdmin: false }))
  ];

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={() => router.push(`/workspaces`)} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">{organization.name} - Users</h1>
      </div>

      <div className="flex justify-end mb-6">
        <Dialog open={addUserDialogOpen} onOpenChange={setAddUserDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add User to Organization</DialogTitle>
              <DialogDescription>
                Select a user to add to this organization.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="search" className="text-right">
                  Search
                </Label>
                <Input
                  id="search"
                  placeholder="Search by name or email"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="user" className="text-right">
                  User
                </Label>
                <Select value={selectedUser} onValueChange={setSelectedUser}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a user" />
                  </SelectTrigger>
                  <SelectContent className="z-[700]">
                    {availableUsers.length === 0 ? (
                      <SelectItem value="none" disabled>
                        No available users
                      </SelectItem>
                    ) : (
                      availableUsers.map((user) => (
                        <SelectItem key={user._id} value={user._id}>
                          {user.fullName} ({user.email})
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">
                  Role
                </Label>
                <Select value={isAdmin ? 'admin' : 'user'} onValueChange={(value) => setIsAdmin(value === 'admin')}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent className="z-[700]">
                    <SelectItem value="user">Regular User</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setAddUserDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddUser} disabled={isSubmitting || !selectedUser}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  'Add User'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Organization Users</CardTitle>
          <CardDescription>Manage users in this organization.</CardDescription>
        </CardHeader>
        <CardContent>
          {orgUsers.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No users in this organization. Add users to get started.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orgUsers.map(({ id, isAdmin }) => {
                  const user = getUserById(id);
                  if (!user) return null;

                  return (
                    <TableRow key={id}>
                      <TableCell className="font-medium">{user.fullName}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {isAdmin ? (
                          <Badge className="bg-blue-500">Admin</Badge>
                        ) : (
                          <Badge>User</Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          {isAdmin ? (
                            <Button
                              variant="outline"
                              size="icon"
                              title="Demote to regular user"
                              onClick={() => handleToggleAdmin(id, false)}
                              disabled={isSubmitting}
                            >
                              <ShieldOff className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="icon"
                              title="Promote to admin"
                              onClick={() => handleToggleAdmin(id, true)}
                              disabled={isSubmitting}
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="icon"
                            className="text-red-500"
                            title="Remove from organization"
                            onClick={() => {
                              setUserToRemove(id);
                              setRemoveUserDialogOpen(true);
                            }}
                            disabled={isSubmitting}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Dialog open={removeUserDialogOpen} onOpenChange={setRemoveUserDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove User</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this user from the organization? They will lose access to all organization resources.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRemoveUserDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleRemoveUser} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removing...
                </>
              ) : (
                'Remove'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
