/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, Check, CheckCircle } from "lucide-react";
import { createCampaign } from "@/app/api/campaign";
import SourcesStep from "./steps/SourcesStep";
// import AgentsStep from "./steps/AgentsStep";
// import SettingsStep from "./steps/SettingsStep";
import Link from "next/link";
import { Contact } from "@/app/api/contacts";
import AgentsStep from "./steps/AgentsStep";
import SettingsStep, { DayOfWeek, StatusType } from "./steps/SettingsSteps";
import { Dialog, DialogContent, DialogDescription, DialogFooter, Dialog<PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import ScheduleStep from "./steps/ScheduleStep";
import { Agent } from "@/types/agent.types";


// Campaign data state
interface CampaignData {
  // Basic info
  name: string;
  concurrentCalls: number;
  dailyCost: number;
  startDate: string;
  endDate: string | null;
  successRate: number;
  sentiment: string;
  status: StatusType;
  instantCall?: boolean;
  batchIntervalMinutes?: number;
  // Sources data (step 1)
  sources: Contact[];
  sourceType?: "contacts" | "import" | "thirdparty";
  // Agents data (step 2)
  agents: Agent[];
  agentId?: string;
  // Additional settings (step 3)
  callSchedule: {
    startTime: string;
    endTime: string;
    timezone: string;
    daysOfWeek: DayOfWeek[];
    callTime?: string;
  };
  callWindow: {
    startTime: string;
    endTime: string;
    timezone: string;
    daysOfWeek: DayOfWeek[];
  };
  callRoute?: "outbound" | "inbound" | "both";
  recallHours?: number;
  maxRecalls?: number;
  followUpDays?: DayOfWeek[]; // Changed from string[] to DayOfWeek[]
}



// Steps for campaign creation
const steps = [
  { id: "sources", label: "Sources" },
  { id: "agents", label: "Agent" },
  { id: "schedule", label: "Schedule" },
  { id: "settings", label: "Settings" },
];

export default function CreateCampaign() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [createdCampaignName, setCreatedCampaignName] = useState("");
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);

  // Campaign data state
  const [campaignData, setCampaignData] = useState<CampaignData>({
    // Basic info
    name: "",
    concurrentCalls: 1,
    dailyCost: 0,
    startDate: "",
    endDate: "",
    successRate: 0,
    sentiment: "neutral",
    status: "inactive",
    instantCall: false,
    batchIntervalMinutes: 3,
    // Sources data (step 1)
    sources: [],
    sourceType: "contacts",
    // Agents data (step 2)
    agents: [],
    // Additional settings (step 3)
    callSchedule: {
      startTime: "09:00",
      endTime: "17:00",
      timezone: "America/New_York",
      daysOfWeek: ["monday", "tuesday", "wednesday", "thursday", "friday"]
    },
    callWindow: {
      startTime: "09:00",
      endTime: "17:00",
      timezone: "America/New_York",
      daysOfWeek: ["monday", "tuesday", "wednesday", "thursday", "friday"]
    },
    recallHours: 24,
    maxRecalls: 3,
    followUpDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
  });

  const fetchUserProfile = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token available");
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      const userData = await response.json();
      setUserRole(userData.role);
    } catch (err) {
      console.error("Failed to load user profile:", err);
    }
  };

 // Fetch agents function
 const fetchAgents = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      },
    });
    if (!response.ok) throw new Error('Failed to fetch agents');
    const data = await response.json();
    setCampaignData(prev => ({ ...prev, agents: data }));
  } catch (error) {
    console.error('Error loading agents:', error);
  }
};

// Load agents when component mounts
  useEffect(() => {
    setLoading(true);
    const loadData = async () => {
      await fetchUserProfile();
      await fetchAgents();
      setLoading(false);
    };

    loadData();
  }, []);

  useEffect(() => {
    if (userRole !== null) {
      fetchAgents();
    }
  }, [userRole]);

  // Update campaign data from any step
  const updateCampaignData = (data: Partial<CampaignData>) => {
    setCampaignData(prev => ({ ...prev, ...data }));
  };


  // Handle campaign creation
  const handleCreateCampaign = async (status: StatusType) => {
    setIsSubmitting(true);
    try {
      // Transform data if needed before sending to API
      const formattedData = {
        ...campaignData,
        contacts: campaignData.sources.map(contact => ({
          contactId: contact._id,
          contactName: contact.contactName,
          phoneNumber: contact.phoneNumber,
        })),
        endDate: campaignData.endDate || null,
        status: status
      };

      const newCampaign = await createCampaign(formattedData);
      setCreatedCampaignName(campaignData.name);
      setSuccessDialogOpen(true);
    } catch (err) {
      console.error('Error creating campaign:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

//   // Render current step content
const renderStepContent = () => {
  switch (currentStep) {
    case 0: // Sources
      return <SourcesStep data={campaignData} updateData={updateCampaignData} />;
    case 1: // Agents
      return <AgentsStep data={campaignData} updateData={updateCampaignData} loading={loading}  userRole={userRole} />;
    case 2: // Schedule
      return <ScheduleStep data={campaignData} updateData={(newData) => updateCampaignData(newData as Partial<CampaignData>)} />;
    case 3: // Settings
      return <SettingsStep data={campaignData} updateData={updateCampaignData} />;
    default:
      return null;
  }
};


  const handleNextStep = () => {
    const errors: string[] = [];

    switch (currentStep) {
      case 0: // Sources step
        if (!campaignData.name || campaignData.name.trim() === '') {
          errors.push("Campaign name is required");
        }
        if (campaignData.sources.length === 0) {
          errors.push("Please select at least one contact");
        }
        break;
      case 1: // Agents step
        if (!campaignData.agentId || campaignData.agentId.trim() === '') {
          errors.push("Please select an agent");
        }
        break;
      case 2: // Schedule step
        // Schedule has default values, so we can skip validation
        break;
      case 3: // Settings step
        // Settings has default values, so we can skip validation
        break;
    }

    setValidationErrors(errors);

    if (errors.length === 0) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  return (
    <div className="container max-w-4xl mx-auto py-6">
      <div className="mb-6">
        <Link href="/campaign" className="text-sm text-muted-foreground hover:text-primary flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Campaigns
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Create New Campaign</CardTitle>
          <CardDescription>
            Set up your outbound call campaign in just a few steps
          </CardDescription>
        </CardHeader>

        <CardContent>
          {/* Step indicators */}
          <div className="mb-8">
            <div className="flex justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex flex-col items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center border-2
                      ${index < currentStep
                        ? "bg-primary border-primary text-white"
                        : index === currentStep
                          ? "border-primary text-primary"
                          : "border-gray-300 text-gray-400"}`}
                  >
                    {index < currentStep ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <span>{index + 1}</span>
                    )}
                  </div>
                  <span
                    className={`mt-2 text-sm ${
                      index <= currentStep ? "text-primary font-medium" : "text-gray-500"
                    }`}
                  >
                    {step.label}
                  </span>
                </div>
              ))}
            </div>

            <div className="relative mt-2">
              <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200">
                <div
                  className="h-1 bg-primary transition-all"
                  style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
                />
              </div>
            </div>
          </div>

          {/* Step content */}
          <div className="py-4">
            {renderStepContent()}
          </div>

          {validationErrors.length > 0 && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              {validationErrors.map((error, index) => (
                <p key={index} className="text-red-600 text-sm flex items-center">
                  <span className="mr-2">•</span> {error}
                </p>
              ))}
            </div>
          )}

        </CardContent>

        <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentStep(currentStep - 1)}
          disabled={currentStep === 0}
        >
          Previous
        </Button>

        {currentStep < steps.length - 1 ? (
          <Button onClick={handleNextStep}>
            Next <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => handleCreateCampaign("inactive")}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Campaign"}
            </Button>
            <Button
              onClick={() => handleCreateCampaign("active")}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Publishing..." : "Publish Campaign"}
            </Button>
          </div>
        )}
      </CardFooter>
      </Card>

      {/* Success Dialog */}
      <Dialog open={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Campaign Created Successfully
            </DialogTitle>
            <DialogDescription>
              Your campaign &quot;{createdCampaignName}&quot; has been created and is ready to use.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              You can now view and manage your campaign from the campaigns dashboard.
            </p>
          </div>
          <DialogFooter>
            <Link href="/campaign">
            <Button className="w-full">
              Go to Campaigns
            </Button>
            </Link>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}