"use client";

import { useState} from "react";
// import Link from "next/link";
import { useRouter } from "next/navigation";
import { useFormStatus } from "react-dom";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { loginUser, ActionResult } from "@/app/(auth)/actions/auth";
import ReCAPTCHA from "react-google-recaptcha";
import Image from "next/image";
import OrovaLogo from '@/assets/img/OROVA-WHITE.png';
import { getCurrentUser } from "@/lib/auth-client";
import { Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";

function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button
      type="submit"
      className="w-full h-12 bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md"
      disabled={pending}
    >
      {pending ? (
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2" />
          Signing in...
        </div>
      ) : (
        "Sign In"
      )}
    </Button>
  );
}

export default function Login() {

  const router = useRouter();
  const [formState, setFormState] = useState<ActionResult | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false); // State to toggle password visibility



  async function handleSubmit(formData: FormData) {
    if (!recaptchaToken) {
      setFormState({
        success: false,
        message: "Please complete the CAPTCHA.",
      });
      return;
    }

    formData.append("recaptchaToken", recaptchaToken);

    const result = await loginUser(formData);
    setFormState(result);

    if (result.success && result.redirect && result.tokens) {
      // Store tokens in localStorage
      localStorage.setItem('access_token', result.tokens.access_token);
      localStorage.setItem('refresh_token', result.tokens.refresh_token);

      // Check if the user is actually authorized
      const userResult = await getCurrentUser();

      if (!userResult.success) {
        // If not authorized, show an error
        setFormState({
          success: false,
          message: "Account not authorized. Please contact an administrator.",
        });

        // Remove the tokens since they're not useful
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_data');
        return;
      }

      // Store user data in localStorage for other components to use
      if (userResult.user) {
        localStorage.setItem('user_data', JSON.stringify({
          ...userResult.user,
          _id: userResult.user.userId // Add _id for compatibility with CreditContext
        }));
      }

      router.push(result.redirect);
      return;
    }

    // Handle validation errors
    if (result.fieldErrors) {
      const errors: Record<string, string> = {};
      Object.entries(result.fieldErrors).forEach(([key, messages]) => {
        if (Array.isArray(messages) && messages.length > 0) {
          errors[key] = messages[0];
        }
      });
      setFormErrors(errors);
    } else {
      setFormErrors({});
    }
  }



  return (
    <>
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Side - Brand Panel */}
      <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-[#383D73] to-[#74546D] text-white flex-col justify-center items-center p-8">
        <div className="max-w-md mx-auto flex flex-col items-center space-y-12">
          <Image
            src={OrovaLogo}
            alt="Orova Logo"
            width={280}
            height={70}
          />

          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold">Welcome to Orova AI</h1>
            <p className="text-lg opacity-80">
              The next generation calling platform powered by artificial intelligence
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 w-full">
            <blockquote className="text-center italic">
              &quot;Orova transformed our customer engagement with intelligent calls that feel personal and professional.&quot;
            </blockquote>
            <div className="mt-4 flex items-center justify-center">
              <div className="h-px w-12 bg-white/30"></div>
              <div className="h-px w-12 bg-white/30"></div>
            </div>
          </div>

        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="flex flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900 p-8">
        <div className="w-full max-w-md">
          {/* Mobile Logo - Only visible on mobile */}
          <div className="flex justify-center mb-8 md:hidden">
            <Image
              src={OrovaLogo}
              alt="Orova Logo"
              width={200}
              height={50}
              className="dark:filter dark:brightness-0 dark:invert"
            />
          </div>

          <Card className="bg-white dark:bg-gray-800 shadow-xl rounded-xl border-0">
            <CardHeader className="space-y-2 pb-2">
              <CardTitle className="text-center text-2xl font-bold bg-gradient-to-r from-[#383D73] to-[#74546D] bg-clip-text text-transparent dark:text-white">
                Welcome to Orova
              </CardTitle>
              <p className="text-center text-gray-500 dark:text-gray-400">
                Sign in to your account
              </p>
            </CardHeader>

            <CardContent className="pt-4">
              {formState && !formState.success && !formState.fieldErrors && (
                <Alert className="mb-4 bg-red-50 text-red-800 border-red-200">
                  <AlertDescription>{formState.message}</AlertDescription>
                </Alert>
              )}

              <form action={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Input
                    name="email"
                    type="email"
                    placeholder="Email"
                    required
                    className="h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700"
                    aria-invalid={!!formErrors.email}
                  />
                  {formErrors.email && (
                    <p className="text-sm text-red-500 mt-1">
                      {formErrors.email}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="relative">
                    <Input
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      required
                      className="h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700 pr-10"
                      aria-invalid={!!formErrors.password}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(prev => !prev)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none"
                      tabIndex={-1} // Skip tab focusing
                      aria-label={showPassword ? "Hide password" : "Show password"}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                  {formErrors.password && (
                    <p className="text-sm text-red-500 mt-1">
                      {formErrors.password}
                    </p>
                  )}
                </div>
                <div className="flex justify-center">
                <ReCAPTCHA
                  sitekey="6LfyPfgqAAAAAJy91ZTqWkEaQcGJJtNC-MnxUa6e"
                  onChange={(token) => setRecaptchaToken(token)}
                />
                </div>


                <div className="flex items-center justify-between">

                  {/* <div className="text-sm">
                    <a href="#" className="font-medium text-[#383D73] hover:text-[#74546D]">
                      Forgot password?
                    </a>
                  </div> */}
                </div>

                <SubmitButton />

                {/* <div className="text-center text-sm mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Link
                    href="/register"
                    className="text-[#383D73] hover:text-[#74546D] font-medium transition-colors duration-200"
                  >
                    Need an account? Sign up
                  </Link>
                </div> */}
              </form>
            </CardContent>
          </Card>

          <div className="mt-6 text-center text-xs text-gray-500 dark:text-gray-400">
            &copy; {new Date().getFullYear()} Orova AI. All rights reserved.
          </div>
        </div>
      </div>
    </div>
    </>
  );
}