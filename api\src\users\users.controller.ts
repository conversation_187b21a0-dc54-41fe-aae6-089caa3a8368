import { <PERSON>, Post, Body, Patch, Param, Delete, Get, Query, UseGuards, Req, BadRequestException, NotFoundException, HttpException, HttpStatus, Inject, forwardRef } from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
// import { Roles } from '../auth/decorators/roles.decorator';
import { ApiBody, ApiOperation, ApiResponse, ApiTags, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { RegisterUserDto } from './dto/register-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { AutoRechargeService } from '../credit/auto-recharge.service';


// Extend the Express Request type to include user property
interface RequestWith<PERSON>ser extends Request {
  user: {
    userId: string;
    email: string;
    name?: string;
    role?: string;
  };
}

// Auto-recharge settings DTO
class AutoRechargeSettingsDto {
  autoRechargeEnabled: boolean;
  autoRechargeThreshold: number;
  autoRechargeAmount: number;
}

@Controller('users')
export class UsersController {
  constructor(
    private usersService: UsersService,
    @Inject(forwardRef(() => AutoRechargeService))
    private autoRechargeService: AutoRechargeService
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User successfully registered' })
  @ApiBody({ type: RegisterUserDto })
  async register(@Body() userDto: { email: string; password: string; fullName: string }) {
    await this.usersService.create(userDto);
    return 'User registered successfully! Awaiting admin approval.';
  }

  @Patch('approve/:id')
  @UseGuards(JwtAuthGuard,RolesGuard)
  @ApiOperation({ summary: 'Approve user' })
  @ApiResponse({ status: 200, description: 'User approved successfully' })
  @ApiParam({ name: 'id', description: 'User ID' })
  async approveUser(@Param('id') id: string) {
    await this.usersService.approveUser(id);
    return { message: 'User approved successfully' };
  }

  @Get()
  @UseGuards(JwtAuthGuard,RolesGuard)
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Returns all users' })
  async getAllUsers() {
    return this.usersService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard,RolesGuard)
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'Returns a user by ID' })
  @ApiParam({ name: 'id', description: 'User ID' })
  async getUserById(@Param('id') id: string) {
    return this.usersService.findById(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard,RolesGuard)
  @ApiOperation({ summary: 'Update user' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ type: UpdateUserDto })
  async updateUser(@Param('id') id: string, @Body() updateDto: UpdateUserDto) {
    await this.usersService.updateUser(id, updateDto);
    return { message: 'User updated successfully' };
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard,RolesGuard)
  @ApiOperation({ summary: 'Delete user' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiParam({ name: 'id', description: 'User ID' })
  async deleteUser(@Param('id') id: string) {
    await this.usersService.deleteUser(id);
    return { message: 'User deleted successfully' };
  }

  @Get('me/credits')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user credits' })
  @ApiResponse({
    status: 200,
    description: 'Returns the current user credits in both dollars and minutes',
    schema: {
      type: 'object',
      properties: {
        credits: { type: 'number' },
        minutes: { type: 'number' },
        callPricePerMinute: { type: 'number' }
      }
    }
  })
  async getUserCredits(@Req() req: RequestWithUser) {
    const userId = req.user.userId;
    const user = await this.usersService.findById(userId);

    // Check if user belongs to an organization
    if (!user.organizationId) {
      console.error(`User ${userId} has no organization assigned`);
      return {
        freeCreditsRemaining: 0,
        paidCredits: 0,
        totalAvailable: 0,
        usingFreeCredits: false,
        freeMinutesRemaining: 0,
        paidMinutes: 0,
        totalMinutesAvailable: 0,
        callPricePerMinute: 0.1,
        monthlyResetDate: 1,
        monthlyAllowance: 0,
        minimumCreditsThreshold: 1.0,
        // Legacy fields for backward compatibility
        credits: 0,
        minutes: 0
      };
    }

    try {
      // Get a direct reference to the organizations service for a fresh fetch
      const organizationsService = this.usersService.getOrganizationsService();

      // Get available credits information using the new monthly credits system
      const availableCredits = await organizationsService.getAvailableCredits(user.organizationId.toString());

      // Get organization details for reset date and billing settings
      const organization = await organizationsService.findOne(user.organizationId.toString());

      // Get the price per minute from organization settings
      const callPricePerMinute = organization.callPricePerMinute;

      // Calculate minutes from credits
      const freeMinutesRemaining = availableCredits.freeCreditsRemaining;
      const paidMinutes = callPricePerMinute > 0 ? availableCredits.paidCredits / callPricePerMinute : 0;
      const totalMinutesAvailable = freeMinutesRemaining + paidMinutes;

      return {
        freeCreditsRemaining: availableCredits.freeCreditsRemaining,
        paidCredits: availableCredits.paidCredits,
        totalAvailable: availableCredits.totalAvailable,
        usingFreeCredits: availableCredits.usingFreeCredits,
        freeMinutesRemaining,
        paidMinutes,
        totalMinutesAvailable,
        callPricePerMinute,
        monthlyResetDate: organization.monthlyResetDate || 1,
        monthlyAllowance: organization.monthlyMinutesAllowance || 0,
        minimumCreditsThreshold: organization.minimumCreditsThreshold || 1.0,
        // Legacy fields for backward compatibility
        credits: availableCredits.totalAvailable,
        minutes: totalMinutesAvailable
      };
    } catch (error) {
      console.error('Error getting organization credits:', error);
      return {
        freeCreditsRemaining: 0,
        paidCredits: 0,
        totalAvailable: 0,
        usingFreeCredits: false,
        freeMinutesRemaining: 0,
        paidMinutes: 0,
        totalMinutesAvailable: 0,
        callPricePerMinute: 0.1,
        monthlyResetDate: 1,
        monthlyAllowance: 0,
        minimumCreditsThreshold: 1.0,
        // Legacy fields for backward compatibility
        credits: 0,
        minutes: 0
      };
    }
  }

  @Get('me/auto-recharge')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get auto-recharge settings for the user\'s organization' })
  @ApiResponse({
    status: 200,
    description: 'Returns the auto-recharge settings',
    schema: {
      type: 'object',
      properties: {
        autoRechargeEnabled: { type: 'boolean' },
        autoRechargeThreshold: { type: 'number' },
        autoRechargeAmount: { type: 'number' }
      }
    }
  })
  async getAutoRechargeSettings(@Req() req: RequestWithUser) {
    try {
      const userId = req.user.userId;

      // Get the user's organization
      const organization = await this.usersService.getUserOrganization(userId);

      if (!organization) {
        throw new NotFoundException('User does not belong to an organization');
      }

      // Return the auto-recharge settings from the organization
      return {
        autoRechargeEnabled: organization.autoRechargeEnabled,
        autoRechargeThreshold: organization.autoRechargeThreshold,
        autoRechargeAmount: organization.autoRechargeAmount
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get auto-recharge settings',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch('me/auto-recharge')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update auto-recharge settings for the user\'s organization' })
  @ApiResponse({ status: 200, description: 'Auto-recharge settings updated successfully' })
  @ApiBody({ type: AutoRechargeSettingsDto })
  async updateAutoRechargeSettings(
    @Req() req: RequestWithUser,
    @Body() updateDto: AutoRechargeSettingsDto
  ) {
    try {
      const userId = req.user.userId;

      // Get the user's organization
      const organization = await this.usersService.getUserOrganization(userId);

      if (!organization) {
        throw new NotFoundException('User does not belong to an organization');
      }

      // Update the organization's auto-recharge settings
      const organizationsService = this.usersService.getOrganizationsService();
      const updatedOrganization = await organizationsService.updateBilling(
        organization._id.toString(),
        {
          autoRechargeEnabled: updateDto.autoRechargeEnabled,
          autoRechargeThreshold: updateDto.autoRechargeThreshold,
          autoRechargeAmount: updateDto.autoRechargeAmount
        }
      );

      // Return the updated settings
      return {
        message: 'Auto-recharge settings updated successfully',
        settings: {
          autoRechargeEnabled: updatedOrganization.autoRechargeEnabled,
          autoRechargeThreshold: updatedOrganization.autoRechargeThreshold,
          autoRechargeAmount: updatedOrganization.autoRechargeAmount
        }
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update auto-recharge settings',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('me/test-auto-recharge')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Test auto-recharge functionality' })
  @ApiResponse({ status: 200, description: 'Auto-recharge test results' })
  async testAutoRecharge(@Req() req: RequestWithUser) {
    try {
      const userId = req.user.userId;

      // Get the user's organization
      const organization = await this.usersService.getUserOrganization(userId);

      if (!organization) {
        throw new NotFoundException('User does not belong to an organization');
      }

      // Log current settings and credits
      const currentSettings = {
        autoRechargeEnabled: organization.autoRechargeEnabled,
        autoRechargeThreshold: organization.autoRechargeThreshold,
        autoRechargeAmount: organization.autoRechargeAmount,
        currentCredits: organization.credits
      };

      // Attempt to trigger auto-recharge
      const autoRechargeResult = await this.autoRechargeService.checkAndProcessAutoRecharge(userId);

      // Get updated organization data
      const updatedOrganization = await this.usersService.getUserOrganization(userId);

      return {
        message: autoRechargeResult ? 'Auto-recharge test successful' : 'Auto-recharge test did not trigger a payment',
        beforeTest: currentSettings,
        afterTest: {
          autoRechargeEnabled: updatedOrganization.autoRechargeEnabled,
          autoRechargeThreshold: updatedOrganization.autoRechargeThreshold,
          autoRechargeAmount: updatedOrganization.autoRechargeAmount,
          currentCredits: updatedOrganization.credits
        },
        autoRechargeTriggered: autoRechargeResult
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to test auto-recharge',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
