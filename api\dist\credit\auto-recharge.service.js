"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AutoRechargeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoRechargeService = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("../users/users.service");
const billing_service_1 = require("../billing/billing.service");
const organizations_service_1 = require("../organizations/organizations.service");
let AutoRechargeService = AutoRechargeService_1 = class AutoRechargeService {
    constructor(usersService, billingService, organizationsService) {
        this.usersService = usersService;
        this.billingService = billingService;
        this.organizationsService = organizationsService;
        this.logger = new common_1.Logger(AutoRechargeService_1.name);
    }
    async checkAndProcessAutoRecharge(userId) {
        try {
            this.logger.log(`Auto-recharge check initiated for user ${userId}`);
            const user = await this.usersService.findById(userId);
            if (!user.organizationId) {
                this.logger.warn(`Auto-recharge skipped for user ${userId}: No organization found`);
                return false;
            }
            const organization = await this.organizationsService.findOne(user.organizationId.toString());
            if (!organization.autoRechargeEnabled) {
                this.logger.warn(`Auto-recharge skipped for organization ${organization._id}: Auto-recharge is disabled`);
                return false;
            }
            const orgCredits = organization.credits || 0;
            const threshold = organization.autoRechargeThreshold || 1.0;
            this.logger.log(`Organization ${organization._id} credits: $${orgCredits.toFixed(2)}, threshold: $${threshold.toFixed(2)}`);
            if (orgCredits > threshold) {
                this.logger.log(`Auto-recharge not needed for organization ${organization._id}: Credits ($${orgCredits.toFixed(2)}) are above threshold ($${threshold.toFixed(2)})`);
                return false;
            }
            this.logger.log(`Fetching payment methods for organization ${organization._id}`);
            const paymentMethods = await this.billingService.getOrganizationPaymentMethods(organization._id.toString());
            this.logger.log(`Found ${paymentMethods.length} payment methods for organization ${organization._id}`);
            const defaultPaymentMethod = paymentMethods.find(pm => pm.isDefault);
            if (!defaultPaymentMethod) {
                this.logger.warn(`Auto-recharge failed for organization ${organization._id}: No default payment method found`);
                return false;
            }
            this.logger.log(`Using default payment method: ${defaultPaymentMethod._id} (${defaultPaymentMethod.stripePaymentMethodId})`);
            let rechargeAmount = organization.autoRechargeAmount || 0;
            this.logger.log(`Initial auto-recharge amount from settings: $${rechargeAmount.toFixed(2)}`);
            if (!rechargeAmount || rechargeAmount <= 0) {
                this.logger.log(`Auto-recharge amount not set for organization ${organization._id}, checking transaction history`);
                const transactionHistory = await this.billingService.getOrganizationTransactionHistory(organization._id.toString(), 1, 10);
                if (transactionHistory && transactionHistory.transactions && transactionHistory.transactions.length > 0) {
                    this.logger.log(`Found ${transactionHistory.transactions.length} transactions in history`);
                    const lastSuccessfulPayment = transactionHistory.transactions.find(t => t.status === 'succeeded' && t.amount > 0 && !t.description?.includes('Auto-recharge'));
                    if (lastSuccessfulPayment) {
                        rechargeAmount = lastSuccessfulPayment.amount;
                        this.logger.log(`Using last payment amount for auto-recharge: $${rechargeAmount.toFixed(2)} (transaction ID: ${lastSuccessfulPayment._id})`);
                    }
                    else {
                        this.logger.log(`No suitable previous payment found in transaction history`);
                    }
                }
                else {
                    this.logger.log(`No transaction history found for organization ${organization._id}`);
                }
                if (!rechargeAmount || rechargeAmount <= 0) {
                    rechargeAmount = 10.0;
                    this.logger.log(`No previous payment found, using default minimum amount: $${rechargeAmount.toFixed(2)}`);
                }
            }
            if (rechargeAmount < 10.0) {
                this.logger.log(`Recharge amount $${rechargeAmount.toFixed(2)} is below minimum, adjusting to $10.00`);
                rechargeAmount = 10.0;
            }
            const amountInCents = Math.round(rechargeAmount * 100);
            this.logger.log(`Processing payment of $${rechargeAmount.toFixed(2)} (${amountInCents} cents) for organization ${organization._id}`);
            try {
                this.logger.log(`Calling billingService.processOrganizationPayment with organizationId: ${organization._id}, userId: ${userId}, paymentMethodId: ${defaultPaymentMethod.stripePaymentMethodId}`);
                await this.billingService.processOrganizationPayment(organization._id.toString(), userId, user.email, defaultPaymentMethod.stripePaymentMethodId, {
                    amount: amountInCents,
                    currency: 'usd',
                    description: `Auto-recharge: $${rechargeAmount.toFixed(2)}`,
                }, organization.name, false, false);
                this.logger.log(`Auto-recharge payment processed successfully for organization ${organization._id}`);
                const updatedOrganization = await this.organizationsService.findOne(organization._id.toString());
                this.logger.log(`Organization credits after auto-recharge: $${updatedOrganization.credits.toFixed(2)} (was $${orgCredits.toFixed(2)})`);
                this.logger.log(`Auto-recharge successful for organization ${organization._id}: Added $${rechargeAmount.toFixed(2)}`);
                return true;
            }
            catch (paymentError) {
                this.logger.error(`Payment processing failed during auto-recharge for organization ${organization._id}:`, paymentError);
                throw paymentError;
            }
        }
        catch (error) {
            this.logger.error(`Auto-recharge failed for user ${userId}:`, error);
            if (error.response) {
                this.logger.error(`Error response: ${JSON.stringify(error.response.data || {})}`);
            }
            return false;
        }
    }
};
exports.AutoRechargeService = AutoRechargeService;
exports.AutoRechargeService = AutoRechargeService = AutoRechargeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        billing_service_1.BillingService,
        organizations_service_1.OrganizationsService])
], AutoRechargeService);
//# sourceMappingURL=auto-recharge.service.js.map