import { Injectable, NestMiddleware, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { UsersService } from 'src/users/users.service';
import { AutoRechargeService } from './auto-recharge.service';
import { OrganizationsService } from 'src/organizations/organizations.service';

// Define a custom interface that extends Request to include user property
interface RequestWithUser extends Request {
  user?: {
    userId: string;
    email: string;
    role?: string;
    fullName?: string;
  };
}

@Injectable()
export class CreditCheckMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CreditCheckMiddleware.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly autoRechargeService: AutoRechargeService,
    private readonly organizationsService: OrganizationsService
  ) {}

  async use(req: RequestWithUser, res: Response, next: NextFunction) {
    try {
      // Skip credit check for certain routes
      const skipRoutes = [
        '/api/auth',
        '/api/users/credits',
        '/api/billing',
        '/api/organizations',
      ];

      // Check if the current route should skip credit check
      for (const route of skipRoutes) {
        if (req.path.startsWith(route)) {
          // this.logger.debug(`Skipping credit check for route: ${req.path} (in skip list)`);
          return next();
        }
      }

      // Skip credit check for GET requests (read operations)
      if (req.method === 'GET') {
        // this.logger.debug(`Skipping credit check for route: ${req.path} (GET request)`);
        return next();
      }

      // Get user ID from request (assuming it's set by auth middleware)
      const userId = req.user?.userId;

      // If no user ID, skip credit check (likely an unauthenticated route)
      if (!userId) {
        // this.logger.debug(`Skipping credit check for route: ${req.path} (no user ID)`);
        return next();
      }

      this.logger.log(`Checking credits for user ${userId} on route: ${req.method} ${req.path}`);

      // Check if user has sufficient credits
      const hasSufficientCredits = await this.usersService.hasSufficientCredits(userId, 1);

      if (!hasSufficientCredits) {
        this.logger.log(`Insufficient credits for user ${userId}, attempting auto-recharge`);

        // Try auto-recharge if credits are insufficient
        const autoRechargeSuccessful = await this.autoRechargeService.checkAndProcessAutoRecharge(userId);

        if (autoRechargeSuccessful) {
          // Auto-recharge was successful, continue with the request
          this.logger.log(`Auto-recharge successful for user ${userId}, allowing request to proceed`);
          return next();
        }

        this.logger.warn(`Auto-recharge failed or not enabled for user ${userId}, returning payment required error`);

        // Auto-recharge failed or not enabled, return error
        return res.status(HttpStatus.PAYMENT_REQUIRED).json({
          error: 'Insufficient credits. Please add funds to your account.',
        });
      }

      this.logger.log(`User ${userId} has sufficient credits, allowing request to proceed`);


      next();
    } catch (error) {
      // Log the error but allow the request to proceed
      console.error('Error in credit check middleware:', error);
      next();
    }
  }
}
