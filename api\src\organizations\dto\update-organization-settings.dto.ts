import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>N<PERSON><PERSON>, <PERSON>Optional, <PERSON>, Max, IsString, IsEmail } from 'class-validator';

export class UpdateOrganizationSettingsDto {
  @ApiProperty({ description: 'Day of month when credits reset (1-28)', example: 1 })
  @IsNumber()
  @Min(1)
  @Max(28)
  @IsOptional()
  monthlyResetDate?: number;

  @ApiProperty({ description: 'Client full name for email personalization', example: '<PERSON>' })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ description: 'Email address to send credit notifications to', example: '<EMAIL>' })
  @IsEmail()
  @IsOptional()
  email?: string;
}
