/* eslint-disable @typescript-eslint/no-unused-vars */

"use client";

import { useState } from "react";
import Link from "next/link";
import { useFormStatus } from "react-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { registerUser, ActionResult } from "@/app/(auth)/actions/auth";
import { ArrowRight, CheckCircle2, XCircle } from "lucide-react";
import Image from "next/image";
import OrovaLogo from '@/assets/img/OROVA-WHITE.png';

function SubmitButton() {
  const { pending } = useFormStatus();
  
  return (
    <Button
      type="submit"
      className="w-full h-12 bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md"
      disabled={pending}
    >
      {pending ? (
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2" />
          Processing...
        </div>
      ) : (
        "Create Account"
      )}
    </Button>
  );
}

// Success feedback component
function RegistrationSuccess({ message }: { message: string }) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-xl">
          <CardContent className="p-8 flex flex-col items-center text-center">
            <div className="h-24 w-24 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mb-6">
              <CheckCircle2 className="h-12 w-12 text-green-600 dark:text-green-400" />
            </div>
            
            <h2 className="text-2xl font-bold mb-2">Registration Successful!</h2>
            
            <p className="text-gray-800 dark:text-gray-300 mb-8">
              {message}
            </p>
            
            <div className="flex flex-col space-y-4 w-full">
              <Link 
                href="/login"
                className="inline-flex items-center justify-center h-12 px-6 w-full bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold rounded-md transition-all duration-200 hover:scale-[1.02] hover:shadow-md"
              >
                Go to Login <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
              
              <Link 
                href="/"
                className="inline-flex items-center justify-center h-12 px-6 w-full text-gray-700 dark:text-gray-300 font-medium border border-gray-300 dark:border-gray-700 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                Return to Home
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }


// Error feedback component
function RegistrationError({ message, onRetry }: { message: string; onRetry: () => void }) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-xl">
          <CardContent className="p-8 flex flex-col items-center text-center">
            <div className="h-24 w-24 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-6">
              <XCircle className="h-12 w-12 text-red-600 dark:text-red-400" />
            </div>
            
            <h2 className="text-2xl font-bold mb-2">Registration Failed</h2>
            
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              {message}
            </p>
            
            <div className="flex flex-col space-y-4 w-full">
              <Button 
                onClick={onRetry}
                className="h-12 w-full bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md"
              >
                Try Again
              </Button>
              
              <Link 
                href="/"
                className="inline-flex items-center justify-center h-12 px-6 w-full text-gray-700 dark:text-gray-300 font-medium border border-gray-300 dark:border-gray-700 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                Return to Home
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }



export default function Register() {
  const [formState, setFormState] = useState<ActionResult | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isRegistrationComplete, setIsRegistrationComplete] = useState(false);
  
  async function handleSubmit(formData: FormData) {
    const result = await registerUser(formData);
    setFormState(result);
    
    // If registration was successful, show the success component
    if (result.success) {
      setIsRegistrationComplete(true);
      return;
    }
    
    // Otherwise, handle validation errors
    if (result.fieldErrors) {
      const errors: Record<string, string> = {};
      Object.entries(result.fieldErrors).forEach(([key, messages]) => {
        if (Array.isArray(messages) && messages.length > 0) {
          errors[key] = messages[0];
        }
      });
      setFormErrors(errors);
    } else {
      setFormErrors({});
    }
  }

   // Handle retry for error case
   const handleRetry = () => {
    setFormState(null);
    setFormErrors({});
    setIsRegistrationComplete(false);
  };

  // If registration is complete and successful, show success component
  if (isRegistrationComplete && formState?.success) {
    return <RegistrationSuccess message={formState.message} />;
  }
  
  // If registration is complete but failed with a non-validation error, show error component
  if (isRegistrationComplete && !formState?.success && !formState?.fieldErrors) {
    return <RegistrationError message={formState?.message || "An error occurred during registration"} onRetry={handleRetry} />;
  }

  return (
    <>
     {/* <div className="min-h-screen flex flex-col md:flex-row">
     
      <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-[#383D73] to-[#74546D] text-white flex-col justify-center items-center p-8">
        <div className="max-w-md mx-auto flex flex-col items-center space-y-12">
          <Image 
            src={OrovaLogo} 
            alt="Orova Logo" 
            width={280} 
            height={70}
          />
          
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold">Join Orova AI</h1>
            <p className="text-lg opacity-80">
              Create your account and start building powerful AI voice agents today
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 w-full">
            <blockquote className="text-center italic">
              &quot;Our AI voice agents provide a seamless experience that increases conversions and enhances customer satisfaction.&quot;
            </blockquote>
            <div className="mt-4 flex items-center justify-center">
              <div className="h-px w-12 bg-white/30"></div>
              <div className="h-px w-12 bg-white/30"></div>
            </div>
          </div>
          
        </div>
      </div>
      
      <div className="flex flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900 p-8">
        <div className="w-full max-w-md">
        
          <div className="flex justify-center mb-8 md:hidden">
            <Image 
              src={OrovaLogo} 
              alt="Orova Logo" 
              width={200} 
              height={50}
            />
          </div>
          
          <Card className="bg-white dark:bg-gray-800 shadow-xl rounded-xl border-0">
            <CardHeader className="space-y-2 pb-2">
              <CardTitle className="text-center text-2xl font-bold bg-gradient-to-r from-[#383D73] to-[#74546D] bg-clip-text text-transparent">
                Create Account
              </CardTitle>
              <p className="text-center text-gray-500 dark:text-gray-400">
                Sign up to start creating AI agents
              </p>
            </CardHeader>
            
            <CardContent className="pt-4">
              {formState && !formState.success && !formState.fieldErrors && (
                <Alert className="mb-4 bg-red-50 text-red-800 border-red-200">
                  <AlertDescription>{formState.message}</AlertDescription>
                </Alert>
              )}
              
              <form action={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Input
                    name="fullName"
                    type="text"
                    placeholder="Full Name"
                    required
                    className="h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700"
                    aria-invalid={!!formErrors.fullName}
                  />
                  {formErrors.fullName && (
                    <p className="text-sm text-red-500 mt-1">
                      {formErrors.fullName}
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Input
                    name="email"
                    type="email"
                    placeholder="Email"
                    required
                    className="h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700"
                    aria-invalid={!!formErrors.email}
                  />
                  {formErrors.email && (
                    <p className="text-sm text-red-500 mt-1">
                      {formErrors.email}
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Input
                    name="password"
                    type="password"
                    placeholder="Password"
                    required
                    className="h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700"
                    aria-invalid={!!formErrors.password}
                  />
                  {formErrors.password && (
                    <p className="text-sm text-red-500 mt-1">
                      {formErrors.password}
                    </p>
                  )}
                </div>

             

                <SubmitButton />

                <div className="text-center text-sm mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Link 
                    href="/login"
                    className="text-[#383D73] hover:text-[#74546D] font-medium transition-colors duration-200"
                  >
                    Already have an account? Sign in
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
          
          <div className="mt-6 text-center text-xs text-gray-500 dark:text-gray-400">
            &copy; {new Date().getFullYear()} Orova AI. All rights reserved.
          </div>
        </div>
      </div>
    </div> */}
    </>
  );
}