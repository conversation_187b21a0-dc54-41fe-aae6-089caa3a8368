import { Model } from 'mongoose';
import { LogDocument } from './interfaces/log.interface';
export declare class LoggerService {
    private readonly logModel;
    private logger;
    private logFilePath;
    constructor(logModel: Model<LogDocument>);
    private writeToFile;
    private saveToDatabase;
    log(message: string): Promise<void>;
    warn(message: string): Promise<void>;
    error(message: string, trace?: any): Promise<void>;
    deleteOldLogs(days?: number): Promise<number>;
    scheduledLogCleanup(): Promise<void>;
}
