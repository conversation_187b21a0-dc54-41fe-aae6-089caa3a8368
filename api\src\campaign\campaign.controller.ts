import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseGuards,
  } from '@nestjs/common';
import { User } from '../auth/decorators/user.decorator';
import { FullUserType } from '../auth/interceptors/user-redaction.interceptor';
  import { CampaignService } from './campaign.service';
  import { CampaignDto, UpdateCampaignStatusDto } from './dto/campaign.dto';
  import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
  import { RolesGuard } from '../auth/guards/roles.guard';
  import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiParam,
    ApiQuery,
  } from '@nestjs/swagger';

  @ApiTags('Campaigns')
  @Controller('campaigns')
  @UseGuards(JwtAuthGuard, RolesGuard)
  export class CampaignController {
    constructor(private readonly campaignService: CampaignService) {}

    @Post()
    @ApiOperation({ summary: 'Create a new campaign' })
    @ApiResponse({ status: 201, description: 'Campaign successfully created' })
    @ApiBody({ type: CampaignDto })
    create(@Body() createCampaignDto: CampaignDto, @User() user: FullUserType) {
      createCampaignDto.createdBy = user.fullName || 'User';
      return this.campaignService.create(createCampaignDto);
    }

    @Get()
    @ApiOperation({ summary: 'Get all campaigns' })
    @ApiResponse({ status: 200, description: 'List of campaigns' })
    @ApiQuery({
      name: 'status',
      required: false,
      description: 'Filter campaigns by status (active, paused, completed)',
    })
    @ApiQuery({
      name: 'search',
      required: false,
      description: 'Search campaigns by name',
    })
    findAll(
      @Query('status') status?: string,
      @Query('search') search?: string,
    ) {
      if (search) {
        return this.campaignService.search(search);
      }
      return this.campaignService.findAll(status);
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a campaign by ID' })
    @ApiResponse({ status: 200, description: 'The requested campaign' })
    @ApiParam({ name: 'id', description: 'Campaign ID' })
    findOne(@Param('id') id: string) {
      return this.campaignService.findById(id);
    }

    @Patch(':id')
    @ApiOperation({ summary: 'Update a campaign by ID' })
    @ApiResponse({ status: 200, description: 'Campaign updated' })
    @ApiParam({ name: 'id', description: 'Campaign ID' })
    @ApiBody({ type: CampaignDto })
    update(@Param('id') id: string, @Body() updateCampaignDto: CampaignDto, @User() user: FullUserType) {
      updateCampaignDto.createdBy = user.fullName || 'User';
      return this.campaignService.update(id, updateCampaignDto);
    }

    @Patch(':id/status')
    @ApiOperation({ summary: 'Update campaign status' })
    @ApiResponse({ status: 200, description: 'Campaign status updated' })
    @ApiParam({ name: 'id', description: 'Campaign ID' })
    @ApiBody({ type: UpdateCampaignStatusDto })
    updateStatus(
      @Param('id') id: string,
      @Body() statusDto: UpdateCampaignStatusDto,
    ) {
      return this.campaignService.updateStatus(id, statusDto);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Delete a campaign by ID' })
    @ApiResponse({ status: 200, description: 'Campaign deleted' })
    @ApiParam({ name: 'id', description: 'Campaign ID' })
    remove(@Param('id') id: string) {
      return this.campaignService.remove(id);
    }

    @Get(':id/metrics')
    @ApiOperation({ summary: 'Get campaign performance metrics' })
    @ApiResponse({ status: 200, description: 'Campaign performance metrics' })
    @ApiParam({ name: 'id', description: 'Campaign ID' })
    getMetrics(@Param('id') id: string) {
      return this.campaignService.calculatePerformanceMetrics(id);
    }
  }