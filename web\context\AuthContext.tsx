"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';

type UserInfo = {
  id: string;
  email: string;
  role: string;
  isApproved: boolean;
  fullName?: string;
};

type AuthContextType = {
  user: UserInfo | null;
  accessToken: string | null;
  loading: boolean;
  login: (token: string, refreshToken: string, userData: UserInfo) => void;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
};

// Create context with a default undefined value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Load user from localStorage on initial mount
  useEffect(() => {
    const loadUserFromStorage = async () => {
      try {
        // Check if we're in a browser environment
        if (typeof window === "undefined") {
          setLoading(false);
          return;
        }

        const token = localStorage.getItem('access_token');

        if (!token) {
          setLoading(false);
          return;
        }

        // Fetch user data with the token
        const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {
          headers: {
            "Authorization": `Bearer ${token}`
          }
        });

        if (!response.ok) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          setLoading(false);
          return;
        }

        const userData = await response.json();
        setUser(userData);
        setAccessToken(token);
      } catch (error) {
        console.error('Error loading user:', error);
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
      } finally {
        setLoading(false);
      }
    };

    loadUserFromStorage();
  }, []);

  const login = (token: string, refreshToken: string, userData: UserInfo) => {
    localStorage.setItem('access_token', token);
    localStorage.setItem('refresh_token', refreshToken);
    setAccessToken(token);
    setUser(userData);
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');
    setUser(null);
    setAccessToken(null);
    router.push('/login');
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const currentRefreshToken = localStorage.getItem('refresh_token');

      if (!currentRefreshToken) {
        return false;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: currentRefreshToken }),
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();

      if (data.access_token) {
        localStorage.setItem('access_token', data.access_token);
        setAccessToken(data.access_token);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  };

  const value = {
    user,
    accessToken,
    loading,
    login,
    logout,
    refreshToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
}