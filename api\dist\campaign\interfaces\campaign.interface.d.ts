import { Document } from 'mongoose';
export interface Campaign {
    name: string;
    createdBy?: string;
    concurrentCalls: number;
    dailyCost: number;
    startDate: Date;
    endDate: Date;
    successRate: number;
    sentiment: 'positive' | 'neutral' | 'negative';
    status: 'active' | 'paused' | 'completed' | 'inactive';
    createdAt?: Date;
    updatedAt?: Date;
    contacts?: Array<{
        contactId: any;
        contactName: string;
        phoneNumber: string;
    }>;
    agentId?: string;
    recallHours?: number;
    maxRecalls?: number;
    followUpDays?: string[];
    callSchedule?: {
        startTime: string;
        endTime: string;
        timezone: string;
        daysOfWeek: string[];
        callTime?: string;
    };
    callWindow?: {
        startTime: string;
        endTime: string;
        timezone?: string;
        daysOfWeek: string[];
    };
    instantCall?: boolean;
}
export interface CampaignDocument extends Campaign, Document {
}
