"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { IntegrationCard } from "@/components/IntegrationCard";

type Integration = {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  status: "installed" | "not_installed" | "coming_soon";
  website?: string;
};

const integrations: Integration[] = [
  {
    id: "webhook",
    name: "Webhooks",
    description: "Create custom integrations with your own systems",
    category: "Developer Tools",
    icon: "https://cdn-icons-png.flaticon.com/512/6295/6295417.png",
    status: "not_installed",
    website: "https://docs.orova.ai/webhooks",
  },
  {
    id: "calcom",
    name: "Cal.com",
    description: "Organize events and meetings",
    category: "Calendar",
    icon: "https://cal.com/android-chrome-512x512.png",
    status: "not_installed",
  },
  {
    id: "google-calendar",
    name: "Google Calendar",
    description: "Time management and scheduling service",
    category: "Calendar",
    icon: "https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Calendar_icon_%282020%29.svg",
    status: "installed",
  },
  {
    id: "twilio",
    name: "Twilio",
    description: "Make and receive calls around the world",
    category: "Voice & video",
    icon: "https://www.vectorlogo.zone/logos/twilio/twilio-icon.svg",
    status: "coming_soon",
  },
  {
    id: "email",
    name: "Email",
    description: "Send emails directly from",
    category: "Communication",
    icon: "https://cdn-icons-png.flaticon.com/512/732/732200.png",
    status: "coming_soon",
  },
  {
    id: "sms",
    name: "SMS",
    description: "Send SMS directly from",
    category: "Communication",
    icon: "https://cdn-icons-png.flaticon.com/512/3062/3062634.png",
    status: "coming_soon",
  },
  {
    id: "zoom",
    name: "Zoom",
    description: "Secure and reliable online communication platform",
    category: "Communication",
    icon: "https://upload.wikimedia.org/wikipedia/commons/thumb/7/7b/Zoom_Communications_Logo.svg/512px-Zoom_Communications_Logo.svg.png",
    status: "coming_soon",
  },
  {
    id: "google-meet",
    name: "Google Meet",
    description: "Connect & collaborate with video conferencing",
    category: "Communication",
    icon: "https://upload.wikimedia.org/wikipedia/commons/9/9b/Google_Meet_icon_%282020%29.svg",
    status: "coming_soon",
  },
  {
    id: "zapier",
    name: "Zapier",
    description: "Automate tasks and workflows",
    category: "Developer Tools",
    icon: "https://cdn.zapier.com/zapier/images/favicon.ico",
    status: "coming_soon",
  },
  {
    id: "make",
    name: "Make",
    description: "Build and automate workflows",
    category: "Productivity",
    icon: "https://images.ctfassets.net/qqlj6g4ee76j/7F5t0LvLhYMBzTXneYCTI8/2aa687a8d23d841380f1d4c33631c799/Make-icon.svg",
    status: "coming_soon",
  },
  {
    id: "calendly",
    name: "Calendly",
    description: "Make scheduling faster and more efficient",
    category: "Calendar",
    icon: "https://assets.calendly.com/assets/frontend/media/logo-square-cd364a3c33976d32792a.png",
    status: "coming_soon",
  },
  {
    id: "stripe",
    name: "Stripe",
    description: "Online payment processing and financial solutions",
    category: "Payment",
    icon: "https://upload.wikimedia.org/wikipedia/commons/b/ba/Stripe_Logo%2C_revised_2016.svg",
    status: "coming_soon",
  },
];

const categories = Array.from(new Set(integrations.map(i => i.category)));

export default function IntegrationContent() {
  const [search, setSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const filteredIntegrations = integrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(search.toLowerCase()) ||
      integration.description.toLowerCase().includes(search.toLowerCase());
    const matchesCategory = selectedCategory === "all" || integration.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Integrations</h1>
        <p className="text-gray-600 dark:text-gray-300 mt-1">
          Connect your favorite tools and services
        </p>
      </div>

      {/* Search and Filter */}
      <div className="mb-8 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
          <Input
            placeholder="Search integrations..."
            className="pl-10 bg-card"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        <Select
          value={selectedCategory}
          onValueChange={setSelectedCategory}
        >
          <SelectTrigger className="w-full sm:w-[180px] bg-card">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Integrations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredIntegrations.map((integration) => (
          <IntegrationCard
            key={integration.id}
            {...integration}
          />
        ))}
      </div>
    </>
  );
}