import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GlobalSettingsDocument } from './interfaces/global-settings.interface';
import { UpdateGlobalSettingsDto } from './dto/update-global-settings.dto';
import { OrganizationDocument } from '../organizations/interfaces/organization.interface';

@Injectable()
export class GlobalSettingsService {
  constructor(
    @InjectModel('GlobalSettings')
    private globalSettingsModel: Model<GlobalSettingsDocument>,
    @InjectModel('Organization')
    private organizationModel: Model<OrganizationDocument>,
  ) {}

  /**
   * Get the global settings. If no settings exist, create default settings.
   */
  async getGlobalSettings(): Promise<GlobalSettingsDocument> {
    const settings = await this.globalSettingsModel.findOne().exec();

    if (!settings) {
      // Create default settings if none exist
      const defaultSettings = new this.globalSettingsModel({});
      return defaultSettings.save();
    }

    return settings;
  }

  /**
   * Update global settings
   */
  async updateGlobalSettings(
    updateGlobalSettingsDto: UpdateGlobalSettingsDto,
    userId: string,
  ): Promise<GlobalSettingsDocument> {
    const settings = await this.getGlobalSettings();

    // Set the last updated by field
    settings.lastUpdatedBy = userId;
    settings.updatedAt = new Date();

    const updatedSettings = await settings.save();

    return updatedSettings;
  }
}
