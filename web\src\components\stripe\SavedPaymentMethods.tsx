/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect, useMemo, memo } from "react";
import {
  getPaymentMethods,
  processPayment,
  PaymentMethod
} from "@/app/api/billing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CreditCard, Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SavedPaymentMethodsProps {
  amount: number;
  currency: string;
  description?: string;
  onSuccess: () => void;
  onError: (message: string) => void;
}

const SavedPaymentMethods = ({
  amount,
  currency,
  description,
  onSuccess,
  onError,
}: SavedPaymentMethodsProps) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedMethodId, setSelectedMethodId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  // Memoize the formatted amount to prevent unnecessary re-renders
  const formattedAmount = useMemo(() => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount / 100);
  }, [amount, currency]);

  // Memoize the payment method options to prevent unnecessary re-renders
  const paymentMethodOptions = useMemo(() => {
    return paymentMethods.map((method) => (
      <SelectItem key={method._id} value={method._id}>
        <div className="flex items-center">
          {method.brand === 'visa' && (
            <svg viewBox="0 0 32 21" className="h-4 w-4 mr-2"><path d="M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z" fill="#fff"></path><path d="M12.322 7.583c.165-.437.352-.867.55-1.289h-1.09c-.33.39-.65.803-.957 1.234-.1-.431-.415-.845-.736-1.234h-.922c.616.452 1.013 1.014 1.013 1.716 0 .669-.352 1.289-.968 1.716h1.112c.308-.365.583-.803.781-1.274.187.471.451.909.77 1.274h1.134c-.627-.427-.968-1.047-.968-1.716a2.069 2.069 0 0 1 .28-1.427z" fill="#00579f"></path><path d="M16.9 7.233c-.319 0-.605.075-.825.224-.209.149-.319.373-.319.634 0 .5.407.764 1.013.94l.297.09c.341.104.517.193.517.365 0 .268-.275.432-.726.432-.385 0-.671-.075-.979-.277l-.143.67c.33.164.737.254 1.112.254.374 0 .682-.075.902-.239.242-.164.374-.403.374-.7 0-.537-.44-.81-1.024-.97l-.297-.09c-.275-.074-.506-.164-.506-.343 0-.224.231-.373.594-.373.33 0 .649.09.869.194l.132-.634a2.365 2.365 0 0 0-.99-.179zm2.772 0c-.22 0-.407.03-.55.09l-.11.642c.143-.06.33-.104.55-.104.407-.15.66.224.66.642 0 .582-.308.94-.77.94-.242 0-.44-.06-.627-.164l-.121.656c.187.09.506.149.792.149.891 0 1.463-.642 1.463-1.61 0-.94-.517-1.24-1.288-1.24zm-6.578.09l-.209 1.28c-.22-.06-.44-.09-.66-.09-.56 0-1.024.149-1.024.582 0 .194.121.328.297.403.22.09.275.12.275.209 0 .134-.143.194-.341.194a1.9 1.9 0 0 1-.638-.12l-.11.626c.22.075.55.12.847.12.891 0 1.365-.373 1.365-.94 0-.224-.132-.387-.33-.477-.165-.075-.22-.09-.22-.179 0-.104.11-.164.297-.164.165 0 .374.03.55.09l.11-.634h-.209zm13.64-.09c-.33 0-.638.09-.869.373l.088-.373h-.748l-.572 2.68h.847l.33-1.582c.11-.507.396-.776.737-.776.11 0 .22.015.308.045l.165-.746a1.274 1.274 0 0 0-.286-.03zm-8.415.09l-.671 2.68h.847l.66-2.68h-.836zm-3.96 0l-.847 1.88-.341-1.88h-.902l-.781 2.68h.792l.484-1.85.396 1.85h.55l.99-1.85-.495 1.85h.803l.847-2.68h-1.497z" fill="#00579f"></path></svg>
          )}
          {method.brand === 'mastercard' && (
            <svg viewBox="0 0 32 21" className="h-4 w-4 mr-2"><path d="M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z" fill="#fff"></path><path d="M23.209 7.465c0-1.196-.946-2.142-2.142-2.142h-10.12c-1.196 0-2.142.946-2.142 2.142v6.07c0 1.196.946 2.142 2.142 2.142h10.12c1.196 0 2.142-.946 2.142-2.142v-6.07z" fill="#ff5f00"></path><path d="M12.947 10.5a4.006 4.006 0 0 1 1.535-3.16 4.006 4.006 0 1 0 0 6.32 4.006 4.006 0 0 1-1.535-3.16z" fill="#eb001b"></path><path d="M20.994 10.5a4.006 4.006 0 0 1-6.512 3.16 4.006 4.006 0 0 0 0-6.32a4.006 4.006 0 0 1 6.512 3.16z" fill="#f79e1b"></path></svg>
          )}
          {(!method.brand || (method.brand !== 'visa' && method.brand !== 'mastercard')) && (
            <CreditCard className="h-4 w-4 mr-2" />
          )}
          <span>
            •••• {method.last4} {method.isDefault && (
              <span className="text-xs text-primary ml-1">(Default)</span>
            )}
          </span>
        </div>
      </SelectItem>
    ));
  }, [paymentMethods]);

  // Memoize the selected payment method card to prevent unnecessary re-renders
  const selectedPaymentMethodCard = useMemo(() => {
    if (!selectedMethodId) return null;

    const selectedMethod = paymentMethods.find(method => method._id === selectedMethodId);
    if (!selectedMethod) return null;

    return (
      <Card className="border-primary/20 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-3 rounded-md ${
                selectedMethod.isDefault
                  ? 'bg-primary/10'
                  : 'bg-gray-100 dark:bg-gray-800'
              }`}>
                {selectedMethod.brand === 'visa' && (
                  <svg viewBox="0 0 32 21" className="h-5 w-5"><path d="M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z" fill="#fff"></path><path d="M12.322 7.583c.165-.437.352-.867.55-1.289h-1.09c-.33.39-.65.803-.957 1.234-.1-.431-.415-.845-.736-1.234h-.922c.616.452 1.013 1.014 1.013 1.716 0 .669-.352 1.289-.968 1.716h1.112c.308-.365.583-.803.781-1.274.187.471.451.909.77 1.274h1.134c-.627-.427-.968-1.047-.968-1.716a2.069 2.069 0 0 1 .28-1.427z" fill="#00579f"></path><path d="M16.9 7.233c-.319 0-.605.075-.825.224-.209.149-.319.373-.319.634 0 .5.407.764 1.013.94l.297.09c.341.104.517.193.517.365 0 .268-.275.432-.726.432-.385 0-.671-.075-.979-.277l-.143.67c.33.164.737.254 1.112.254.374 0 .682-.075.902-.239.242-.164.374-.403.374-.7 0-.537-.44-.81-1.024-.97l-.297-.09c-.275-.074-.506-.164-.506-.343 0-.224.231-.373.594-.373.33 0 .649.09.869.194l.132-.634a2.365 2.365 0 0 0-.99-.179zm2.772 0c-.22 0-.407.03-.55.09l-.11.642c.143-.06.33-.104.55-.104.407-.15.66.224.66.642 0 .582-.308.94-.77.94-.242 0-.44-.06-.627-.164l-.121.656c.187.09.506.149.792.149.891 0 1.463-.642 1.463-1.61 0-.94-.517-1.24-1.288-1.24zm-6.578.09l-.209 1.28c-.22-.06-.44-.09-.66-.09-.56 0-1.024.149-1.024.582 0 .194.121.328.297.403.22.09.275.12.275.209 0 .134-.143.194-.341.194a1.9 1.9 0 0 1-.638-.12l-.11.626c.22.075.55.12.847.12.891 0 1.365-.373 1.365-.94 0-.224-.132-.387-.33-.477-.165-.075-.22-.09-.22-.179 0-.104.11-.164.297-.164.165 0 .374.03.55.09l.11-.634h-.209zm13.64-.09c-.33 0-.638.09-.869.373l.088-.373h-.748l-.572 2.68h.847l.33-1.582c.11-.507.396-.776.737-.776.11 0 .22.015.308.045l.165-.746a1.274 1.274 0 0 0-.286-.03zm-8.415.09l-.671 2.68h.847l.66-2.68h-.836zm-3.96 0l-.847 1.88-.341-1.88h-.902l-.781 2.68h.792l.484-1.85.396 1.85h.55l.99-1.85-.495 1.85h.803l.847-2.68h-1.497z" fill="#00579f"></path></svg>
                )}
                {selectedMethod.brand === 'mastercard' && (
                  <svg viewBox="0 0 32 21" className="h-5 w-5"><path d="M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z" fill="#fff"></path><path d="M23.209 7.465c0-1.196-.946-2.142-2.142-2.142h-10.12c-1.196 0-2.142.946-2.142 2.142v6.07c0 1.196.946 2.142 2.142 2.142h10.12c1.196 0 2.142-.946 2.142-2.142v-6.07z" fill="#ff5f00"></path><path d="M12.947 10.5a4.006 4.006 0 0 1 1.535-3.16a4.006 4.006 0 1 0 0 6.32a4.006 4.006 0 0 1-1.535-3.16z" fill="#eb001b"></path><path d="M20.994 10.5a4.006 4.006 0 0 1-6.512 3.16a4.006 4.006 0 0 0 0-6.32a4.006 4.006 0 0 1 6.512 3.16z" fill="#f79e1b"></path></svg>
                )}
                {(!selectedMethod.brand || (selectedMethod.brand !== 'visa' && selectedMethod.brand !== 'mastercard')) && (
                  <CreditCard className={`h-5 w-5 ${
                    selectedMethod.isDefault
                      ? 'text-primary'
                      : ''
                  }`} />
                )}
              </div>
              <div>
                <div className="flex items-center">
                  <p className="font-medium">•••• {selectedMethod.last4}</p>
                  {selectedMethod.isDefault && (
                    <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                      Default
                    </span>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  {selectedMethod.brand && selectedMethod.brand.charAt(0).toUpperCase() + selectedMethod.brand.slice(1)} • Expires {selectedMethod.expMonth}/{selectedMethod.expYear}
                </p>
                {selectedMethod.cardholderName && (
                  <p className="text-sm mt-1">{selectedMethod.cardholderName}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }, [selectedMethodId, paymentMethods]);

  // Fetch payment methods on component mount
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        setIsLoading(true);
        const methods = await getPaymentMethods();
        setPaymentMethods(methods);

        // Set default payment method if available
        const defaultMethod = methods.find(method => method.isDefault);
        if (defaultMethod) {
          setSelectedMethodId(defaultMethod._id);
        } else if (methods.length > 0) {
          setSelectedMethodId(methods[0]._id);
        }
      } catch (err) {
        console.error("Error fetching payment methods:", err);
        onError("Failed to load payment methods");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPaymentMethods();
  }, [onError]);

  const handlePayment = async () => {
    if (!selectedMethodId) {
      onError("Please select a payment method");
      return;
    }

    setIsProcessing(true);

    try {
      // Find the selected payment method
      const selectedMethod = paymentMethods.find(
        method => method._id === selectedMethodId
      );

      if (!selectedMethod) {
        throw new Error("Selected payment method not found");
      }

      // Process the payment
      const result = await processPayment({
        paymentMethodId: selectedMethod.stripePaymentMethodId,
        amount,
        currency,
        description,
      });

      if (result.success) {
        onSuccess();
      } else if (result.status === 'requires_action' || result.status === 'requires_confirmation') {
        // For 3D Secure or other authentication, we would need to handle this
        // This would typically be handled with Stripe.js in the browser
        throw new Error("This payment requires additional authentication. Please use a different payment method.");
      } else {
        throw new Error(`Payment failed: ${result.status}`);
      }
    } catch (err: any) {
      console.error("Payment error:", err);
      onError(err.message || "Payment processing failed");
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (paymentMethods.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-muted-foreground">No saved payment methods</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium flex items-center">
          <CreditCard className="h-4 w-4 mr-2 text-primary" />
          Select Payment Method
        </label>
        <Select
          value={selectedMethodId || ""}
          onValueChange={setSelectedMethodId}
        >
          <SelectTrigger className="bg-white dark:bg-gray-800">
            <SelectValue placeholder="Select a payment method" />
          </SelectTrigger>
          <SelectContent>
            {paymentMethodOptions}
          </SelectContent>
        </Select>
      </div>

      {selectedPaymentMethodCard}

      <Button
        onClick={handlePayment}
        disabled={!selectedMethodId || isProcessing || amount < 1000}
        className="w-full bg-green-600 hover:bg-green-700 text-white"
      >
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          <>
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 9L15 11L21 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Recharge {formattedAmount}
          </>
        )}
      </Button>
    </div>
  );
};

// Use React.memo to prevent unnecessary re-renders
export default memo(SavedPaymentMethods);
