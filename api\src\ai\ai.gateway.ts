// import { SubscribeMessage, WebSocketGateway, WebSocketServer, ConnectedSocket, MessageBody } from '@nestjs/websockets';
// import { Socket, Server } from 'socket.io';
// import { AiService } from './ai.service';
// import { ConversationsService } from 'src/conversations/conversations.service';
// import { AgentService } from 'src/agent/agent.service';

// @WebSocketGateway(5000, { cors: { origin: '*' } })
// export class AiGateway {
//   @WebSocketServer()
//   server: Server;

//   constructor(
//     private readonly aiService: AiService,
//     private readonly conversationsService: ConversationsService,
//     private readonly agentsService: AgentService,
//   ) {}
//   @SubscribeMessage('joinRoom')
//   handleJoinRoom(
//     @ConnectedSocket() client: Socket,
//     @MessageBody() data: { conversationId: string },
//   ) {
//     client.join(data.conversationId);
//     console.log(`Client joined room: ${data.conversationId}`);
//   }
//   /**
//    * Handle incoming audio chunks from the client.
//    * Transcribes audio with <PERSON>hisper, gets LLM response, generates TTS with ElevenLabs, and sends audio back.
//    */
//   @SubscribeMessage('audio_chunk')
//   async handleAudioChunk(
//     @ConnectedSocket() client: Socket,
//     @MessageBody() data: { conversationId: string; audioBase64: string },
//   ) {
//     try {
//       // Fetch conversation and agent
//       const conversation = await this.conversationsService.getConversationById(data.conversationId);
//       if (!conversation) throw new Error('Conversation not found');
//       const agent = await this.agentsService.findById(conversation.agentId.toString());
//       if (!agent) throw new Error('Agent not found');

//       // Set voice settings with Whisper for STT and ElevenLabs for TTS
//       const voiceSettings = {
//         ...agent.voiceSettings,
//         transcriptionProvider: 'whisper',
//         ttsProvider: 'elevenlabs',
//       };

//       // Convert base64 audio to Buffer
//       const audioBuffer = Buffer.from(data.audioBase64, 'base64');

//       // Step 1: Transcribe audio with Whisper
//       const userText = await this.aiService.transcribeAudio('whisper', audioBuffer);
//       if (!userText) throw new Error('Transcription failed');

//       // Step 2: Get LLM response using agent’s configuration
//       const history = conversation.messages.map(m => ({ role: m.role, content: m.content }));
//       const llmResponse = await this.aiService.getChatResponse(conversation.agentId.toString(), userText, history)
//       if (!llmResponse) throw new Error('LLM response generation failed');

//       // Step 3: Generate TTS with ElevenLabs
//       const ttsAudio = await this.aiService.textToSpeech('elevenlabs', llmResponse, voiceSettings);
//       if (!ttsAudio) throw new Error('TTS generation failed');

//       // Step 4: Send audio back to the client
//       this.server.to(data.conversationId).emit('assistant_audio', {
//         audioBase64: ttsAudio.toString('base64'),
//       });

//       // Save messages to conversation
//       conversation.messages.push(
//         { role: 'user', content: userText, timestamp: new Date() },
//         { role: 'assistant', content: llmResponse, timestamp: new Date() },
//       );
//       await conversation.save();

//     } catch (error) {
//       console.error('Error processing audio chunk:', error.message);
//       client.emit('error', { message: 'Failed to process audio: ' + error.message });
//     }
//   }

//   /**
//    * Handle interruption events (e.g., user speaks while assistant is talking).
//    */
//   @SubscribeMessage('interrupt')
//   handleInterrupt(
//     @MessageBody() data: { conversationId: string },
//   ) {
//     console.log(`Interrupt received for conversation ${data.conversationId}`);
//     // Optionally, add logic to cancel ongoing TTS or reset state
//   }
// }