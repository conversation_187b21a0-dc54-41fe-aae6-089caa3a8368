import { Document } from 'mongoose';

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface Conversation {
  agentId: string; // or ObjectId
  userId: string;  // or ObjectId
  type: 'chat' | 'call';
  status: 'active' | 'ended';
  messages: Message[];
  startedAt: Date;
  endedAt?: Date;
}

export type ConversationDocument = Conversation & Document;
