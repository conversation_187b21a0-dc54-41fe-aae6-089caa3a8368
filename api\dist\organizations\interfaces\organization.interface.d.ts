import { Document, Types } from 'mongoose';
export interface Organization {
    _id?: string | Types.ObjectId;
    name: string;
    description?: string;
    status: 'active' | 'inactive' | 'suspended';
    credits: number;
    monthlyFreeCredits: number;
    monthlyFreeCreditsUsed: number;
    lastMonthlyReset: Date;
    usingFreeCredits: boolean;
    monthlyResetDate: number;
    autoRechargeEnabled: boolean;
    autoRechargeThreshold: number;
    autoRechargeAmount: number;
    callPricePerMinute: number;
    minimumCreditsThreshold: number;
    monthlyMinutesAllowance: number;
    stripeCustomerId?: string;
    fullName?: string;
    email?: string;
    lastWarningEmailSent?: Date;
    lastRunoutEmailSent?: Date;
    adminUsers: string[];
    users: string[];
    createdAt: Date;
    updatedAt: Date;
}
export type OrganizationDocument = Organization & Document;
