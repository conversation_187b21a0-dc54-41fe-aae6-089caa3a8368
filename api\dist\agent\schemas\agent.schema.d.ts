import { Schema, Types } from 'mongoose';
export declare const AgentSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    strict: false;
    timestamps: true;
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    updatedAt: NativeDate;
    status: string;
    role: string;
    avatar: string;
    localPhoneNumberId: string;
    internationalPhoneNumberId: string;
    voicemailMessage: string;
    clientMessages: string[];
    serverMessages: string[];
    endCallPhrases: string[];
    createdAt?: NativeDate;
    id?: string;
    model?: {
        model: string;
        messages: Types.DocumentArray<{
            role: string;
            content: string;
            _id?: unknown;
        }, Types.Subdocument<unknown, any, {
            role: string;
            content: string;
            _id?: unknown;
        }> & {
            role: string;
            content: string;
            _id?: unknown;
        }>;
        provider?: string;
        maxTokens?: number;
        temperature?: number;
        knowledgeBaseId?: string;
        emotionRecognitionEnabled?: boolean;
    };
    name?: string;
    server?: {
        url?: string;
    };
    voice?: {
        model: string;
        style: number;
        voiceId: string;
        provider: string;
        stability: number;
        inputPunctuationBoundaries: string[];
        similarityBoost?: number;
        useSpeakerBoost?: boolean;
        inputMinCharacters?: number;
    };
    recordingEnabled?: boolean;
    firstMessage?: string;
    endCallFunctionEnabled?: boolean;
    endCallMessage?: string;
    transcriber?: {
        model: string;
        provider: string;
        language: string;
        numerals: boolean;
        confidenceThreshold: number;
    };
    hipaaEnabled?: boolean;
    maxDurationSeconds?: number;
    backgroundSound?: string;
    backchannelingEnabled?: boolean;
    analysisPlan?: {
        summaryPrompt?: string;
        structuredDataPrompt?: string;
        structuredDataSchema?: any;
        structuredDataRequestTimeoutSeconds?: number;
        successEvaluationPrompt?: string;
        successEvaluationRubric?: string;
    };
    voicemailDetection?: {
        provider?: string;
    };
    backgroundDenoisingEnabled?: boolean;
    messagePlan?: {
        idleMessages: string[];
        idleMessageMaxSpokenCount?: number;
        idleTimeoutSeconds?: number;
    };
    startSpeakingPlan?: {
        waitSeconds?: number;
    };
    stopSpeakingPlan?: {
        numWords?: number;
    };
    compliancePlan?: {
        hipaaEnabled?: boolean;
        pciEnabled?: boolean;
    };
    isServerUrlSecretSet?: boolean;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    updatedAt: NativeDate;
    status: string;
    role: string;
    avatar: string;
    localPhoneNumberId: string;
    internationalPhoneNumberId: string;
    voicemailMessage: string;
    clientMessages: string[];
    serverMessages: string[];
    endCallPhrases: string[];
    createdAt?: NativeDate;
    id?: string;
    model?: {
        model: string;
        messages: Types.DocumentArray<{
            role: string;
            content: string;
            _id?: unknown;
        }, Types.Subdocument<unknown, any, {
            role: string;
            content: string;
            _id?: unknown;
        }> & {
            role: string;
            content: string;
            _id?: unknown;
        }>;
        provider?: string;
        maxTokens?: number;
        temperature?: number;
        knowledgeBaseId?: string;
        emotionRecognitionEnabled?: boolean;
    };
    name?: string;
    server?: {
        url?: string;
    };
    voice?: {
        model: string;
        style: number;
        voiceId: string;
        provider: string;
        stability: number;
        inputPunctuationBoundaries: string[];
        similarityBoost?: number;
        useSpeakerBoost?: boolean;
        inputMinCharacters?: number;
    };
    recordingEnabled?: boolean;
    firstMessage?: string;
    endCallFunctionEnabled?: boolean;
    endCallMessage?: string;
    transcriber?: {
        model: string;
        provider: string;
        language: string;
        numerals: boolean;
        confidenceThreshold: number;
    };
    hipaaEnabled?: boolean;
    maxDurationSeconds?: number;
    backgroundSound?: string;
    backchannelingEnabled?: boolean;
    analysisPlan?: {
        summaryPrompt?: string;
        structuredDataPrompt?: string;
        structuredDataSchema?: any;
        structuredDataRequestTimeoutSeconds?: number;
        successEvaluationPrompt?: string;
        successEvaluationRubric?: string;
    };
    voicemailDetection?: {
        provider?: string;
    };
    backgroundDenoisingEnabled?: boolean;
    messagePlan?: {
        idleMessages: string[];
        idleMessageMaxSpokenCount?: number;
        idleTimeoutSeconds?: number;
    };
    startSpeakingPlan?: {
        waitSeconds?: number;
    };
    stopSpeakingPlan?: {
        numWords?: number;
    };
    compliancePlan?: {
        hipaaEnabled?: boolean;
        pciEnabled?: boolean;
    };
    isServerUrlSecretSet?: boolean;
}>> & import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    updatedAt: NativeDate;
    status: string;
    role: string;
    avatar: string;
    localPhoneNumberId: string;
    internationalPhoneNumberId: string;
    voicemailMessage: string;
    clientMessages: string[];
    serverMessages: string[];
    endCallPhrases: string[];
    createdAt?: NativeDate;
    id?: string;
    model?: {
        model: string;
        messages: Types.DocumentArray<{
            role: string;
            content: string;
            _id?: unknown;
        }, Types.Subdocument<unknown, any, {
            role: string;
            content: string;
            _id?: unknown;
        }> & {
            role: string;
            content: string;
            _id?: unknown;
        }>;
        provider?: string;
        maxTokens?: number;
        temperature?: number;
        knowledgeBaseId?: string;
        emotionRecognitionEnabled?: boolean;
    };
    name?: string;
    server?: {
        url?: string;
    };
    voice?: {
        model: string;
        style: number;
        voiceId: string;
        provider: string;
        stability: number;
        inputPunctuationBoundaries: string[];
        similarityBoost?: number;
        useSpeakerBoost?: boolean;
        inputMinCharacters?: number;
    };
    recordingEnabled?: boolean;
    firstMessage?: string;
    endCallFunctionEnabled?: boolean;
    endCallMessage?: string;
    transcriber?: {
        model: string;
        provider: string;
        language: string;
        numerals: boolean;
        confidenceThreshold: number;
    };
    hipaaEnabled?: boolean;
    maxDurationSeconds?: number;
    backgroundSound?: string;
    backchannelingEnabled?: boolean;
    analysisPlan?: {
        summaryPrompt?: string;
        structuredDataPrompt?: string;
        structuredDataSchema?: any;
        structuredDataRequestTimeoutSeconds?: number;
        successEvaluationPrompt?: string;
        successEvaluationRubric?: string;
    };
    voicemailDetection?: {
        provider?: string;
    };
    backgroundDenoisingEnabled?: boolean;
    messagePlan?: {
        idleMessages: string[];
        idleMessageMaxSpokenCount?: number;
        idleTimeoutSeconds?: number;
    };
    startSpeakingPlan?: {
        waitSeconds?: number;
    };
    stopSpeakingPlan?: {
        numWords?: number;
    };
    compliancePlan?: {
        hipaaEnabled?: boolean;
        pciEnabled?: boolean;
    };
    isServerUrlSecretSet?: boolean;
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
