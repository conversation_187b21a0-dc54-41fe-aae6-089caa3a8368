import { Injectable, NotFoundException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AgentService } from '../agent/agent.service';
import FormData from 'form-data';

@Injectable()
export class AiService {
  constructor(
    private readonly httpService: HttpService,
    private readonly agentsService: AgentService,
  ) {}

  /**
   * Streaming generator method to stream tokens from the LLM.
   */
  async *streamChatWithAgentGenerator(
    agentId: string,
    userMessage: string,
    conversationHistory: Array<{ role: string; content: string }> = [],
  ): AsyncGenerator<string> {
    const agent = await this.agentsService.findById(agentId);
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    
    // Limit history to only the last 3 messages.
    const limitedHistory = conversationHistory.slice(-3);
    
    // Incorporate language information into the system message if available.
    const language = agent.voiceSettings?.language || 'English';
    const systemMessage =
      agent.prompt ||
      `You are ${agent.role}. Please respond in ${language}.`;

    const messages = [
      { role: 'system', content: systemMessage },
      ...limitedHistory,
      { role: 'user', content: userMessage },
    ];

    // Determine if streaming is supported.
    const streamingSupportedModels = ['gpt-3.5-turbo', 'gpt-4', 'gpt-4o-mini'];
    const supportsStreaming = streamingSupportedModels.includes(
      agent.languageModel.model,
    );

    const payload = {
      model: agent.languageModel.model,
      messages,
      temperature: agent.languageModel.temperature,
      stream: supportsStreaming,
    };

    const response$ = this.httpService.post(
      'https://api.openai.com/v1/chat/completions',
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        },
        responseType: supportsStreaming ? 'stream' : 'json',
      },
    );

    if (supportsStreaming) {
      const response = await firstValueFrom(response$);
      const stream = response.data; // Node Readable stream.
      let buffer = '';
      for await (const chunk of stream) {
        buffer += chunk.toString('utf8');
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6).trim();
            if (dataStr === '[DONE]') {
              return;
            }
            try {
              const parsed = JSON.parse(dataStr);
              const token = parsed.choices[0].delta?.content;
              if (token) {
                yield token;
              }
            } catch (e) {
              console.error('Stream parsing error:', e);
            }
          }
        }
      }
    } else {
      const response = await firstValueFrom(response$);
      yield response.data.choices[0].message.content;
    }
  }

  async getChatResponse(
    agentId: string,
    userMessage: string,
    conversationHistory: Array<{ role: string; content: string }> = [],
  ): Promise<string> {
    let fullResponse = '';
    for await (const token of this.streamChatWithAgentGenerator(
      agentId,
      userMessage,
      conversationHistory,
    )) {
      fullResponse += token;
    }
    return fullResponse;
  }

  /**
   * Transcribes audio to text using the specified STT provider (Whisper).
   * Accepts an optional options parameter to specify language.
   */
  async transcribeAudio(
    provider: string,
    audioBuffer: Buffer,
    options?: { language?: string },
  ): Promise<string> {
    if (provider === 'whisper') {
      const form = new FormData();
      form.append('file', audioBuffer, {
        filename: 'audio.webm', // Match the client’s audio format
        contentType: 'audio/webm',
      });
      form.append('model', 'whisper-1');

      // If a language is provided, add it to the form data.
      if (options?.language) {
        form.append('language', options.language);
      }

      const response = await firstValueFrom(
        this.httpService.post('https://api.openai.com/v1/audio/transcriptions', form, {
          headers: {
            ...form.getHeaders(),
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
          },
        }),
      );
      return response.data.text;
    }
    throw new Error('Unsupported STT provider');
  }

  /**
   * Converts text to speech using the specified TTS provider (ElevenLabs).
   * It incorporates additional voice settings (speechSpeed, responsiveness, emotions, interruptionSensitivity, backgroundNoise) 
   * and uses default values if they’re not provided.
   */
  async textToSpeech(
    provider: string,
    text: string,
    voiceSettings: any = {},
  ): Promise<Buffer> {
    if (provider !== 'elevenlabs') {
      throw new Error(`Unsupported TTS provider: ${provider}`);
    }

    const voiceId =
      voiceSettings.voiceId || process.env.ELEVENLABS_DEFAULT_VOICE_ID;
    if (!voiceId) {
      throw new Error('A valid voice id must be provided for ElevenLabs TTS.');
    }
    const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`;

    const payload = {
      text,
      model_id: voiceSettings.ttsModel || 'eleven_monolingual_v1',
      voice_settings: {
        stability: voiceSettings.stability ?? 0.5,
        similarity_boost: voiceSettings.voiceSimilarity ?? 0.75,
        // Additional parameters with defaults:
        speech_speed: voiceSettings.speechSpeed ?? 1,
        responsiveness: voiceSettings.responsiveness ?? 0.8,
        emotions: voiceSettings.emotions || 'balanced',
        interruption_sensitivity: voiceSettings.interruptionSensitivity ?? 0.5,
        background_noise: voiceSettings.backgroundNoise || 'office',
      },
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post(url, payload, {
          headers: {
            'Content-Type': 'application/json',
            'xi-api-key': process.env.ELEVENLABS_API_KEY,
            Accept: 'audio/mpeg',
          },
          responseType: 'arraybuffer',
          timeout: 10000, // 10 seconds timeout
        }),
      );
      return Buffer.from(response.data);
    } catch (error) {
      console.error('Error generating speech with ElevenLabs:', error);
      throw new Error('Failed to generate speech');
    }
  }
}
