"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogSchema = void 0;
const mongoose_1 = require("mongoose");
exports.LogSchema = new mongoose_1.Schema({
    level: { type: String, enum: ['INFO', 'WARN', 'ERROR'], required: true },
    message: { type: String, required: true },
    timestamp: { type: Date, default: Date.now },
    trace: { type: String, default: null },
});
//# sourceMappingURL=log.schema.js.map