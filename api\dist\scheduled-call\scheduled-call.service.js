"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduledCallService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const scheduled_call_schema_1 = require("./schemas/scheduled-call.schema");
let ScheduledCallService = class ScheduledCallService {
    constructor(scheduledCallModel) {
        this.scheduledCallModel = scheduledCallModel;
    }
    async createScheduledCall(payload) {
        let { contacts, scheduledTime, region, scheduledByName } = payload;
        let agentId = payload.agentId;
        try {
            if (!agentId) {
                throw new Error('agentId is required');
            }
            if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
                throw new Error('contacts array is required and must not be empty');
            }
            if (!scheduledTime) {
                throw new Error('scheduledTime is required');
            }
            if (!region) {
                throw new Error('region is required');
            }
            const utcTime = moment_timezone_1.default.tz(scheduledTime, region).utc().toDate();
            const scheduledByTimestamp = new Date();
            const newScheduledCall = new this.scheduledCallModel({
                agentId,
                contacts,
                scheduledTime: utcTime,
                region,
                scheduledByName,
                scheduledByTimestamp,
                status: "pending",
            });
            const savedCall = await newScheduledCall.save();
            console.log(`Successfully created scheduled call with ID: ${savedCall._id}`);
            return savedCall;
        }
        catch (error) {
            console.error('Error creating scheduled call:', error);
            if (error.name === 'ValidationError') {
                const validationErrors = Object.values(error.errors).map((err) => err.message);
                throw new common_1.HttpException(`Validation error: ${validationErrors.join(', ')}`, common_1.HttpStatus.BAD_REQUEST);
            }
            throw new common_1.HttpException(`Error creating scheduled call: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getScheduledCalls(page, limit, search, dateFilter) {
        try {
            const queryFilter = {};
            if (search && search.trim().length >= 1) {
                const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const searchRegex = new RegExp(escapedSearch, 'i');
                queryFilter['contacts'] = {
                    $elemMatch: {
                        Name: { $regex: searchRegex }
                    }
                };
            }
            if (dateFilter) {
                const now = new Date();
                const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);
                const monday = new Date(today);
                monday.setDate(monday.getDate() - monday.getDay() + 1);
                const sunday = new Date(monday);
                sunday.setDate(monday.getDate() + 6);
                const nextMonday = new Date(sunday);
                nextMonday.setDate(sunday.getDate() + 1);
                const nextSunday = new Date(nextMonday);
                nextSunday.setDate(nextMonday.getDate() + 6);
                switch (dateFilter) {
                    case 'today':
                        queryFilter.scheduledTime = {
                            $gte: today,
                            $lt: tomorrow
                        };
                        break;
                    case 'thisWeek':
                        queryFilter.scheduledTime = {
                            $gte: monday,
                            $lte: sunday
                        };
                        break;
                    case 'weekend':
                        queryFilter.$expr = {
                            $in: [
                                { $dayOfWeek: '$scheduledTime' },
                                [1, 7]
                            ]
                        };
                        break;
                    case 'nextWeek':
                        queryFilter.scheduledTime = {
                            $gte: nextMonday,
                            $lt: nextSunday
                        };
                        break;
                    case 'upcoming':
                        queryFilter.scheduledTime = {
                            $gte: now
                        };
                        break;
                    case 'past':
                        queryFilter.scheduledTime = {
                            $lt: now
                        };
                        break;
                }
            }
            const totalCount = await this.scheduledCallModel.countDocuments().exec();
            let query = this.scheduledCallModel.find(queryFilter).sort({ scheduledTime: -1 });
            if (page !== undefined && limit !== undefined) {
                const skip = (page - 1) * limit;
                query = query.skip(skip).limit(limit);
            }
            const scheduledCalls = await query.exec();
            scheduledCalls.forEach(call => {
                call['_totalCount'] = totalCount;
            });
            return scheduledCalls;
        }
        catch (error) {
            throw new common_1.HttpException("Error fetching scheduled calls", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getLatestScheduledCallForCampaign(agentId) {
        try {
            return this.scheduledCallModel
                .find({
                agentId: agentId,
                status: 'pending',
            })
                .sort({ scheduledTime: -1 })
                .limit(1)
                .exec();
        }
        catch (error) {
            throw new common_1.HttpException("Error fetching latest scheduled call", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPendingCallsForCampaign(agentId) {
        try {
            return this.scheduledCallModel
                .find({
                agentId: agentId,
                status: 'pending',
            })
                .sort({ scheduledTime: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.HttpException("Error fetching pending calls for campaign", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removeDuplicateCalls(agentId) {
        try {
            const pendingCalls = await this.getPendingCallsForCampaign(agentId);
            if (!pendingCalls || pendingCalls.length === 0) {
                return {
                    duplicatesRemoved: 0
                };
            }
            const contactMap = new Map();
            let duplicatesRemoved = 0;
            for (const call of pendingCalls) {
                for (const contact of call.contacts) {
                    const key = `${contact.Name}-${contact.MobileNumber}`;
                    if (!contactMap.has(key)) {
                        contactMap.set(key, []);
                    }
                    contactMap.get(key).push(call);
                }
            }
            for (const [, calls] of contactMap.entries()) {
                if (calls.length > 1) {
                    calls.sort((a, b) => new Date(b.scheduledTime).getTime() - new Date(a.scheduledTime).getTime());
                    for (let i = 1; i < calls.length; i++) {
                        await this.scheduledCallModel.findByIdAndDelete(calls[i]._id);
                        duplicatesRemoved++;
                    }
                }
            }
            return {
                duplicatesRemoved
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error removing duplicate calls", error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async rescheduleCampaignCalls(agentId, concurrentCalls, batchIntervalMinutes, callWindow) {
        try {
            const pendingCalls = await this.getPendingCallsForCampaign(agentId);
            if (!pendingCalls || pendingCalls.length === 0) {
                return {
                    rescheduledCount: 0,
                    duplicatesRemoved: 0,
                    totalCount: 0
                };
            }
            const contactMap = new Map();
            let duplicatesRemoved = 0;
            for (const call of pendingCalls) {
                for (const contact of call.contacts) {
                    const key = `${contact.Name}-${contact.MobileNumber}`;
                    if (!contactMap.has(key)) {
                        contactMap.set(key, []);
                    }
                    contactMap.get(key).push(call);
                }
            }
            for (const [, calls] of contactMap.entries()) {
                if (calls.length > 1) {
                    calls.sort((a, b) => new Date(b.scheduledTime).getTime() - new Date(a.scheduledTime).getTime());
                    for (let i = 1; i < calls.length; i++) {
                        await this.scheduledCallModel.findByIdAndDelete(calls[i]._id);
                        duplicatesRemoved++;
                    }
                }
            }
            const updatedPendingCalls = await this.getPendingCallsForCampaign(agentId);
            if (!updatedPendingCalls || updatedPendingCalls.length === 0) {
                return {
                    rescheduledCount: 0,
                    duplicatesRemoved,
                    totalCount: duplicatesRemoved
                };
            }
            const [startHour, startMinute] = callWindow.startTime.split(':').map(Number);
            const [endHour, endMinute] = callWindow.endTime.split(':').map(Number);
            const contactGroups = new Map();
            for (const call of pendingCalls) {
                for (const contact of call.contacts) {
                    const key = `${contact.Name}-${contact.MobileNumber}`;
                    if (!contactGroups.has(key)) {
                        contactGroups.set(key, []);
                    }
                    contactGroups.get(key).push(call);
                }
            }
            const contacts = Array.from(contactGroups.keys());
            const batches = [];
            for (let i = 0; i < contacts.length; i += concurrentCalls) {
                batches.push(contacts.slice(i, i + concurrentCalls));
            }
            const now = new Date();
            let currentDate = new Date(now.getTime() + 5 * 60 * 1000);
            const normalizedDaysOfWeek = callWindow.daysOfWeek.map(day => day.toLowerCase());
            let currentDayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
            if (!normalizedDaysOfWeek.includes(currentDayName)) {
                let daysToAdd = 1;
                while (daysToAdd <= 7) {
                    const nextDate = new Date(currentDate);
                    nextDate.setDate(nextDate.getDate() + daysToAdd);
                    const nextDayName = nextDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
                    if (normalizedDaysOfWeek.includes(nextDayName)) {
                        currentDate = nextDate;
                        currentDayName = nextDayName;
                        break;
                    }
                    daysToAdd++;
                }
            }
            const currentHour = currentDate.getHours();
            const currentMinute = currentDate.getMinutes();
            const isBeforeWindow = currentHour < startHour || (currentHour === startHour && currentMinute < startMinute);
            const isAfterWindow = currentHour > endHour || (currentHour === endHour && currentMinute > endMinute);
            if (isBeforeWindow) {
                currentDate.setHours(startHour, startMinute, 0, 0);
            }
            else if (isAfterWindow) {
                let daysToAdd = 1;
                let nextAllowedDay = false;
                while (daysToAdd <= 7 && !nextAllowedDay) {
                    const nextDate = new Date(currentDate);
                    nextDate.setDate(nextDate.getDate() + daysToAdd);
                    const nextDayName = nextDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
                    if (normalizedDaysOfWeek.includes(nextDayName)) {
                        nextAllowedDay = true;
                        currentDate = new Date(nextDate);
                        currentDate.setHours(startHour, startMinute, 0, 0);
                    }
                    daysToAdd++;
                }
                if (!nextAllowedDay && normalizedDaysOfWeek.length > 0) {
                    const today = new Date();
                    const dayOfWeek = today.getDay();
                    const dayNameToNumber = {
                        'sunday': 0,
                        'monday': 1,
                        'tuesday': 2,
                        'wednesday': 3,
                        'thursday': 4,
                        'friday': 5,
                        'saturday': 6
                    };
                    const firstAllowedDayName = normalizedDaysOfWeek[0];
                    const firstAllowedDayNumber = dayNameToNumber[firstAllowedDayName];
                    let daysToFirstAllowed = firstAllowedDayNumber - dayOfWeek;
                    if (daysToFirstAllowed <= 0) {
                        daysToFirstAllowed += 7;
                    }
                    const nextDate = new Date(today);
                    nextDate.setDate(nextDate.getDate() + daysToFirstAllowed);
                    currentDate = new Date(nextDate);
                    currentDate.setHours(startHour, startMinute, 0, 0);
                }
            }
            let rescheduledCount = 0;
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                const scheduledTime = new Date(currentDate);
                for (const contactKey of batch) {
                    const calls = contactGroups.get(contactKey);
                    if (calls && calls.length > 0) {
                        const call = calls[0];
                        const contactRegion = call.region || 'UTC';
                        const contactLocalTime = moment_timezone_1.default.utc(scheduledTime).tz(contactRegion);
                        const contactHour = contactLocalTime.hours();
                        const contactMinute = contactLocalTime.minutes();
                        const isContactBeforeWindow = contactHour < startHour ||
                            (contactHour === startHour && contactMinute < startMinute);
                        const isContactAfterWindow = contactHour > endHour ||
                            (contactHour === endHour && contactMinute > endMinute);
                        const contactDayName = contactLocalTime.format('dddd').toLowerCase();
                        const isAllowedDay = normalizedDaysOfWeek.includes(contactDayName);
                        let finalScheduledTime;
                        if (!isAllowedDay || isContactBeforeWindow || isContactAfterWindow) {
                            let nextAllowedDate = moment_timezone_1.default.utc(scheduledTime).tz(contactRegion);
                            if (!isAllowedDay || isContactAfterWindow) {
                                let daysChecked = 0;
                                let foundAllowedDay = false;
                                while (daysChecked < 7 && !foundAllowedDay) {
                                    nextAllowedDate.add(1, 'days').startOf('day');
                                    const nextDayName = nextAllowedDate.format('dddd').toLowerCase();
                                    if (normalizedDaysOfWeek.includes(nextDayName)) {
                                        foundAllowedDay = true;
                                        nextAllowedDate.hours(startHour).minutes(startMinute).seconds(0).milliseconds(0);
                                    }
                                    daysChecked++;
                                }
                                if (!foundAllowedDay && normalizedDaysOfWeek.length > 0) {
                                    const firstAllowedDay = normalizedDaysOfWeek[0];
                                    const dayMapping = {
                                        'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
                                        'thursday': 4, 'friday': 5, 'saturday': 6
                                    };
                                    const today = nextAllowedDate.day();
                                    const targetDay = dayMapping[firstAllowedDay];
                                    let daysToAdd = targetDay - today;
                                    if (daysToAdd <= 0)
                                        daysToAdd += 7;
                                    nextAllowedDate.add(daysToAdd, 'days')
                                        .hours(startHour).minutes(startMinute).seconds(0).milliseconds(0);
                                }
                            }
                            else if (isContactBeforeWindow) {
                                nextAllowedDate.hours(startHour).minutes(startMinute).seconds(0).milliseconds(0);
                            }
                            finalScheduledTime = nextAllowedDate.toDate();
                        }
                        else {
                            finalScheduledTime = scheduledTime;
                        }
                        for (const callToUpdate of calls) {
                            await this.scheduledCallModel.updateOne({ _id: callToUpdate._id }, { scheduledTime: finalScheduledTime });
                            rescheduledCount++;
                        }
                    }
                }
                if (batchIndex < batches.length - 1) {
                    const intervalMinutes = batchIntervalMinutes || 3;
                    currentDate = new Date(currentDate.setMinutes(currentDate.getMinutes() + intervalMinutes));
                    if (currentDate.getHours() > endHour ||
                        (currentDate.getHours() === endHour && currentDate.getMinutes() > endMinute)) {
                        let daysToAdd = 1;
                        let nextAllowedDay = false;
                        while (daysToAdd <= 7 && !nextAllowedDay) {
                            const nextDate = new Date(currentDate);
                            nextDate.setDate(nextDate.getDate() + daysToAdd);
                            const nextDayName = nextDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
                            if (normalizedDaysOfWeek.includes(nextDayName)) {
                                nextAllowedDay = true;
                                currentDate = new Date(nextDate);
                                currentDate.setHours(startHour, startMinute, 0, 0);
                            }
                            daysToAdd++;
                        }
                        if (!nextAllowedDay && normalizedDaysOfWeek.length > 0) {
                            const today = new Date();
                            const dayOfWeek = today.getDay();
                            const dayNameToNumber = {
                                'sunday': 0,
                                'monday': 1,
                                'tuesday': 2,
                                'wednesday': 3,
                                'thursday': 4,
                                'friday': 5,
                                'saturday': 6
                            };
                            const firstAllowedDayName = normalizedDaysOfWeek[0];
                            const firstAllowedDayNumber = dayNameToNumber[firstAllowedDayName];
                            let daysToFirstAllowed = firstAllowedDayNumber - dayOfWeek;
                            if (daysToFirstAllowed <= 0) {
                                daysToFirstAllowed += 7;
                            }
                            const nextDate = new Date(today);
                            nextDate.setDate(nextDate.getDate() + daysToFirstAllowed);
                            currentDate = new Date(nextDate);
                            currentDate.setHours(startHour, startMinute, 0, 0);
                        }
                    }
                }
            }
            return {
                rescheduledCount,
                duplicatesRemoved,
                totalCount: rescheduledCount + duplicatesRemoved
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error rescheduling campaign calls", error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateScheduledCall(id, updateData) {
        try {
            if (updateData.scheduledTime && updateData.region) {
                updateData.scheduledTime = moment_timezone_1.default
                    .tz(updateData.scheduledTime, updateData.region)
                    .utc()
                    .toDate();
            }
            const updatedCall = await this.scheduledCallModel
                .findByIdAndUpdate(id, updateData, { new: true })
                .exec();
            if (!updatedCall) {
                throw new common_1.HttpException("Scheduled call not found", common_1.HttpStatus.NOT_FOUND);
            }
            return updatedCall;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error updating scheduled call", error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getScheduledCallsByStatusAndTime(status, now) {
        try {
            return this.scheduledCallModel
                .find({
                status,
                scheduledTime: { $lte: now },
            })
                .sort({ scheduledTime: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.HttpException("Error fetching scheduled calls by time", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getStuckProcessingCalls(timeThreshold = 10) {
        try {
            const thresholdTime = new Date();
            thresholdTime.setMinutes(thresholdTime.getMinutes() - timeThreshold);
            return this.scheduledCallModel
                .find({
                status: 'processing',
                lastProcessedAt: { $lte: thresholdTime }
            })
                .exec();
        }
        catch (error) {
            throw new common_1.HttpException("Error fetching stuck processing calls", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getScheduledCallById(id) {
        try {
            const scheduledCall = await this.scheduledCallModel.findById(id).exec();
            if (!scheduledCall) {
                throw new common_1.HttpException("Scheduled call not found", common_1.HttpStatus.NOT_FOUND);
            }
            return scheduledCall;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error fetching scheduled call", error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteScheduledCall(id) {
        try {
            const result = await this.scheduledCallModel.findByIdAndDelete(id).exec();
            if (!result) {
                throw new common_1.HttpException("Scheduled call not found", common_1.HttpStatus.NOT_FOUND);
            }
            return true;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error deleting scheduled call", error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateAgentForPendingCalls(contactNames, contactPhones, newAgentId) {
        try {
            if (!contactNames || !contactPhones || contactNames.length === 0 || contactPhones.length === 0) {
                return 0;
            }
            if (contactNames.length !== contactPhones.length) {
                throw new common_1.HttpException("Contact names and phone numbers arrays must have the same length", common_1.HttpStatus.BAD_REQUEST);
            }
            const contactConditions = [];
            for (let i = 0; i < contactNames.length; i++) {
                if (contactNames[i] && contactPhones[i]) {
                    contactConditions.push({
                        'contacts.Name': contactNames[i],
                        'contacts.MobileNumber': contactPhones[i]
                    });
                }
            }
            if (contactConditions.length === 0) {
                return 0;
            }
            const result = await this.scheduledCallModel.updateMany({
                status: 'pending',
                $or: contactConditions
            }, { agentId: newAgentId });
            return result.modifiedCount;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error updating agent for scheduled calls", error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async cancelPendingCallsForContacts(contactNames, contactPhones) {
        try {
            if (!contactNames || !contactPhones || contactNames.length === 0 || contactPhones.length === 0) {
                return 0;
            }
            if (contactNames.length !== contactPhones.length) {
                throw new common_1.HttpException("Contact names and phone numbers arrays must have the same length", common_1.HttpStatus.BAD_REQUEST);
            }
            const contactConditions = [];
            for (let i = 0; i < contactNames.length; i++) {
                if (contactNames[i] && contactPhones[i]) {
                    contactConditions.push({
                        'contacts.Name': contactNames[i],
                        'contacts.MobileNumber': contactPhones[i]
                    });
                }
            }
            if (contactConditions.length === 0) {
                return 0;
            }
            const result = await this.scheduledCallModel.updateMany({
                status: 'pending',
                $or: contactConditions
            }, { status: 'cancelled' });
            return result.modifiedCount;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error cancelling scheduled calls", error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ScheduledCallService = ScheduledCallService;
exports.ScheduledCallService = ScheduledCallService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(scheduled_call_schema_1.ScheduledCall.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ScheduledCallService);
//# sourceMappingURL=scheduled-call.service.js.map