/* eslint-disable @typescript-eslint/no-unused-vars */

"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Play, Save, Search } from "lucide-react";
import { useState } from "react";

// Voice type definition
type Voice = {
  id: string;
  name: string;
  gender: string;
  language: string;
  platform: string;
  credits: string | null;
};

// Sample voice data
const voiceData: Voice[] = [
  {
    id: "1",
    name: "<PERSON>",
    gender: "Male",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "2",
    name: "<PERSON>",
    gender: "Male",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "3",
    name: "<PERSON><PERSON><PERSON> - Saleswoman",
    gender: "Female",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "4",
    name: "Blondie - Conversational",
    gender: "Female",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "5",
    name: "<PERSON> The GREAT",
    gender: "Male",
    language: "pl Polish",
    platform: "Eleven Labs",
    credits: "4x credits"
  },
  {
    id: "6",
    name: "Mike - Aussie Entrepreneur",
    gender: "Male",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "7",
    name: "Monika Sogam - Friendly Customer Care Voice",
    gender: "Female",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "8",
    name: "Remi",
    gender: "Male",
    language: "de German",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "9",
    name: "Talkative Joe: Lively British RP Male Voice for Engaging Conversations",
    gender: "Male",
    language: "us English",
    platform: "Eleven Labs",
    credits: "4x credits"
  },
  {
    id: "10",
    name: "Jin",
    gender: "Male",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "11",
    name: "Ana Maria",
    gender: "Female",
    language: "ro Romanian",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "12",
    name: "Carter D",
    gender: "Male",
    language: "us English",
    platform: "Eleven Labs",
    credits: null
  },
  {
    id: "13",
    name: "Diego Alez - Professional, narrative and conversational",
    gender: "Male",
    language: "es Spanish",
    platform: "Eleven Labs",
    credits: "3x credits"
  }
];

export default function VoicesContent() {
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [voices, setVoices] = useState<Voice[]>(voiceData);
  const [previewingVoice, setPreviewingVoice] = useState<string | null>(null);

  // Filter voices based on search query
  const filteredVoices = voices.filter(voice => 
    !searchQuery || 
    voice.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    voice.language.toLowerCase().includes(searchQuery.toLowerCase()) ||
    voice.platform.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handlePreviewVoice = (id: string) => {
    setPreviewingVoice(id);
    // Here you would typically play a sample of the voice
    // Mock implementation for demo purposes
    setTimeout(() => {
      setPreviewingVoice(null);
    }, 2000);
  };

  return (
    <>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Voices</h1>
        <div className="flex gap-2">
          {searchOpen ? (
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search voices..."
                className="h-10 px-3 pr-10 rounded-md border border-input bg-background text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                autoFocus
              />
              <button 
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => {
                  setSearchOpen(false);
                  setSearchQuery("");
                }}
              >
                <Search className="h-4 w-4 text-muted-foreground" />
              </button>
            </div>
          ) : (
            <Button variant="outline" className="flex items-center gap-2" onClick={() => setSearchOpen(true)}>
              <Search className="h-4 w-4" />
            </Button>
          )}
          
        </div>
      </div>

      {/* Voices Table */}
      <div className="bg-card rounded-lg border shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead className="hidden md:table-cell">Gender</TableHead>
              <TableHead>Language</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredVoices.map((voice) => (
              <TableRow key={voice.id}>
                <TableCell className="font-medium">{voice.name}</TableCell>
                <TableCell className="hidden md:table-cell">{voice.gender}</TableCell>
                <TableCell>{voice.language}</TableCell>
               
                <TableCell>
                  <div className="flex flex-col sm:flex-row gap-1">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="h-8 px-2"
                      onClick={() => handlePreviewVoice(voice.id)}
                      disabled={previewingVoice === voice.id}
                    >
                      {previewingVoice === voice.id ? (
                        <div className="h-3 w-3 rounded-full bg-primary animate-pulse mr-1"></div>
                      ) : (
                        <Play className="h-3 w-3 mr-1" />
                      )}
                      Preview
                    </Button>
                    <Button size="sm" variant="outline" className="h-8 px-2">
                      <Save className="h-3 w-3 mr-1" /> Save
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {filteredVoices.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="h-60 text-center">
                  <p className="text-lg font-medium text-muted-foreground">No matching voices found</p>
                  <p className="text-sm text-muted-foreground">Try adjusting your search terms</p>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </>
  );
}