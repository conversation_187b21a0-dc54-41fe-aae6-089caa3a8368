import {Controller,Post,Get,Patch,Delete,Param,Body,UseGuards,UseInterceptors,UploadedFile,BadRequestException} from '@nestjs/common';
import { AgentService } from './agent.service';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import path, { join } from 'path';
import * as fs from 'fs';

@ApiTags('Agents')
@Controller('agents')
export class AgentController {
  constructor(private readonly agentsService: AgentService) {}
  
  @Get('avatars')
  @ApiOperation({ summary: 'List all files in the uploads directory' })
  @ApiResponse({ status: 200, description: 'List of uploaded files' })
  async getUploadedFiles() {
    return this.agentsService.getUploadedFiles();
  }

  @Get('volume-avatars')
  @ApiOperation({ summary: 'List all files in the Docker volume' })
  @ApiResponse({ status: 200, description: 'List of files in volume' })
  async getVolumeFiles() {
    return this.agentsService.getVolumeFiles();
  }


  @Post()
  @ApiOperation({ summary: 'Create a new AI agent' })
  @ApiResponse({ status: 201, description: 'Agent successfully created' })
  @ApiBearerAuth()
  create(@Body() createAgentDto: any) {
    return this.agentsService.create(createAgentDto);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: 'Get all agents' })
  @ApiResponse({ status: 200, description: 'List of agents' })
  findAll() {
    return this.agentsService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get an agent by ID' })
  @ApiResponse({ status: 200, description: 'The requested agent' })
  findById(@Param('id') id: string) {
    return this.agentsService.findById(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent updated' })
  update(@Param('id') id: string, @Body() updateAgentDto: any) {
    return this.agentsService.update(id, updateAgentDto);
  }

  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: string,) {
    if (!['active', 'inactive'].includes(status)) {
      throw new BadRequestException('Status must be either "active" or "inactive"');
    }
    
    return this.agentsService.update(id, { status });
  }

  // Add this new endpoint
@Post('upload')
@UseInterceptors(
  FileInterceptor('file', {
    storage: diskStorage({
      destination: './uploads',
      filename: (req, file, callback) => {
        // Preserve original filename
        const originalName = file.originalname;
        const uploadPath = path.join('./uploads', originalName);
        
        // Check if file already exists
        if (fs.existsSync(uploadPath)) {
          // File already exists, return the existing filename
          callback(null, originalName);
        } else {
          // New file, save with original name
          callback(null, originalName);
        }
      },
    }),
  }),
)
@ApiOperation({ summary: 'Upload an agent avatar' })
@ApiResponse({ status: 201, description: 'File uploaded successfully' })
async uploadFile(@UploadedFile() file: Express.Multer.File) {
  return {
    filename: file.filename,
    path: `/uploads/${file.filename}`
  };
}

@Post(':id/avatar')
@UseInterceptors(
  FileInterceptor('avatar', {
    storage: diskStorage({
       destination: (req, file, cb) => {
        // Use single uploads directory in volume
        const uploadDir = '/usr/src/app/uploads';
        
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        
        cb(null, uploadDir);
      },
      filename: (req, file, callback) => {
        // Generate unique filename to prevent overwrites
        const uniqueSuffix = `${Math.round(Math.random() * 1E9)}`;
        const ext = path.extname(file.originalname);
        callback(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
      },
    }),
  }),
)

@ApiOperation({ summary: 'Upload and set agent avatar' })
@ApiResponse({ status: 200, description: 'Avatar updated successfully' })
async uploadAndSetAvatar(
  @Param('id') id: string,
  @UploadedFile() file: Express.Multer.File
) {

  const distUploadDir = join(__dirname, '../uploads');
  const distFilePath = join(distUploadDir, file.filename);
  
  // Ensure dist/uploads exists
  if (!fs.existsSync(distUploadDir)) {
    fs.mkdirSync(distUploadDir, { recursive: true });
  }
  
  // Copy file to dist/uploads
  fs.copyFileSync(file.path, distFilePath);
    
  // Update the agent with the avatar path
  const avatarPath = `/api/uploads/${file.filename}`;
  const updatedAgent = await this.agentsService.update(id, { avatar: avatarPath });
  
  return {
    ...updatedAgent,
    avatarPath
  };
}

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent deleted' })
  remove(@Param('id') id: string) {
    return this.agentsService.remove(id);
  }

  

}
