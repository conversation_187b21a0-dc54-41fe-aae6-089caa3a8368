import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString, MinLength, IsBoolean, IsMongoId } from 'class-validator';

export class UpdateUserDto {
  @ApiProperty({ required: false, description: 'User email' })
  @IsOptional()
  @IsEmail({}, { message: 'Please provide a valid email' })
  email?: string;

  @ApiProperty({ required: false, description: 'User password' })
  @IsOptional()
  @IsString()
  @MinLength(4, { message: 'Password must be at least 4 characters' })
  password?: string;

  @ApiProperty({ required: false, description: 'Full name of user' })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ required: false, type: Boolean, description: 'Approval status' })
  @IsOptional()
  @IsBoolean()
  isApproved?: boolean;

  @ApiProperty({ required: false, type: String, description: 'Organization ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;
}

