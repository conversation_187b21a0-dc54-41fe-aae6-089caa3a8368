"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VapiModule = void 0;
const common_1 = require("@nestjs/common");
const vapi_controller_1 = require("./vapi.controller");
const vapi_service_1 = require("./vapi.service");
const history_module_1 = require("../history/history.module");
const logger_module_1 = require("../logger/logger.module");
const mongoose_1 = require("@nestjs/mongoose");
const contacts_module_1 = require("../contacts/contacts.module");
const scheduled_call_module_1 = require("../scheduled-call/scheduled-call.module");
const campaign_module_1 = require("../campaign/campaign.module");
const agent_module_1 = require("../agent/agent.module");
const users_module_1 = require("../users/users.module");
const global_settings_module_1 = require("../global-settings/global-settings.module");
const organizations_module_1 = require("../organizations/organizations.module");
const history_schema_1 = require("../history/schemas/history.schema");
let VapiModule = class VapiModule {
};
exports.VapiModule = VapiModule;
exports.VapiModule = VapiModule = __decorate([
    (0, common_1.Module)({
        imports: [
            history_module_1.HistoryModule,
            logger_module_1.LoggerModule,
            contacts_module_1.ContactsModule,
            mongoose_1.MongooseModule.forFeature([{ name: 'History', schema: history_schema_1.HistorySchema }]),
            mongoose_1.MongooseModule,
            (0, common_1.forwardRef)(() => scheduled_call_module_1.ScheduledCallModule),
            campaign_module_1.CampaignModule,
            agent_module_1.AgentModule,
            users_module_1.UsersModule,
            global_settings_module_1.GlobalSettingsModule,
            (0, common_1.forwardRef)(() => organizations_module_1.OrganizationsModule)
        ],
        controllers: [vapi_controller_1.VapiController],
        providers: [vapi_service_1.VapiService],
        exports: [vapi_service_1.VapiService]
    })
], VapiModule);
//# sourceMappingURL=vapi.module.js.map