"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalSettingsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const global_settings_controller_1 = require("./global-settings.controller");
const global_settings_service_1 = require("./global-settings.service");
const global_settings_schema_1 = require("./schemas/global-settings.schema");
const organization_schema_1 = require("../organizations/schemas/organization.schema");
let GlobalSettingsModule = class GlobalSettingsModule {
};
exports.GlobalSettingsModule = GlobalSettingsModule;
exports.GlobalSettingsModule = GlobalSettingsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: 'GlobalSettings', schema: global_settings_schema_1.GlobalSettingsSchema },
                { name: 'Organization', schema: organization_schema_1.OrganizationSchema },
            ]),
        ],
        controllers: [global_settings_controller_1.GlobalSettingsController],
        providers: [global_settings_service_1.GlobalSettingsService],
        exports: [global_settings_service_1.GlobalSettingsService],
    })
], GlobalSettingsModule);
//# sourceMappingURL=global-settings.module.js.map