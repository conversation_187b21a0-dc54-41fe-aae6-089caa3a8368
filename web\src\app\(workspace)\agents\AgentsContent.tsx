/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {Globe, Headset, Loader2,MessageSquare,Mic,Pencil,Phone,PhoneOutgoing,Play,Plus,Search, Video, Volume2} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import FadeIn from "@/animations/FadeIn";
import {DropdownMenu,DropdownMenuContent,DropdownMenuItem,DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import { MoreVertical, Settings, Trash2, FileAudio } from "lucide-react";
import agent<PERSON>isa from "@/assets/img/Binghatti-Lisa.jpeg";
import Image, { StaticImageData } from "next/image";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Agent } from "@/types/agent.types";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/useAuth";
import { useAgent } from "@/hooks/useAgent";
import { useAgentsList } from "@/hooks/useAgentList";
import { AgentPhoneCallDialog } from "@/components/agentscomponents/AgentPhoneCallDialog";
import { AgentChatWidget } from "@/components/agentscomponents/AgentChatWidget";
import { AgentWebCallDialog } from "@/components/agentscomponents/AgentWebCallDialog";


// Define the Assistant interface based on the actual API response

export default function AgentsContent() {
  
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [isCallDialogOpen, setIsCallDialogOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isWebCallOpen, setIsWebCallOpen] = useState(false);
  

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [agentToDelete, setAgentToDelete] = useState<Agent | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  const { userRole, authIsLoading, authError } = useAuth();
  const { agents, agentsisLoading, AgentsError, deleteAgentMutation } = useAgentsList();


    const handleDeleteAgent = async () => {
    if (!agentToDelete) return;
    
    try {
      await deleteAgentMutation.mutateAsync(agentToDelete.id);
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error("Failed to delete agent:", error);
    } finally {
      setAgentToDelete(null);
    }
  };


  const getLanguageDisplay = (languageCode: string): string => {
    const languages: { [key: string]: string } = {
      'en-US': 'English (US)',
      'en-GB': 'English (UK)',
      'en': 'English',
      'fr-FR': 'French',
      'fr': 'French',
      'es-ES': 'Spanish',
      'de-DE': 'German',
      'it-IT': 'Italian',
      'ar-SA': 'Arabic',
      // Add more languages as needed
    };

    return languages[languageCode] || languageCode;
  };

 const filteredAgents = useMemo(() => {
    const filtered = agents.filter(agent => 
      agent.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    // Only show active agents for non-superadmin users
    return userRole === 'superadmin' 
      ? filtered 
      : filtered.filter(agent => agent.status === 'active');
  }, [agents, searchTerm, userRole]);


  return (
    <>
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">AI Agents</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage and interact with your assistants
          </p>
        </div>
        {userRole === 'superadmin' && (
          <Link href="/agents/create">
            <Button
              size="lg"
              className=" text-white transition-all duration-200 hover:scale-107 dark:text-black"
            >
              <Plus className="h-5 w-5" />
              Create New Agent
            </Button>
          </Link>
        )}
      </div>

      {/* Search */}
      <div className="mb-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input
            placeholder="Search assistants..."
            className="pl-10 bg-white dark:bg-gray-800"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {authError && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800">
          {authError}
        </div>
      )}

      {/* Agent Error message */}
      {AgentsError && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800">
          {AgentsError}
        </div>
      )}

      {/* Loading state */}
      {(authIsLoading || agentsisLoading) ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
            <p className="text-lg font-medium">Loading your assistants...</p>
          </div>
        </div>
      ) : (
        /* Assistants Grid */
        <div className="grid gap-6 grid-cols-1 sm:grod-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 auto-rows-fr">
        {filteredAgents.map((agent, index) => (
          <FadeIn key={agent.id} direction="up" delay={0.1}>
            <div 
              className="text-card-foreground flex flex-col rounded-xl border-1  dark:border-gray-500 relative bg-white dark:bg-gray-800 hover:shadow-xl transition-shadow duration-200 overflow-hidden cursor-pointer h-full"                  
              onClick={(e) => {
                // Only navigate if not clicking menu or action buttons
                if (!(e.target as HTMLElement).closest('.clickPrevention')) {
                  router.push(`/agents/edit/${agent.id}`);
                }
              }}
            >
              {/* Add the three-dot menu in the top-right corner */}
              <div className="absolute top-2 right-2 z-20 clickPrevention" onClick={(e) => e.stopPropagation()}>
              {userRole === 'superadmin' && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-full">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem 
                      className="text-blue-500 focus:text-blue-500 cursor-pointer"
                      onSelect={(e) => {
                        e.preventDefault();
                        router.push(`/agents/edit/${agent.id}`);
                      }}
                    >

                      <Pencil className="h-4 w-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                      className="text-red-600 focus:text-red-600 cursor-pointer"
                      onSelect={(e) => {
                        e.preventDefault();
                        setAgentToDelete(agent);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>

                  </DropdownMenuContent>
                </DropdownMenu>
                  )}
              </div>
              {/* ... */}
    
                {/* Avatar */}
       <div className="flex h-full">
        {/* Left side - Avatar */}
        <div className="relative w-[120px] md:w-[120px] lg:w-[130px] xl:w-[140px]">
          <div className="h-full w-full"
          onClick={() => router.push(`/agents/edit/${agent.id}`)}
          >
            {agent.avatar ? (
              <img 
                src={agent.avatar}
                alt={`${agent.name} avatar`}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="h-full w-full bg-black">
                <Image 
                  src={agentLisa}
                  alt={`${agent.name} avatar`}
                  fill
                  className="object-cover"
                />
              </div>
            )}
          </div>
          {/* Status Indicator */}
          <div 
            className={`absolute bottom-2 right-2 h-2 sm:h-3 w-2 sm:w-3 rounded-full border-2 border-white dark:border-gray-800 
             ${agent.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}
          />
        </div>

    {/* Right side - Content */}
    <div className="flex flex-col flex-1 p-3 md:p-2 lg:p-3 xl:p-4">
      {/* Agent Info */}
      <div className="mb-2" 
      onClick={() => router.push(`/agents/edit/${agent.id}`)}
      >
        <h3 className="font-semibold text-sm md:text-base lg:text-sm xl:text-base mb-1 truncate">{agent.name}</h3>
        <div className="space-y-2">
          <p className="text-xs md:text-sm lg:text-xs xl:text-sm text-[#702760] dark:text-white font-medium truncate">
            {agent?.role || 'Assistant'}
          </p>
          <div className="flex items-center gap-1.5">
        {agent.transcriber?.language && (
          <Badge 
            variant="outline" 
            className="text-[10px] md:text-xs lg:text-[10px] xl:text-xs bg-purple-50 dark:bg-purple-900/20 text-gray-700 dark:text-white border-purple-200 dark:border-purple-800"
          >
            {getLanguageDisplay(agent.transcriber.language)}
          </Badge>
        )}
      </div>
        </div>
      </div>

  {/* Action Icons */}
    <div className="flex items-center gap-1.5 md:gap-1 lg:gap-1.5 xl:gap-2 mt-auto"
    onClick={(e) => e.stopPropagation()}
    >
      <TooltipProvider>  
        <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-emerald-50 dark:hover:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 transition-all duration-200 hover:scale-115"
              >
                <Volume2 className="h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-emerald-500 dark:text-emerald-400" />
              </Button>
            </TooltipTrigger>
            <TooltipContent className="text-xs sm:text-sm border-gray-200 dark:border-gray-700">
              <p>Test voice</p>
            </TooltipContent>
          </Tooltip>

    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-blue-200 dark:border-gray-700 transition-all duration-200 hover:scale-115"
          onClick={(e) => {
          if (isChatOpen && selectedAgent?.id === agent.id) {
            // If chat is open and it's the same agent, close it
            setIsChatOpen(false);
            setSelectedAgent(null);
          } else {
            // If chat is closed or it's a different agent, open it
            setSelectedAgent(agent);
            setIsChatOpen(true);
          }
        }}
        >
          <MessageSquare className="h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-blue-500 dark:text-blue-400" />
        </Button>
      </TooltipTrigger>
      <TooltipContent 
        className="text-xs sm:text-sm border-gray-200 dark:border-gray-700"
      >
        <p>Chat with agent</p>
      </TooltipContent>
    </Tooltip>

    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-indigo-50 dark:hover:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 transition-all duration-200 hover:scale-115"
          onClick={(e) => {
            setSelectedAgent(agent);
            setIsWebCallOpen(true);
          }}
        >
          <Headset className="h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-indigo-500 dark:text-indigo-400" />
        </Button>
      </TooltipTrigger>
      <TooltipContent 
        className="border-2 border-gray-200 dark:border-gray-700"
      >
        <p>Start web call</p>
      </TooltipContent>
    </Tooltip>

    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-purple-50 dark:hover:bg-purple-900/20 border border-purple-200 dark:border-gray-700 transition-all duration-200 hover:scale-115"
          onClick={(e) => {
          setSelectedAgent(agent);
          setIsCallDialogOpen(true);
        }}
        >
          <PhoneOutgoing className="h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-purple-500 dark:text-purple-400" />
        </Button>
      </TooltipTrigger>
      <TooltipContent 
        className="text-xs sm:text-sm border-2 border-gray-200 dark:border-gray-700"
      >
        <p>Start phone call</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
</div>
    </div>
      </div>         
            </div>
          </FadeIn>
        ))}
      </div>
      )}

  <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
        <AlertDialogDescription>
          This will permanently delete the agent &quot;{agentToDelete?.name}&quot;. 
          This action cannot be undone.
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
        <AlertDialogAction
          onClick={handleDeleteAgent}
          disabled={isDeleting}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          {isDeleting ? "Deleting..." : "Delete"}
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>


      {/* Phone Call Dialog */}
     <AgentPhoneCallDialog 
        agent={selectedAgent}
        isOpen={isCallDialogOpen}
        onClose={() => setIsCallDialogOpen(false)}
      />

      {/* Chat Widget */}
      <AgentChatWidget 
        agent={selectedAgent}
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
      />

      {/* Agent Web Call Dialog */}
      <AgentWebCallDialog 
      agent={selectedAgent}
      isOpen={isWebCallOpen}
      onClose={() => setIsWebCallOpen(false)}
    />

    </>
  );
}