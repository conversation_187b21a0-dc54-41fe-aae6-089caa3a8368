"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationPaymentMethodSchema = exports.OrganizationPaymentMethod = void 0;
const mongoose_1 = require("@nestjs/mongoose");
let OrganizationPaymentMethod = class OrganizationPaymentMethod {
};
exports.OrganizationPaymentMethod = OrganizationPaymentMethod;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "organizationId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "stripePaymentMethodId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "last4", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "expMonth", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "expYear", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "cardholderName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], OrganizationPaymentMethod.prototype, "isDefault", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], OrganizationPaymentMethod.prototype, "brand", void 0);
exports.OrganizationPaymentMethod = OrganizationPaymentMethod = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], OrganizationPaymentMethod);
exports.OrganizationPaymentMethodSchema = mongoose_1.SchemaFactory.createForClass(OrganizationPaymentMethod);
//# sourceMappingURL=organization-payment-method.schema.js.map