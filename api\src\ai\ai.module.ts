import { Module, forwardRef } from '@nestjs/common';
import { AiService } from './ai.service';
// import { AiGateway } from './ai.gateway';
import { HttpModule } from '@nestjs/axios';
import { ConversationsModule } from '../conversations/conversations.module';
import { AgentModule } from 'src/agent/agent.module';

@Module({
  imports: [
    HttpModule,
    AgentModule,
    forwardRef(() => ConversationsModule),  
  ],
  providers: [AiService],
  exports: [AiService],
})
export class AiModule {}
