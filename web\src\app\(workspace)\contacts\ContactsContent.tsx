/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";


import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {Table,TableBody,TableCell,TableHead,TableHeader,TableRow} from "@/components/ui/table";
import {Filter,Search,MoreVertical,Plus,Phone,Edit,Trash2,CheckCircle,XIcon,History,Calendar,ArrowRight,User,Users,Loader2, PhoneCall, Columns, Columns2, GripVertical, Eye, EyeOff, RotateCcw, Upload, FileDown, FileText, FileSpreadsheet} from "lucide-react";
import { useState, useEffect, useRef, useMemo } from "react";
import {Dialog,DialogContent,DialogDescription,DialogFooter,DialogHeader,DialogTitle,DialogClose,} from "@/components/ui/dialog";
import {AlertDialog,AlertDialogAction,AlertDialogCancel,AlertDialogContent,AlertDialogDescription,AlertDialogFooter,AlertDialogHeader,AlertDialogTitle} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Link from "next/link";

import {callContacts,Contact,deleteContact,fetchCampaign,fetchContacts,importContacts,scheduleCall} from "@/app/api/contacts";
import {DropdownMenu,DropdownMenuContent,DropdownMenuItem,DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import FadeIn from "@/animations/FadeIn";
import { useCredits } from "@/contexts/CreditContext";
import { LowCreditAlert } from "@/components/LowCreditAlert";
import { CreditWarningAlert } from "@/components/CreditWarningAlert";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Agent } from "@/types/agent.types";

interface ScheduledCall {
  agentId: string;
  contacts: { Name: string; MobileNumber: string }[];
  scheduledTime: Date;
  region: string;
  status: string;
}

// Campaign interface
interface Campaign {
  _id: string;
  name: string;
}


type ColumnType = 'customerId' | 'name' | 'phoneNumber' | 'lastCall' |  'campaign' | 'createdAt' | 'timeZone' | 'source' | 'addedBy' | 'call' | 'actions';

interface ColumnItem {
  id: ColumnType;
  label: string;
}


export default function ContactsContent() {
  const { credits, hasSufficientCredits, minutes, organizationCreditThreshold } = useCredits();
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>("all");
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [scheduledCallsCount, setScheduledCallsCount] = useState<number>(0);
  // agents state
  const [agents, setAgents] = useState<Agent[]>([]);
  const inputRef = useRef<HTMLInputElement>(null); // a ref for the input


  // states for paginations
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loadingRef = useRef<HTMLTableRowElement>(null);
  const ITEMS_PER_PAGE = 20;

  const MAX_TOTAL_SIZE = 10 * 1024 * 1024; // 10MB



  const [searchTerm, setSearchTerm] = useState("");
  const [columnsDialogOpen, setColumnsDialogOpen] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState({
    customerId: true,
    name: true,
    phoneNumber: true,
    lastCall: true,
    campaign: true,
    createdAt: true,
    timeZone: true,
    source: true,
    addedBy: true,
    call: true,
    actions: true
  });

  // Update your component to include this state
const defaultColumnOrder: ColumnItem[] = [
  { id: 'customerId', label: 'Contact ID' },
  { id: 'name', label: 'Name' },
  { id: 'phoneNumber', label: 'Phone Number' },
  { id: 'lastCall', label: 'Last Call' },
  { id: 'campaign', label: 'Campaign' },
  { id: 'createdAt', label: 'Created At' },
  { id: 'timeZone', label: 'Time Zone' },
  { id: 'source', label: 'Source' },
  { id: 'addedBy', label: 'Added By' },
];

  const [columnOrder, setColumnOrder] = useState<ColumnItem[]>(defaultColumnOrder);

  const [showImport, setShowImport] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const [importSuccessOpen, setImportSuccessOpen] = useState(false);
  const [importResults, setImportResults] = useState<{
    totalProcessed?: number;
    totalFiles?: number;
    totalSuccess?: number;
    totalError?: number;
    fileResults?: {
    fileName: string;
    successCount?: number;
    errorCount?: number;
    totalProcessed?: number;
    successfulContacts?: Array<{
      contactName: string;
      phoneNumber: string;
      region?: string;
      source: string;
      addedBy: string;
    }>;
  }[]
    successCount?: number;
    errorCount?: number;
  } | null>(null);


  const [fileErrors, setFileErrors] = useState<{[key: string]: string}>({});
  const [importError, setImportError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [callDialogOpen, setCallDialogOpen] = useState(false);
  const [activeContact, setActiveContact] = useState<Contact | null>(null);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [calling, setCalling] = useState(false);
  const [callMode, setCallMode] = useState<"now" | "schedule">("now");
  const [callError, setCallError] = useState<string | null>(null);

  // First, add this state to track if the calendar is open
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Success state at the top with other states
  const [scheduleSuccessOpen, setScheduleSuccessOpen] = useState(false);
  const [successScheduleDetails, setSuccessScheduleDetails] = useState<{
    contacts: { Name: string }[];
    scheduledTime: string;
    agentName: string;
    region: string;
  } | null>(null);

  const [scheduledDateTime, setScheduledDateTime] = useState("");
  const [contactToDeleteId, setContactToDeleteId] = useState<string | null>(null);
  const [multiDeleteDialogOpen, setMultiDeleteDialogOpen] = useState(false);

  const [campaigns, setCampaigns] = useState<{ _id: string; name: string }[]>(
    []
  );
  const [filterType, setFilterType] = useState<"name" | "campaign">("name");
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [importing, setImporting] = useState(false);

  const [selectedAgent, setSelectedAgent] = useState("");
  const userRegion = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const PLATFORM_ENV = process.env.NEXT_PUBLIC_PLATFORM_ENV || "dev";

  const fetchUserProfile = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token available");
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      const userData = await response.json();
      setUserRole(userData.role);
    } catch (err) {
      console.error("Failed to load user profile:", err);
    }
  }

  const fetchAgents = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );
      if (!response.ok) throw new Error("Failed to fetch agents");
      const fetchedAgents = await response.json();

      if (userRole === 'superadmin') {
        setAgents(fetchedAgents);
      } else {
        const activeAgents = fetchedAgents.filter((agent: { status: string; }) => agent.status === 'active');
        setAgents(activeAgents);
      }
    } catch (error) {
      console.error("Error fetching agents:", error);
    }
  };

  // Load contacts function
  const loadContacts = async (pageNum: number = 1, search?: string) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
      const filterParam = `&filterType=${filterType}`;

      const campaignParam = selectedCampaignId !== "all" && selectedCampaignId !== "unknown"
      ? `&campaignId=${selectedCampaignId}`
      : selectedCampaignId === "unknown" ? `&noCampaign=true` : '';

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contacts?page=${pageNum}&limit=${ITEMS_PER_PAGE}${searchParam}${filterParam}${campaignParam}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );

      if (!response.ok) throw new Error("Failed to fetch contacts");
      const data = await response.json();

      if (pageNum === 1) {
        setContacts(data);
      } else {
        setContacts(prev => [...prev, ...data]);
      }

      // Update hasMore based on received data length
      setHasMore(data.length === ITEMS_PER_PAGE);

    } catch (error) {
      console.error("Error loading contacts:", error);
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  };

  // Add search handler
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    loadContacts(1, searchTerm);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        const firstEntry = entries[0];
        if (firstEntry.isIntersecting && hasMore && !loading && !isLoadingMore) {
          setPage(prev => prev + 1);
        }
      },
      {
        threshold: 0.1 // Trigger when even 10% of the element is visible
      }
    );

    const currentRef = loadingRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, loading, isLoadingMore]);

  // Add effect for page changes
  useEffect(() => {
    if (page > 1) {
      loadContacts(page, searchTerm);
    }
  }, [page, selectedCampaignId]);



  const loadCampaigns = async () => {
    try {
      const data = await fetchCampaign();
      setCampaigns(data);
    } catch (error) {
      console.error("Error loading campaigns:", error);
    }
  };


  const fetchScheduledCallsCount = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/scheduled-call?page=1&limit=1`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );
      if (!response.ok) throw new Error("Failed to fetch scheduled calls");
      const calls: ScheduledCall[] = await response.json();

      if (calls.length > 0 && 'totalSchedules' in calls[0]) {
        const totalCount = Number(calls[0].totalSchedules);
      setScheduledCallsCount(isNaN(totalCount) ? 0 : totalCount);
      } else {
        // Fallback to zero if no records or no totalSchedules field
        setScheduledCallsCount(0);
      }
    } catch (error) {
      console.error("Error fetching scheduled calls count:", error);
      setScheduledCallsCount(0);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch user profile to get role
        await fetchUserProfile();
        // Fetch agents
        await fetchAgents();

        setPage(1)
        // Fetch contacts
        await loadContacts(1);
        // Fetch campaigns
        await loadCampaigns();
        // Fetch scheduled calls count
        await fetchScheduledCallsCount();
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle importing contacts
  const handleImportContacts = async () => {
    setImporting(true);
    try {
      await importContacts();
      await loadContacts();
    } catch (error) {
      console.error("Error importing contacts:", error);
    } finally {
      setImporting(false);
    }
  };

  // Add a function to handle campaign selection
  const handleCampaignSelect = (campaignId: string) => {
  setSelectedCampaignId(campaignId);
  setPage(1); // Reset to first page


  const searchParam = searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : '';
  const filterParam = `&filterType=${filterType}`;
  const campaignParam = campaignId !== "all" && campaignId !== "unknown"
    ? `&campaignId=${campaignId}`
    : campaignId === "unknown" ? `&noCampaign=true` : '';

  setLoading(true);
  fetch(
    `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contacts?page=1&limit=${ITEMS_PER_PAGE}${searchParam}${filterParam}${campaignParam}`,
    {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
      },
    }
  )
    .then(response => {
      if (!response.ok) throw new Error("Failed to fetch contacts");
      return response.json();
    })
    .then(data => {
      setContacts(data);
      setHasMore(data.length === ITEMS_PER_PAGE);
    })
    .catch(error => {
      console.error("Error loading contacts:", error);
    })
    .finally(() => {
      setLoading(false);
    });
};

  const handleMakeCall = (contact: Contact) => {
    setActiveContact(contact);
    setSelectedContacts([]);
    setCallDialogOpen(true);
  };

  const handleSelectContact = (contact: Contact) => {
    if (selectedContacts.some((c) => c._id === contact._id)) {
      setSelectedContacts(
        selectedContacts.filter((c) => c._id !== contact._id)
      );
    } else {
      setSelectedContacts([...selectedContacts, contact]);
    }
  };

  const handleSelectAll = () => {
    if (selectedContacts.length === contacts.length) {
      setSelectedContacts([]);
    } else {
      setSelectedContacts(contacts);
    }
  };

  const handleCallSelected = () => {
    if (selectedContacts.length > 0) {
      setActiveContact(null);
      setCallDialogOpen(true);
    }
  };

  const handleScheduleCall = async (agentId: string) => {
    // Clear any previous errors
    setCallError(null);

    // Check if an agent is selected
    if (!agentId) {
      setCallError("Please select an agent before scheduling");
      return;
    }

    // Check if date is selected
    if (!scheduledDateTime) {
      setCallError("Please select a date and time for the schedule");
      return;
    }

    let contactsPayload;
    let contactRegion = userRegion; // Default to user's timezone

    if (activeContact) {
      contactsPayload = [
        {
          Name: activeContact.contactName,
          MobileNumber: activeContact.phoneNumber.startsWith("+")
            ? activeContact.phoneNumber
            : `+${activeContact.phoneNumber}`,
        },
      ];
      // Use contact's region if available
      contactRegion = activeContact.region || userRegion;
    } else if (selectedContacts.length > 0) {
      contactsPayload = selectedContacts.map((c) => ({
        Name: c.contactName,
        MobileNumber: c.phoneNumber.startsWith("+")
          ? c.phoneNumber
          : `+${c.phoneNumber}`,
      }));
      // If single contact selected, use their region
      if (selectedContacts.length === 1) {
        contactRegion = selectedContacts[0].region || userRegion;
      }
    } else {
      return;
    }

    setCalling(true);
    try {
      await scheduleCall(
        agentId,
        contactsPayload,
        scheduledDateTime,
        contactRegion // Use contact's region for scheduling
      );

      // Refresh scheduled calls count after successful scheduling
      await fetchScheduledCallsCount();

      // Set success details and show success dialog
      setSuccessScheduleDetails({
        contacts: contactsPayload,
        scheduledTime: scheduledDateTime,
        agentName:
          agents.find((agent) => agent.id === agentId)?.name || "Unknown",
        region: contactRegion, // Use contact's region in success details
      });
      setScheduleSuccessOpen(true);
      toast.success("Call scheduled successfully");
    } catch (error) {
      console.error("Error scheduling call:", error);

      // Check if it's a credit-related error
      const errorMessage = error instanceof Error ? error.message : "Failed to schedule call";
      if (errorMessage.includes("Insufficient credits") || errorMessage.includes("add funds")) {
        toast.error(errorMessage);
      } else {
        setCallError("Failed to schedule call. Please try again.");
      }
    } finally {
      setCalling(false);
      setCallDialogOpen(false);
      setActiveContact(null);
      setSelectedContacts([]);
      setScheduledDateTime("");
    }
  };

  const handleStartCall = async (agentId: string) => {
    // Clear any previous errors
    setCallError(null);

    // Check if an agent is selected
    if (!agentId) {
      setCallError("Please select an agent before starting the call");
      return;
    }

    let contactsPayload;
    if (activeContact) {
      contactsPayload = [
        {
          Name: activeContact.contactName,
          MobileNumber: activeContact.phoneNumber.startsWith("+")
            ? activeContact.phoneNumber
            : `+${activeContact.phoneNumber}`,
        },
      ];
    } else if (selectedContacts.length > 0) {
      contactsPayload = selectedContacts.map((c) => ({
        Name: c.contactName,
        MobileNumber: c.phoneNumber.startsWith("+")
          ? c.phoneNumber
          : `+${c.phoneNumber}`,
      }));
    } else {
      return;
    }

    setCalling(true);
    try {
      const data = await callContacts(agentId, contactsPayload, userRegion);
      console.log("Call initiated:", data);
      toast.success("Call initiated successfully");
    } catch (error) {
      console.error("Error calling contact(s):", error);

      // Check if it's a credit-related error
      const errorMessage = error instanceof Error ? error.message : "Failed to initiate call";
      if (errorMessage.includes("Insufficient credits") || errorMessage.includes("add funds")) {
        toast.error(errorMessage);
      } else {
        setCallError("Failed to initiate call. Please try again.");
      }
    } finally {
      setCalling(false);
      setCallDialogOpen(false);
      setActiveContact(null);
      setSelectedContacts([]);
    }
  };

  const handleDeleteContact = (id: string) => {
    setContactToDeleteId(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteContact = async () => {
    if (!contactToDeleteId) return;

    try {
      await deleteContact(contactToDeleteId);
      await loadContacts();
      setDeleteDialogOpen(false);
      setContactToDeleteId(null);
    } catch (error) {
      console.error("Error deleting contact:", error);
    }
  };

  const confirmMultipleDelete = async () => {
  try {
    // Show loading state
    setLoading(true);

    // Delete all selected contacts
    await Promise.all(
      selectedContacts.map(contact => deleteContact(contact._id))
    );

    // Refresh the contacts list
    await loadContacts();

    // Clear selection and close dialog
    setSelectedContacts([]);
    setMultiDeleteDialogOpen(false);

    // Optional: Show success toast/notification
  } catch (error) {
    console.error("Error deleting contacts:", error);
  } finally {
    setLoading(false);
  }
};


  const getCurrentDateTimeLocal = () => {
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    return now.toISOString().slice(0, 16);
  };

  const handleSort = (field: string) => {
    // If clicking the same field, toggle direction
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If clicking a new field, set it as sort field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const toggleColumnVisibility = (column: keyof typeof visibleColumns) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column]
    }));
  };


  const sortedContacts = useMemo(() => {
    if (!sortField) return contacts;

    return [...contacts].sort((a, b) => {
      let valueA, valueB;
      // Extract the values to compare based on the sort field
      if (sortField === 'name') {
        valueA = a.contactName.toLowerCase();
        valueB = b.contactName.toLowerCase();
      } else if (sortField === 'phoneNumber') {
        valueA = a.phoneNumber;
        valueB = b.phoneNumber;
      } else if (sortField === 'lastCall') {
        // For lastCall, compare the dates (or use 0 if null)
        valueA = a.lastCall ? new Date(a.lastCall).getTime() : 0;
        valueB = b.lastCall ? new Date(b.lastCall).getTime() : 0;
      } else if (sortField === 'timeZone') {
        // For timeZone, compare the region strings (or empty string if null)
        valueA = a.region ? a.region.toLowerCase() : '';
        valueB = b.region ? b.region.toLowerCase() : '';
      } else if (sortField === 'campaign') {
        // For campaigns, compare the first campaign name or empty string
        valueA = a.campaigns && a.campaigns.length > 0 ?
          (typeof a.campaigns[0] === 'object' ?
            (a.campaigns[0] as Campaign).name || '' :
            a.campaigns[0] || '') :
          '';
        valueB = b.campaigns && b.campaigns.length > 0 ?
          (typeof b.campaigns[0] === 'object' ?
            (b.campaigns[0] as Campaign).name || '' :
            b.campaigns[0] || '') :
          '';
      } else if (sortField === 'source') {
          valueA = a.source?.toLowerCase() || '';
          valueB = b.source?.toLowerCase() || '';
        } else if (sortField === 'addedBy') {
          valueA = a.addedBy?.toLowerCase() || '';
          valueB = b.addedBy?.toLowerCase() || '';
        } else if (sortField === 'createdAt') {
          valueA = new Date(a.createdAt).getTime();
          valueB = new Date(b.createdAt).getTime();
        } else {
        return 0;
      }
      // Compare the values based on sort direction
      if (sortDirection === 'asc') {
        return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
      } else {
        return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
      }
    });
  }, [contacts, sortField, sortDirection]);


    // DND handlers
    const sensors = useSensors(
      useSensor(PointerSensor),
      useSensor(KeyboardSensor, {
        coordinateGetter: sortableKeyboardCoordinates,
      })
    );

    const handleDragEnd = (event: any) => {
      const { active, over } = event;

      if (active.id !== over.id) {
        setColumnOrder((items) => {
          const oldIndex = items.findIndex((item) => item.id === active.id);
          const newIndex = items.findIndex((item) => item.id === over.id);

          return arrayMove(items, oldIndex, newIndex);
        });
      }
    };

  const handleImport = async () => {
  setImporting(true);
  setFileErrors({});
  setImportError(null);

  try {
    const results = [];

    // Process each file sequentially
    for (const file of selectedFiles) {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/contacts/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to import contacts from ${file.name}`);
      }

      const result = await response.json();
      results.push({
        fileName: file.name,
        ...result
      });
    }

    // Aggregate results
    const aggregatedResults = {
      totalFiles: results.length,
      totalProcessed: results.reduce((sum, r) => sum + r.totalProcessed, 0),
      successCount: results.reduce((sum, r) => sum + r.successCount, 0),
      errorCount: results.reduce((sum, r) => sum + r.errorCount, 0),
      fileResults: results
    };

    // Set the results and show success dialog
    setImportResults(aggregatedResults);
    setImportSuccessOpen(true);

    // Clear files and close import section
    setSelectedFiles([]);
    setShowImport(false);

    // Refresh contacts list
    await loadContacts();

  } catch (error) {
    console.error('Import error:', error);
    setImportError('Failed to import contacts. your file(s) doesn\'t have the correct columns structure.');
  } finally {
    setImporting(false);
  }
};


  const getVisibleColumnsCount = () => {
    const baseColumnCount = Object.values(visibleColumns).filter(Boolean).length;
    // Add 2 for the checkbox and actions columns that are always visible
    return baseColumnCount + 2;
  };


  return (
    <>
      <FadeIn>
        {/* Credit Alerts */}
        <CreditWarningAlert credits={credits} threshold={organizationCreditThreshold} />
        <LowCreditAlert credits={credits} threshold={organizationCreditThreshold} />
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
  <div className="flex items-center gap-3 w-full sm:w-auto">
    <h1 className="text-2xl font-semibold">Contacts</h1>
    {scheduledCallsCount > 0 && (
      <Link
        href="/schedule"
        className="flex items-center gap-2 bg-blue-50 text-blue-600 px-3 py-1.5 rounded-full text-sm font-medium hover:bg-blue-100 transition-colors mt-1 dark:bg-gray-250 "
      >
        <div className="flex items-center">
          {contacts.length > 0 && (
          <span className="flex items-center text-sm font-medium mr-2 ">
            <Phone className="h-4 w-4 mr-2" />
            Showing {contacts.length} of {contacts[0]?.filteredContacts
              ? `${contacts[0].filteredContacts} ${
                  selectedCampaignId === "all"
                    ? "contacts"
                    : selectedCampaignId === "unknown"
                      ? "contacts (No Campaign)"
                      : `contacts in ${campaigns.find(c => c._id === selectedCampaignId)?.name || "campaign"}`
                }`
              : contacts[0]?.totalContacts
                ? `${contacts[0].totalContacts} contacts`
                : `${contacts.length} contacts`
            }
          </span>
        )}
          {/* <Calendar className="h-4 w-4 mr-1" />
          {scheduledCallsCount} scheduled */}
        </div>
      </Link>
    )}
  </div>
  <div className="flex flex-wrap gap-2 w-full sm:w-auto">
    {PLATFORM_ENV === "binghatti" && (
      <Button
        className="bg-[#383D73] text-white flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto"
        onClick={handleImportContacts}
        disabled={importing}
      >
        {importing ? (
          <>
            <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
            <span className="whitespace-nowrap">Importing...</span>
          </>
        ) : (
          <>
            <span className="whitespace-nowrap">Import contacts</span>
          </>
        )}
      </Button>
    )}

     <Button
    variant="outline"
    onClick={() => setShowImport(!showImport)}
    className="flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto dark:border-gray-300 "
    >
    <Upload className="h-4 w-4" />
    Upload Contacts
    </Button>

    <Link href="/contacts/create" className="flex-1 sm:flex-auto">
      <Button className="bg-primary hover:bg-primary/90 w-full text-sm sm:text-base">
        Add Contact
      </Button>
    </Link>

  </div>
</div>


{showImport && (
  <div className="mt-4 mb-4 p-4  border rounded-lg bg-card dark:bg-gray-800/50 animate-in fade-in transition-all duration-600 dark:border-gray-500">
    <div className="flex gap-6">
      {/* Left side - Upload */}
     <div className="flex-1">
  <label
    htmlFor="file-upload"
    className="border-2 border-dashed rounded-lg hover:border-primary/50 transition-colors block cursor-pointer h-[250px] relative"
  >
    {selectedFiles.length === 0 ? (
      // Empty state - show upload prompt
      <div className="flex flex-col items-center justify-center h-full gap-3 p-8">
        <Upload className="h-8 w-8 text-muted-foreground" />
        <div className="flex flex-col items-center gap-1">
          <span className="text-sm font-medium text-primary">
            Click to upload
          </span>
          <span className="text-sm text-muted-foreground">
            or drag and drop
          </span>
        </div>
      </div>
    ) : (
      // Files selected state
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-4 border-b dark:border-gray-700">
          <span className="text-sm font-medium text-muted-foreground">
            Selected Files ({selectedFiles.length})
          </span>
        </div>

        {/* Scrollable file list */}
        <div className="flex-1 overflow-auto p-2 space-y-2">
          {selectedFiles.map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
            >
              <div className="flex items-center gap-2">
                {file.name.endsWith('.csv') ? (
          <FileText className="h-7 w-7 text-green-600 dark:text-green-500" />
            ) : (
              <FileSpreadsheet className="h-7 w-7 text-emerald-600 dark:text-emerald-500" />
            )}
                <div className="flex flex-col">
                  <span className="text-sm truncate max-w-[200px]">
                    {file.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ({(file.size / 1024).toFixed(1)} KB)
                  </span>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.preventDefault();
                  setSelectedFiles(prev => prev.filter((_, i) => i !== index));
                  // Reset the input value so the same file can be selected again
                  if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                  }
                }}
                className="h-8 w-8 text-red-500 hover:text-red-600"
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        {/* Upload prompt footer */}
        <div className="p-3 border-t dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 flex items-center justify-center">
          <span className="text-sm text-muted-foreground">
            Drop more files here or click to browse
          </span>
        </div>
      </div>
    )}

    <input
      id="file-upload"
      type="file"
      className="hidden"
      accept=".csv,.xlsx,.xls"
      multiple
      ref={fileInputRef}
      onChange={(e) => {
      if (e.target.files?.length) {
        // Calculate total size of existing files
        const existingFilesSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);

        // Calculate size of new files
        const newFiles = Array.from(e.target.files);
        const newFilesSize = newFiles.reduce((sum, file) => sum + file.size, 0);

        // Check if total size exceeds limit
        if (existingFilesSize + newFilesSize > MAX_TOTAL_SIZE) {
          alert('File(s) size exceeds 10MB limit. Please select smaller files.');
          // Reset input value
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
          return;
        }

        setSelectedFiles(prev => [...prev, ...newFiles]);
      }
    }}
    />
  </label>
</div>

      {/* Right side - Instructions */}
      <div className="flex-1">
        <h3 className="text-lg font-semibold mb-2">File Requirements</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Upload a CSV or Excel file with contact details. At minimum include Name and Phone number or mobile.
          You can check an example by clicking on one of the templates.
        </p>

        <div className="flex space-x-2 mt-8">
          <Button
            variant="outline"
            className=" justify-start dark:border-gray-500"
            onClick={() => {
              const link = document.createElement('a');
              link.href = '/templates/Orova_Template-csv.csv';
              link.download = 'orova-template.csv';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
          >
            <FileDown className="h-4 w-4 mr-2" />
            Download CSV Template
          </Button>
          <Button
            variant="outline"
            className=" justify-start dark:border-gray-500 "
            onClick={() => {
              const link = document.createElement('a');
              link.href = '/templates/Orova_Template-excel.xlsx';
              link.download = 'orova-template.xlsx';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
          >
            <FileDown className="h-4 w-4 mr-2" />
            Download Excel Template
          </Button>
        </div>
         {/* Import Button - Only show when files are selected */}
        {selectedFiles.length > 0 && (
        <div className="mt-4">
          {Object.entries(fileErrors).map(([fileName, error]) => (
            <div key={fileName} className="text-red-500 text-sm mb-2">
              Error in {fileName}: {error}
            </div>
          ))}
          {importError && (
            <p className="text-red-500 text-sm mb-2">
              {importError}
            </p>
          )}
          <Button
            className="w-1/3 mt-5 bg-primary"
            onClick={handleImport}
            disabled={importing || Object.keys(fileErrors).length > 0}
          >
            {importing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Importing...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Import {selectedFiles.length} {selectedFiles.length === 1 ? 'File' : 'Files'}
              </>
            )}
          </Button>
        </div>
      )}
      </div>

    </div>
  </div>
)}

<div className="flex flex-col md:flex-row gap-6">

    {/* Contacts Table Container */}
    <div className="flex-1 max-w-full">
      {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-2 mb-6">
          <form onSubmit={handleSearch} className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder={filterType === "name" ? "Search by name or phone..." : "Search by campaign..."}
              className="pl-10 pr-20"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />

          </form>


            <div className="flex gap-2">
      {/* Campaign Dropdown/Select - Replacing the sidebar */}
      <Select
        value={selectedCampaignId}
        onValueChange={handleCampaignSelect}
      >
        <SelectTrigger className="w-full ">
          <SelectValue placeholder="Select Campaign" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Campaigns</SelectItem>
          {campaigns.map((campaign) => (
            <SelectItem key={campaign._id} value={campaign._id}>
              {campaign.name}
            </SelectItem>
          ))}
          <SelectItem value="unknown">Unknown (No Campaign)</SelectItem>
        </SelectContent>
      </Select>

       {/* Columns visibility button */}
      <Button
        variant="outline"
        onClick={() => setColumnsDialogOpen(true)}
        className="flex items-center gap-2"
      >
        <Columns2 className="h-4 w-4" />
        <span className="hidden sm:inline">Columns</span>
      </Button>

            </div>
          </div>

          {selectedContacts.length > 0 && (
            <div className="flex items-center justify-between mb-4 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
              <span className="flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 gap-x-3 ml-2">
                <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                {selectedContacts.length} contact{selectedContacts.length !== 1 ? 's' : ''} selected
              </span>
              <div className="flex items-center gap-2">
              <Button
                className="bg-green-600 text-white flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto"

                onClick={handleCallSelected}
                disabled={selectedContacts.length === 0}
              >
                <Phone className="h-4 w-4" />
                <span className="whitespace-nowrap">Call Selected</span>
              </Button>

              <Button
                variant="outline"

                className="text-red-600 dark:text-red-400 border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                onClick={() => setMultiDeleteDialogOpen(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selection
              </Button>
              </div>
            </div>
          )}

          {/* Contacts Table with fixed height */}
          <div className=" bg-card rounded-lg border shadow-sm overflow-hidden dark:bg-gray-800 dark:border-gray-700">
            <div className="w-full overflow-x-auto ">
              <Table>
             <TableHeader className="sticky top-0 bg-card z-10 dark:bg-gray-800 border-b-2 border-gray-300  dark:border-gray-700">
            <TableRow className="bg-gray-50 dark:bg-gray-800/90">
              {/* Header checkbox for select-all */}
              <TableHead className="w-12 py-3 font-semibold text-gray-700 dark:text-gray-200 ">
                <input
                  type="checkbox"
                  className="rounded ml-2"
                  checked={
                    contacts.length > 0 &&
                    selectedContacts.length === contacts.length
                  }
                  onChange={handleSelectAll}
                />
              </TableHead>

               {columnOrder.map(column => (
              visibleColumns[column.id] && (
                <TableHead
                  key={column.id}
                  className="font-bold cursor-pointer text-gray-700 dark:text-gray-200"
                  onClick={() => handleSort(column.id)}
                >
                  <div className="flex items-center">
                    {column.label}
                    {sortField === column.id && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
              )
            ))}
                <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center">Call</TableHead>

                <TableHead className="font-bold text-gray-700 dark:text-gray-200 text-center">Actions</TableHead>

            </TableRow>
          </TableHeader>
              <TableBody>
                {loading && page === 1 ? (
                  <TableRow>
                    <TableCell colSpan={getVisibleColumnsCount()} className="h-80 text-center p-0">
                      <div className="flex flex-col items-center justify-center h-full">
                        <Loader2 className="h-12 w-12 animate-spin text-primary" />
                        <p className="text-lg font-medium">Loading...</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : contacts.length > 0 ? (
                  sortedContacts.map((contact) => (
                    <TableRow
                    key={contact._id}
                    className="animate-in fade-in slide-in-from-bottom-10 duration-500"
                    >
                      <TableCell>
                        <input
                          type="checkbox"
                          className="rounded ml-2"
                          checked={selectedContacts.some(
                            (c) => c._id === contact._id
                          )}
                          onChange={() => handleSelectContact(contact)}
                        />
                      </TableCell>

                       {/* Use columnOrder for dynamic cells */}
                      {columnOrder.map(column => (
                        visibleColumns[column.id] && (
                          <TableCell key={column.id} >
                            {/* Render cell content based on column type */}
                            {column.id === 'customerId' && (
                            <span className="font-mono text-sm text-black dark:text-white">
                              {contact.customerId || '-'}
                            </span>
                          )}

                            {column.id === 'name' && (
                              <span className="font-medium">{contact.contactName}</span>
                            )}
                            {column.id === 'phoneNumber' && contact.phoneNumber}
                            {column.id === 'lastCall' && (
                              (contact.lastCall != null &&
                                new Date(contact.lastCall).toLocaleString("en-GB", {
                                  year: "numeric",
                                  month: "2-digit",
                                  day: "2-digit",
                                  hour: "2-digit",
                                  minute: "2-digit",
                                  second: "2-digit",
                                  hour12: false,
                                })) || "-"
                            )}

                            {column.id === 'campaign' && (
                          <div className="flex flex-wrap gap-2">
                            {/* Show linked campaigns */}
                            {contact.campaigns && contact.campaigns.length > 0 &&
                              contact.campaigns.map((campaign, index) => (
                                <span key={index} className="bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">
                                  {typeof campaign === "object" ? campaign.name : campaign}
                                </span>
                              ))
                            }
                            {/* Show campaign names from upload */}
                            {contact.campaignNames && contact.campaignNames.length > 0 &&
                              contact.campaignNames.map((name, index) => (
                                <span key={`name-${index}`} className="bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">
                                  {name}
                                </span>
                              ))
                            }
                            {(!contact.campaigns?.length && !contact.campaignNames?.length) && "-"}
                          </div>
                        )}

                             {column.id === 'createdAt' && (
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                {contact.createdAt ?
                                  new Date(contact.createdAt).toLocaleString('en-GB', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false
                                  })
                                  : '-'
                                }
                              </span>
                            )}

                            {column.id === 'timeZone' && (
                              contact.region || "-"
                            )}

                            {column.id === "source" && (
                                <span className={`
                                  inline-flex px-2 py-1 rounded-sm text-xs font-medium
                                  ${contact.source === 'manual' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' :
                                    contact.source === 'file Upload' ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400' :
                                    contact.source === 'CRM' ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400' :
                                    ' text-gray-700 dark:bg-gray-800 dark:text-gray-400'}
                                `}>
                                  {contact.source || '-'}
                                </span>
                              )}

                              {column.id === 'addedBy' && (
                              <span className="text-sm text-black font-semibold italic dark:text-gray-100">
                                {contact.addedBy || '-'}
                              </span>
                            )}

                          </TableCell>
                        )
                      ))}
                      <TableCell>
                        <div className="flex flex-wrap justify-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleMakeCall(contact)}
                            className="text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700"
                          >
                            <Phone className="h-4 w-4 mr-1" />
                            Call
                          </Button>
                        </div>
                      </TableCell>


                      <TableCell>
                        <div className="flex justify-center">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="outline"
                                size="icon"
                                className="text-gray-600 hover:bg-gray-100"
                              >
                                <MoreVertical className="h-4 w-4 dark:text-gray-200" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                              align="end"
                              className="w-28 p-2 bg-white shadow-lg rounded-md"
                            >
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/history/${encodeURIComponent(
                                    contact.contactName
                                  )}`}
                                  className="flex items-center p-2 rounded-md hover:bg-blue-50 cursor-pointer"
                                >
                                  <History className="h-4 w-4 mr-2 text-[#383D73]" />
                                  <span className="text-[#383D73]">History</span>
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link
                                href={`/contacts/edit/${encodeURIComponent(
                                  contact.contactName
                                )}/${encodeURIComponent(
                                  contact._id
                                )}`}
                                  className="flex items-center p-2 w-full text-left rounded-md hover:bg-blue-50 cursor-pointer"
                                >
                                  <Edit className="h-4 w-4 mr-2 text-blue-600" />
                                  <span className="text-blue-600">Edit</span>
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <button
                                  onClick={() => handleDeleteContact(contact._id)}
                                  className="flex items-center p-2 w-full text-left rounded-md hover:bg-red-50 cursor-pointer"
                                >
                                  <Trash2 className="h-4 w-4 mr-2 text-red-600" />
                                  <span className="text-red-600">Delete</span>
                                </button>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>

                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={getVisibleColumnsCount()} className="h-80 text-center p-0">
                      <div className="flex flex-col items-center justify-center h-full">
                        <p className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
                        {searchTerm ? 'No contacts found' : 'No contacts yet'}
                        </p>
                        <p className="text-sm text-muted-foreground max-w-md text-center">
                        {searchTerm
                        ? `No contacts match "${searchTerm}"`
                        : 'Contacts are created, or you can create them manually'
                      }
                        </p>
                        {!searchTerm && (
                        <Link href="/contacts/create">
                          <Button variant="outline" className="mt-4">
                            <Plus className="h-4 w-4 mr-2" />
                            Create your first contact
                          </Button>
                        </Link>
                      )}
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              {hasMore && (
              <TableRow ref={loadingRef} className="h-20">
                <TableCell colSpan={7}>
                  <div className="flex items-center justify-center py-4">
                    {isLoadingMore ? (
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    ) : (
                      <div className="h-8" />
                    )}
                  </div>
                </TableCell>
              </TableRow>
                    )}
              </TableBody>
            </Table>
            </div>
          </div>
      </div>

    </div>


        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                contact from your database.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={confirmDeleteContact}
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Multi-Delete Confirmation Dialog */}
        <AlertDialog open={multiDeleteDialogOpen} onOpenChange={setMultiDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Multiple Contacts</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete {selectedContacts.length} contact{selectedContacts.length !== 1 ? 's' : ''}?
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={confirmMultipleDelete}
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Deleting...
                  </div>
                ) : (
                  <>Delete {selectedContacts.length} Contact{selectedContacts.length !== 1 ? 's' : ''}</>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Schedule Success Dialog */}
        <Dialog
          open={scheduleSuccessOpen}
          onOpenChange={setScheduleSuccessOpen}
        >
          <DialogContent className="sm:max-w-[425px]">
            <div className="flex flex-col items-center pt-6">
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <DialogTitle className="text-xl font-semibold text-center mb-2">
                Call Scheduled Successfully!
              </DialogTitle>
              <DialogDescription className="text-center mb-6">
                Your call has been scheduled with the following details:
              </DialogDescription>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 space-y-3 mb-6">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div className="flex flex-col">
                  <span className="text-sm">
                    {new Date(
                      successScheduleDetails?.scheduledTime || ""
                    ).toLocaleString()}
                  </span>
                  <span className="text-xs text-gray-500">
                    Timezone: {successScheduleDetails?.region || ""}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-sm">
                  Agent: {successScheduleDetails?.agentName}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-gray-500" />
                <span className="text-sm">
                  {successScheduleDetails?.contacts.length === 1
                    ? successScheduleDetails.contacts[0].Name
                    : `${successScheduleDetails?.contacts.length} contacts`}
                </span>
              </div>
            </div>

            <DialogFooter className="flex justify-center">
              <Button
                className="w-full flex items-center justify-center gap-2 bg-[#383D73] hover:bg-[#383D73]/90"
                onClick={() => {
                  setScheduleSuccessOpen(false);
                  window.location.href = "/schedule";
                }}
              >
                View in Schedule
                <ArrowRight className="h-4 w-4" />
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Column Visibility Dialog */}
      <Dialog open={columnsDialogOpen} onOpenChange={setColumnsDialogOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Table Columns</DialogTitle>
          <DialogDescription>
            Drag to reorder columns. Toggle visibility with the eye icon.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={columnOrder.map(item => item.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-2 overflow-auto max-h-100">
                {columnOrder.map((column) => (
                  <SortableItem
                    key={column.id}
                    id={column.id}
                    label={column.label}
                    visible={visibleColumns[column.id]}
                    onToggle={() => toggleColumnVisibility(column.id)}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>

         <DialogFooter className="flex justify-between">
      <Button
        variant="outline"
        onClick={() => setColumnOrder(defaultColumnOrder)}
        className="flex items-center gap-2"
      >
        <RotateCcw className="h-4 w-4" />
        Reset Order
      </Button>
      <Button onClick={() => setColumnsDialogOpen(false)}>Done</Button>
    </DialogFooter>
      </DialogContent>
      </Dialog>

        {/* import Success Dialog */}
      <Dialog open={importSuccessOpen} onOpenChange={setImportSuccessOpen}>
  <DialogContent className="sm:max-w-[800px]">
    <div className="flex flex-col items-center pt-6">
      <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center mb-4">
        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-500" />
      </div>
      <DialogTitle className="text-xl font-semibold text-center mb-2">
        Files Imported Successfully!
      </DialogTitle>
      <DialogDescription className="text-center mb-6">
        {importResults?.totalFiles} {importResults?.totalFiles === 1 ? 'file' : 'files'} processed with the following results:
      </DialogDescription>
    </div>

    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-4 mb-6">
      {/* Files Summary */}
      <div className="border-b dark:border-gray-700 pb-3">
        <h3 className="font-medium mb-2">Imported Files:</h3>
        <div className="flex flex-wrap gap-2">
          {importResults?.fileResults?.map((result, index) => (
            <div
              key={index}
              className="flex items-center gap-2 bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full border dark:border-gray-700"
            >
              {result.fileName.endsWith('.csv') ? (
                <FileText className="h-4 w-4 text-green-600" />
              ) : (
                <FileSpreadsheet className="h-4 w-4 text-emerald-600" />
              )}
              <span className="text-sm">{result.fileName}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Overall Summary */}
      <div className="border-b dark:border-gray-700 pb-3">
        <h3 className="font-medium mb-2">Results Summary:</h3>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Total Contacts:</p>
            <p className="font-medium">{importResults?.totalProcessed}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Successful:</p>
            <p className="font-medium text-green-600 dark:text-green-500">
              {importResults?.successCount}
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Errors:</p>
            <p className="font-medium text-red-600 dark:text-red-500">
              {importResults?.errorCount}
            </p>
          </div>
        </div>
      </div>

      {/* Consolidated Contacts Table */}
      <div className="overflow-auto max-h-[170px]">
        <h3 className="font-medium mb-2">Imported Contacts:</h3>
        <div className="border dark:border-gray-700 rounded-lg overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-gray-100 dark:bg-gray-800/90">
              <tr>
                <th className="px-4 py-2 text-left font-medium">Name</th>
                <th className="px-4 py-2 text-left font-medium">Phone Number</th>
                {/* <th className="px-4 py-2 text-left font-medium">Region</th> */}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {importResults?.fileResults?.flatMap(result =>
                result.successfulContacts?.map((contact, contactIndex) => (
                  <tr
                    key={`${result.fileName}-${contactIndex}`}
                    className="bg-white dark:bg-gray-800/50"
                  >
                    <td className="px-4 py-2">{contact.contactName}</td>
                    <td className="px-4 py-2">{contact.phoneNumber}</td>
                    {/* <td className="px-4 py-2">{contact.region || '-'}</td> */}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        {/* Add pagination or scroll if too many contacts */}
        {(importResults?.totalProcessed || 0) > 50 && (
          <p className="text-sm text-muted-foreground mt-2 text-center">
            Showing first 50 contacts of {importResults?.totalProcessed}
          </p>
        )}
      </div>
    </div>

    <DialogFooter>
      <Button
        className="w-full"
        onClick={() => setImportSuccessOpen(false)}
      >
        Done
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>

        {/* Call Dialog */}
        <Dialog
          open={callDialogOpen}
          onOpenChange={(open) => {
            setCallDialogOpen(open);
            if (!open) {
              setCallError(null);
            }
          }}
        >
          <DialogContent className="sm:max-w-[600px] p-0 overflow-hidden rounded-xl border-0 shadow-xl dark:bg-gray-900">
            <div className="bg-gray-950 p-4">
              <DialogHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <DialogTitle className="text-white text-xl font-medium">
                    {activeContact ? "Start Call" : "Group Call"}
                  </DialogTitle>
                  <DialogClose asChild>
                    <button className="absolute top-4 right-4 text-white hover:bg-blue-900 rounded-full p-2">
                      <XIcon className="h-5 w-5" />
                    </button>
                  </DialogClose>
                </div>
                <DialogDescription className="text-white/90 text-lg">
                  {activeContact
                    ? `Connect with ${activeContact.contactName}`
                    : `Connect with ${selectedContacts.length} contacts`}
                </DialogDescription>
              </DialogHeader>
            </div>

            <div className="p-5">
              {activeContact ? (
                <div className="flex items-center gap-4 mb-6 bg-green-50 p-4 rounded-lg dark:bg-green-900/20">
                  <Avatar className="h-16 w-16 border-2 border-green-500">
                    <AvatarFallback className="bg-green-100 dark:bg-green-900 dark:text-green-400 text-green-600 text-lg">
                      {activeContact.contactName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-lg font-semibold dark:text-white">
                      {activeContact.contactName}
                    </p>
                    <p className="text-sm text-muted-foreground ">
                      {activeContact.phoneNumber}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="mb-6 bg-green-50 p-4 rounded-lg dark:bg-green-900/20">
                  <p className="font-medium mb-2 text-green-800 dark:text-green-400">
                    Selected contacts:
                  </p>
                  <div className="max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-green-600 scrollbar-track-green-300">
                    <ul className="space-y-2">
                      {selectedContacts.map((c) => (
                        <li
                          key={c._id}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center">
                            <Avatar className="h-8 w-8 mr-2">
                              <AvatarFallback className="bg-green-100 text-green-600 text-xs">
                                {c.contactName.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium">
                                {c.contactName}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {c.phoneNumber}
                              </p>
                            </div>
                          </div>

                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Mode Toggle: Call Now vs. Schedule Call */}
              <div className="flex gap-4 mb-4">
                <Button
                  variant={callMode === "now" ? "default" : "outline"}
                  onClick={() => setCallMode("now")}
                  className="flex-1 dark:border-gray-700"
                >
                  Call Now
                </Button>
                <Button
                  variant={callMode === "schedule" ? "default" : "outline"}
                  onClick={() => setCallMode("schedule")}
                  className="flex-1 dark:border-gray-700"
                >
                  Schedule Call
                </Button>
              </div>

              {/* If scheduling, show a native datetime-local input */}
              {callMode === "schedule" && (
                <div className="mb-4">
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Select Date & Time:
                  </label>
                  <div className="relative flex items-center">
                    <input
                      type="datetime-local"
                      value={scheduledDateTime}
                      onChange={(e) => setScheduledDateTime(e.target.value)}
                      min={getCurrentDateTimeLocal()}
                      className="border dark:border-gray-700 dark:bg-gray-800 dark:text-white p-2 rounded w-full"
                      ref={inputRef} // Attach a ref to the input
                    />
                    <div className="absolute right-0 top-0 h-full flex items-center gap-2 pr-2">
                      {!isCalendarOpen ? (
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8"
                          onClick={() => {
                            inputRef.current?.showPicker(); // Open the calendar
                            setIsCalendarOpen(true);
                          }}
                        >
                          Set Date
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white h-8"
                          onClick={() => {
                            inputRef.current?.blur(); // Close the calendar
                            setIsCalendarOpen(false);
                          }}
                        >
                          Save
                        </Button>
                      )}
                    </div>
                  </div>
                  {scheduledDateTime && !isCalendarOpen && (
                    <p className="text-sm text-green-600 mt-1">
                      Scheduled: {new Date(scheduledDateTime).toLocaleString()}
                    </p>
                  )}
                </div>
              )}
              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2 dark:text-gray-200">
                  Select an agent for this call:
                </h3>
                <div
                  className="max-h-[230px] overflow-y-auto pr-2"
                  style={{ scrollbarWidth: "thin" }}
                >
                  <div className="grid grid-cols-2 gap-3">
                    {agents.map((agent) => (
                      <div
                        key={agent.id}
                        onClick={() => setSelectedAgent(agent.id)}
                        className={`border rounded-lg p-3 flex items-center gap-2 cursor-pointer transition-all ${
                          selectedAgent === agent.id
                            ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                            : "border-gray-200 dark:border-gray-700"
                        } hover:border-green-300 dark:hover:border-green-600`}
                      >
                        <Avatar className="h-10 w-10 flex-shrink-0">
                        <AvatarImage
                          src={agent.avatar}
                          alt={agent.name}
                        />
                          <AvatarFallback className="bg-gray-100">
                            {agent.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {agent.name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {agent.role}
                          </p>
                        </div>
                        {selectedAgent === agent.id && (
                          <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {callError && (
              <div className="px-5 mb-4">
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-2 rounded-md text-sm">
                  {callError}
                </div>
              </div>
            )}

            <DialogFooter className="flex flex-col sm:flex-row gap-2 p-4 bg-gray-50 dark:bg-gray-800/50 border-t dark:border-gray-700">
              <DialogClose asChild>
                <Button variant="outline" className="w-full sm:w-auto">
                  Cancel
                </Button>
              </DialogClose>
              {callMode === "now" ? (
                <Button
                  className="w-full sm:w-auto bg-green-600 hover:bg-green-700"
                  onClick={() => handleStartCall(selectedAgent)}
                  disabled={calling || !hasSufficientCredits}
                >
                  {calling ? (
                    "Connecting..."
                  ) : (
                    <>
                      <Phone className="h-4 w-4 mr-2" /> Start Call
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  className="w-full sm:w-auto bg-green-600 hover:bg-green-700"
                  onClick={() => handleScheduleCall(selectedAgent)}
                  disabled={calling || isCalendarOpen || !hasSufficientCredits}
                >
                  {calling ? "Scheduling..." : "Schedule Call"}
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </FadeIn>
    </>
  );
}


// Add SortableItem component
const SortableItem = ({ id, label, visible, onToggle }: {
  id: string;
  label: string;
  visible: boolean;
  onToggle: () => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center justify-between p-3 bg-card border rounded-lg"
    >
      <div className="flex items-center gap-3">
        <button
          className="cursor-grab text-muted-foreground"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-5 w-5" />
        </button>
        <span className="font-medium">{label}</span>
      </div>
      <Button
        variant="ghost"
        size="icon"
        onClick={onToggle}
        className={visible ? 'text-primary' : 'text-muted-foreground'}
      >
        {visible ? (
          <Eye className="h-5 w-5" />
        ) : (
          <EyeOff className="h-5 w-5" />
        )}
      </Button>
    </div>
  );
};
