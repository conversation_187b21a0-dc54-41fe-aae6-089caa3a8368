import { BillingService } from './billing.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { CreatePaymentIntentDto } from './dto/create-payment-intent.dto';
import { ProcessPaymentDto } from './dto/process-payment.dto';
import { LoggerService } from 'src/logger/logger.service';
import { Request } from 'express';
interface RequestWithUser extends Request {
    user: {
        userId: string;
        email: string;
        name?: string;
    };
}
export declare class BillingController {
    private readonly billingService;
    private readonly loggerService;
    constructor(billingService: BillingService, loggerService: LoggerService);
    createPaymentMethod(req: RequestWithUser, createPaymentMethodDto: CreatePaymentMethodDto): Promise<import("mongoose").Document<unknown, {}, import("./schemas/organization-payment-method.schema").OrganizationPaymentMethodDocument> & import("./schemas/organization-payment-method.schema").OrganizationPaymentMethod & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    getPaymentMethods(req: RequestWithUser): Promise<(import("mongoose").Document<unknown, {}, import("./schemas/organization-payment-method.schema").OrganizationPaymentMethodDocument> & import("./schemas/organization-payment-method.schema").OrganizationPaymentMethod & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    setDefaultPaymentMethod(req: RequestWithUser, id: string): Promise<import("mongoose").Document<unknown, {}, import("./schemas/organization-payment-method.schema").OrganizationPaymentMethodDocument> & import("./schemas/organization-payment-method.schema").OrganizationPaymentMethod & import("mongoose").Document<unknown, any, any> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>;
    removePaymentMethod(req: RequestWithUser, id: string): Promise<{
        success: boolean;
        message: string;
    }>;
    createPaymentIntent(req: RequestWithUser, createPaymentIntentDto: CreatePaymentIntentDto): Promise<{
        clientSecret: string;
        paymentIntentId: string;
        status: import("stripe").Stripe.PaymentIntent.Status;
    }>;
    getTransactionHistory(req: RequestWithUser, page?: number, limit?: number): Promise<{
        transactions: (import("mongoose").Document<unknown, {}, import("./schemas/organization-transaction.schema").OrganizationTransactionDocument> & import("./schemas/organization-transaction.schema").OrganizationTransaction & import("mongoose").Document<unknown, any, any> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        pagination: {
            total: number;
            page: number;
            limit: number;
            pages: number;
        };
    }>;
    processPayment(req: RequestWithUser, processPaymentDto: ProcessPaymentDto): Promise<{
        success: boolean;
        status: import("stripe").Stripe.PaymentIntent.Status;
        transactionId: unknown;
        paymentIntentId: string;
        paymentMethodId: any;
        clientSecret: string;
        savedPaymentMethod: boolean;
    }>;
}
export {};
