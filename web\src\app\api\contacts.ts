/* eslint-disable @typescript-eslint/no-explicit-any */


const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

export type Campaign = {
  _id: string;
  name: string;
};

export type Contact = {
  _id: string;
  customerId?: string;
  contactName: string;
  phoneNumber: string;
  lastCall?: string;
  campaigns?: any[]; 
  campaignNames?: string[]; // For storing campaign names directly
  region?: string;
  updatedAt?: Date;

  // Project details
  projectName?: string;
  unitNumber?: string;

  // Payment details
  totalPayableAmount?: number | string;
  pendingPayableAmount?: number | string;
  dueDate?: string;
  totalInstallments?: number | string;
  paymentType?: string;
  pendingInstallments?: number | string;

  // Last payment info
  lastPaymentDate?: string;
  lastPaymentAmount?: number | string;
  lastPaymentType?: string;
  collectionBucket?: string;
  type?: string;

  // Collections specific fields
  unitPrice?: number | string;
  paidAmtIncluding?: number | string;

  // Event related fields
  eventDate?: string;
  eventLocation?: string;
  eventTime?: string;
  nameOfRegistrant?: string;
  createdAt: Date;
  addedBy?: string;
  source?: string;
  totalContacts?: number;
  filteredContacts?: number;
}

export type NewContact = Omit<Contact, '_id'> & {
  lastCall?: string;
  campaigns?: string[];
};


export type EditContactData = Contact & {
  lastCall?: string;
  campaigns?: string[];
};

/**
 * Fetch all contacts
 */
export async function fetchContacts(): Promise<Contact[]> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      return [];
    }

    const response = await fetch(`${API_BASE_URL}/api/contacts`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch contacts");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching contacts:", error);
    throw error;
  }
}


export async function fetchPaginatedContacts(page: number = 1, limit: number = 20, search?: string): Promise<Contact[]> {
  try {
    const token = localStorage.getItem("access_token");
    if (!token) {
      console.error("No access token available");
      return [];
    }
    const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
    const response = await fetch(
      `${API_BASE_URL}/api/contacts/paginated?page=${page}&limit=${limit}${searchParam}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch contacts");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching paginated contacts:", error);
    throw error;
  }
}


export async function fetchCampaign(): Promise<any> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      return [];
    }

    const response = await fetch(`${API_BASE_URL}/api/campaigns`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch contacts");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching contacts:", error);
    throw error;
  }
}

/**
 * Create a new contact
 */
export async function createContact(contact: NewContact): Promise<Contact> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/contacts`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(contact),
    });

    if (!response.ok) {
      throw new Error("Failed to create contact");
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating contact:", error);
    throw error;
  }
}

/**
 * Create a new contact for short calls
 */ 
export async function createContactForShortCall(contactData: {
  contactName: string;
  phoneNumber: string;
  campaigns?: string[];
}): Promise<any> {
  const token = localStorage.getItem("access_token");
  if (!token) throw new Error("No access token available");

  const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/contacts`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(contactData),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || `Failed to create contact: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Update an existing contact
 */
export async function updateContact(
  contactId: string,
  data: Partial<NewContact>
): Promise<Contact> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/contacts/${contactId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update contact");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating contact:", error);
    throw error;
  }
}

/**
 * Delete a contact
 */
export async function deleteContact(contactId: string): Promise<void> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/contacts/${contactId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to delete contact");
    }
  } catch (error) {
    console.error("Error deleting contact:", error);
    throw error;
  }
}

/**
 * Import contacts
 */
export async function importContacts(): Promise<void> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(
      `${API_BASE_URL}/api/contacts/import-contacts`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to import contacts");
    }

    await response.json();
  } catch (error) {
    console.error("Error importing contacts:", error);
    throw error;
  }
}

/**
 * Start a call with contacts
 */
export async function callContacts(
  agentId: string,
  contacts: Array<{ Name: string; MobileNumber: string }>,
  region:string
): Promise<unknown> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const payload = {
      agentId,
      contacts,
      region
    };

    const response = await fetch(`${API_BASE_URL}/api/vapi/call-contacts`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    // Check for payment required error (402)
    if (response.status === 402) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Insufficient credits to make this call. Please add funds to your account.");
    }

    if (!response.ok) {
      throw new Error("Failed to start call");
    }

    return await response.json();
  } catch (error) {
    console.error("Error calling contact(s):", error);
    throw error;
  }
}
export async function scheduleCall(
  agentId: string,
  contacts: Array<{ Name: string; MobileNumber: string }>,
  scheduledTime: string,
  region: string
): Promise<unknown> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem("access_token");

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const payload = {
      agentId,
      contacts,
      scheduledTime,
      region,
    };

    const response = await fetch(`${API_BASE_URL}/api/scheduled-call`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    // Check for payment required error (402)
    if (response.status === 402) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Insufficient credits to schedule this call. Please add funds to your account.");
    }

    if (!response.ok) {
      throw new Error("Failed to schedule call");
    }

    return await response.json();
  } catch (error) {
    console.error("Error scheduling call:", error);
    throw error;
  }
}
