"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const agent_service_1 = require("../agent/agent.service");
const form_data_1 = __importDefault(require("form-data"));
let AiService = class AiService {
    constructor(httpService, agentsService) {
        this.httpService = httpService;
        this.agentsService = agentsService;
    }
    async *streamChatWithAgentGenerator(agentId, userMessage, conversationHistory = []) {
        const agent = await this.agentsService.findById(agentId);
        if (!agent) {
            throw new common_1.NotFoundException('Agent not found');
        }
        const limitedHistory = conversationHistory.slice(-3);
        const language = agent.voiceSettings?.language || 'English';
        const systemMessage = agent.prompt ||
            `You are ${agent.role}. Please respond in ${language}.`;
        const messages = [
            { role: 'system', content: systemMessage },
            ...limitedHistory,
            { role: 'user', content: userMessage },
        ];
        const streamingSupportedModels = ['gpt-3.5-turbo', 'gpt-4', 'gpt-4o-mini'];
        const supportsStreaming = streamingSupportedModels.includes(agent.languageModel.model);
        const payload = {
            model: agent.languageModel.model,
            messages,
            temperature: agent.languageModel.temperature,
            stream: supportsStreaming,
        };
        const response$ = this.httpService.post('https://api.openai.com/v1/chat/completions', payload, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            },
            responseType: supportsStreaming ? 'stream' : 'json',
        });
        if (supportsStreaming) {
            const response = await (0, rxjs_1.firstValueFrom)(response$);
            const stream = response.data;
            let buffer = '';
            for await (const chunk of stream) {
                buffer += chunk.toString('utf8');
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const dataStr = line.slice(6).trim();
                        if (dataStr === '[DONE]') {
                            return;
                        }
                        try {
                            const parsed = JSON.parse(dataStr);
                            const token = parsed.choices[0].delta?.content;
                            if (token) {
                                yield token;
                            }
                        }
                        catch (e) {
                            console.error('Stream parsing error:', e);
                        }
                    }
                }
            }
        }
        else {
            const response = await (0, rxjs_1.firstValueFrom)(response$);
            yield response.data.choices[0].message.content;
        }
    }
    async getChatResponse(agentId, userMessage, conversationHistory = []) {
        let fullResponse = '';
        for await (const token of this.streamChatWithAgentGenerator(agentId, userMessage, conversationHistory)) {
            fullResponse += token;
        }
        return fullResponse;
    }
    async transcribeAudio(provider, audioBuffer, options) {
        if (provider === 'whisper') {
            const form = new form_data_1.default();
            form.append('file', audioBuffer, {
                filename: 'audio.webm',
                contentType: 'audio/webm',
            });
            form.append('model', 'whisper-1');
            if (options?.language) {
                form.append('language', options.language);
            }
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post('https://api.openai.com/v1/audio/transcriptions', form, {
                headers: {
                    ...form.getHeaders(),
                    Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
                },
            }));
            return response.data.text;
        }
        throw new Error('Unsupported STT provider');
    }
    async textToSpeech(provider, text, voiceSettings = {}) {
        if (provider !== 'elevenlabs') {
            throw new Error(`Unsupported TTS provider: ${provider}`);
        }
        const voiceId = voiceSettings.voiceId || process.env.ELEVENLABS_DEFAULT_VOICE_ID;
        if (!voiceId) {
            throw new Error('A valid voice id must be provided for ElevenLabs TTS.');
        }
        const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`;
        const payload = {
            text,
            model_id: voiceSettings.ttsModel || 'eleven_monolingual_v1',
            voice_settings: {
                stability: voiceSettings.stability ?? 0.5,
                similarity_boost: voiceSettings.voiceSimilarity ?? 0.75,
                speech_speed: voiceSettings.speechSpeed ?? 1,
                responsiveness: voiceSettings.responsiveness ?? 0.8,
                emotions: voiceSettings.emotions || 'balanced',
                interruption_sensitivity: voiceSettings.interruptionSensitivity ?? 0.5,
                background_noise: voiceSettings.backgroundNoise || 'office',
            },
        };
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(url, payload, {
                headers: {
                    'Content-Type': 'application/json',
                    'xi-api-key': process.env.ELEVENLABS_API_KEY,
                    Accept: 'audio/mpeg',
                },
                responseType: 'arraybuffer',
                timeout: 10000,
            }));
            return Buffer.from(response.data);
        }
        catch (error) {
            console.error('Error generating speech with ElevenLabs:', error);
            throw new Error('Failed to generate speech');
        }
    }
};
exports.AiService = AiService;
exports.AiService = AiService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        agent_service_1.AgentService])
], AiService);
//# sourceMappingURL=ai.service.js.map