import { Schema } from 'mongoose';
export declare const PhoneNumberSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    strict: false;
    timestamps: true;
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    status: string;
    hooks: import("mongoose").Types.DocumentArray<{
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }> & {
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }>;
    number?: string;
    createdAt?: NativeDate;
    updatedAt?: NativeDate;
    id?: string;
    name?: string;
    server?: {
        headers?: any;
        secret?: string;
        url?: string;
        timeoutSeconds?: number;
        backoffPlan?: {
            type?: any;
            maxRetries?: number;
            baseDelaySeconds?: number;
        };
    };
    provider?: string;
    orgId?: string;
    credentialId?: string;
    assistantId?: string;
    numberE164CheckEnabled?: boolean;
    squadId?: string;
    workflowId?: string;
    fallbackDestination?: {
        number?: string;
        type?: string;
        description?: string;
        message?: string;
        numberE164CheckEnabled?: boolean;
        callerId?: string;
        extension?: string;
        transferPlan?: {
            mode?: string;
        };
    };
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    status: string;
    hooks: import("mongoose").Types.DocumentArray<{
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }> & {
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }>;
    number?: string;
    createdAt?: NativeDate;
    updatedAt?: NativeDate;
    id?: string;
    name?: string;
    server?: {
        headers?: any;
        secret?: string;
        url?: string;
        timeoutSeconds?: number;
        backoffPlan?: {
            type?: any;
            maxRetries?: number;
            baseDelaySeconds?: number;
        };
    };
    provider?: string;
    orgId?: string;
    credentialId?: string;
    assistantId?: string;
    numberE164CheckEnabled?: boolean;
    squadId?: string;
    workflowId?: string;
    fallbackDestination?: {
        number?: string;
        type?: string;
        description?: string;
        message?: string;
        numberE164CheckEnabled?: boolean;
        callerId?: string;
        extension?: string;
        transferPlan?: {
            mode?: string;
        };
    };
}>> & import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    status: string;
    hooks: import("mongoose").Types.DocumentArray<{
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }> & {
        do: import("mongoose").Types.DocumentArray<{
            type?: string;
        }, import("mongoose").Types.Subdocument<import("mongoose").Types.ObjectId, any, {
            type?: string;
        }> & {
            type?: string;
        }>;
        on?: string;
    }>;
    number?: string;
    createdAt?: NativeDate;
    updatedAt?: NativeDate;
    id?: string;
    name?: string;
    server?: {
        headers?: any;
        secret?: string;
        url?: string;
        timeoutSeconds?: number;
        backoffPlan?: {
            type?: any;
            maxRetries?: number;
            baseDelaySeconds?: number;
        };
    };
    provider?: string;
    orgId?: string;
    credentialId?: string;
    assistantId?: string;
    numberE164CheckEnabled?: boolean;
    squadId?: string;
    workflowId?: string;
    fallbackDestination?: {
        number?: string;
        type?: string;
        description?: string;
        message?: string;
        numberE164CheckEnabled?: boolean;
        callerId?: string;
        extension?: string;
        transferPlan?: {
            mode?: string;
        };
    };
}> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
