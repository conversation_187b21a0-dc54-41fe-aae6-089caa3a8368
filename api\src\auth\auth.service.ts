import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import axios from 'axios';
import { LoginDto } from './dto/login.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}
  private readonly secretKey = '6LfyPfgqAAAAAJNHXbDXPHwiNGGpqeF7hJDuh6ZO';

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findOne(email);
    if (user && (await bcrypt.compare(password, user.password))) {
      if (!user.isApproved) {
        return { message: 'Your account is pending approval' };
      }

      const { password, ...result } = user.toObject();
      return result;
    }
    return null;
  }

  async login(loginDto: LoginDto) {
    const { email, password, recaptchaToken } = loginDto;

    // Verify reCAPTCHA token
    // const isHuman = await this.verifyToken(recaptchaToken);
    
    // if (!isHuman) {
    //   throw new UnauthorizedException('Failed reCAPTCHA verification');
    // }

    const user = await this.validateUser(email, password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload = { fullName: user.fullName, email: user.email, sub: user._id, role: user.role };
    const access_token = this.jwtService.sign(payload, { expiresIn: '60m' });
    const refresh_token = this.jwtService.sign(payload, { expiresIn: '30d' });
    return {
      access_token,
      refresh_token,
    };
  }

  async refreshToken(refreshToken: string) {
    try {
      const decoded = this.jwtService.verify(refreshToken);
      const user = await this.usersService.findOne(decoded.email);
      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }
      const payload = { fullName: user.fullName, email: user.email, sub: user._id, role: user.role };
      const newAccessToken = this.jwtService.sign(payload, { expiresIn: '60m' });
      return { access_token: newAccessToken };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async verifyToken(token: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `https://www.google.com/recaptcha/api/siteverify?secret=${this.secretKey}&response=${token}`
      );
      console.log('reCAPTCHA response:', response.data); // Add this line to log the response
      return response.data.success;
    } catch (error) {
      console.error('Error verifying reCAPTCHA:', error); // Add this line to log any errors
      return false;
    }
  }
}