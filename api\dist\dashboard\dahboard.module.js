"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const dashboard_controller_1 = require("./dashboard.controller");
const dashboard_service_1 = require("./dashboard.service");
const history_schema_1 = require("../history/schemas/history.schema");
const agent_schema_1 = require("../agent/schemas/agent.schema");
const campaign_schema_1 = require("../campaign/schemas/campaign.schema");
const scheduled_call_schema_1 = require("../scheduled-call/schemas/scheduled-call.schema");
const contact_schema_1 = require("../contacts/schemas/contact.schema");
const dashboard_schema_1 = require("./schemas/dashboard.schema");
const schedule_1 = require("@nestjs/schedule");
let DashboardModule = class DashboardModule {
};
exports.DashboardModule = DashboardModule;
exports.DashboardModule = DashboardModule = __decorate([
    (0, common_1.Module)({
        imports: [
            schedule_1.ScheduleModule.forRoot(),
            mongoose_1.MongooseModule.forFeature([
                { name: 'History', schema: history_schema_1.HistorySchema },
                { name: 'Agent', schema: agent_schema_1.AgentSchema },
                { name: 'Campaign', schema: campaign_schema_1.CampaignSchema },
                { name: 'ScheduledCall', schema: scheduled_call_schema_1.ScheduledCallSchema },
                { name: 'Contact', schema: contact_schema_1.ContactSchema },
                { name: 'DashboardStats', schema: dashboard_schema_1.DashboardStatsSchema },
            ]),
        ],
        controllers: [dashboard_controller_1.DashboardController],
        providers: [dashboard_service_1.DashboardService],
        exports: [dashboard_service_1.DashboardService, mongoose_1.MongooseModule],
    })
], DashboardModule);
//# sourceMappingURL=dahboard.module.js.map