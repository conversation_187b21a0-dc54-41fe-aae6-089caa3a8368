import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, IsOptional, IsMongoId } from 'class-validator';

export class RegisterUserDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  password: string;

  @ApiProperty({ example: 'John Doe' })
  @IsString()
  fullName: string;

  @ApiProperty({ example: '60d0fe4f5311236168a109ca', required: false })
  @IsMongoId()
  @IsOptional()
  organizationId?: string;
}
