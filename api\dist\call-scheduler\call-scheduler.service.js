"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CallSchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallSchedulerService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const scheduled_call_service_1 = require("../scheduled-call/scheduled-call.service");
const vapi_service_1 = require("../vapi/vapi.service");
const users_service_1 = require("../users/users.service");
let CallSchedulerService = CallSchedulerService_1 = class CallSchedulerService {
    constructor(scheduledCallService, vapiService, usersService, contactModel, campaignModel) {
        this.scheduledCallService = scheduledCallService;
        this.vapiService = vapiService;
        this.usersService = usersService;
        this.contactModel = contactModel;
        this.campaignModel = campaignModel;
        this.logger = new common_1.Logger(CallSchedulerService_1.name);
        this.recentlyProcessedContacts = new Map();
        this.isProcessingCalls = false;
    }
    async checkCampaignStatus(contactName, phoneNumber) {
        try {
            const contact = await this.contactModel
                .findOne({
                contactName: contactName,
                phoneNumber: phoneNumber,
            })
                .exec();
            if (!contact || !contact.campaigns || contact.campaigns.length === 0) {
                this.logger.warn(`No campaigns found for contact ${contactName}`);
                return { isActive: true };
            }
            const campaignId = contact.campaigns[0];
            const campaign = await this.campaignModel.findById(campaignId).exec();
            if (!campaign) {
                this.logger.warn(`Campaign ${campaignId} not found for contact ${contactName}`);
                return { isActive: true };
            }
            const isActive = campaign.status === "active";
            if (!isActive) {
                this.logger.log(`Campaign ${campaign.name} (${campaignId}) is ${campaign.status}, not initiating call for ${contactName}`);
            }
            return {
                isActive,
                campaignName: campaign.name,
                campaignId: campaign._id.toString(),
            };
        }
        catch (error) {
            this.logger.error(`Error checking campaign status for contact ${contactName}:`, error.message);
            return { isActive: true };
        }
    }
    cleanupRecentlyProcessedContacts() {
        const now = Date.now();
        const ONE_MINUTES = 30 * 1000;
        for (const [key, timestamp] of this.recentlyProcessedContacts.entries()) {
            if (now - timestamp > ONE_MINUTES) {
                this.recentlyProcessedContacts.delete(key);
            }
        }
    }
    async recoverStuckCalls() {
        this.logger.log("Checking for stuck calls in processing status...");
        try {
            const stuckCalls = await this.scheduledCallService.getStuckProcessingCalls(10);
            if (!stuckCalls || stuckCalls.length === 0) {
                this.logger.log("No stuck calls found");
                return;
            }
            this.logger.log(`Found ${stuckCalls.length} calls stuck in processing status`);
            for (const call of stuckCalls) {
                const currentRetryCount = call.retryCount || 0;
                if (currentRetryCount >= 3) {
                    await this.scheduledCallService.updateScheduledCall(call._id, {
                        status: "failed",
                        retryCount: currentRetryCount + 1
                    });
                    this.logger.log(`Marked call ${call._id} as failed after ${currentRetryCount + 1} attempts`);
                }
                else {
                    await this.scheduledCallService.updateScheduledCall(call._id, {
                        status: "pending",
                        retryCount: currentRetryCount + 1
                    });
                    this.logger.log(`Reset stuck call ${call._id} to pending (attempt ${currentRetryCount + 1})`);
                }
            }
        }
        catch (error) {
            this.logger.error("Error recovering stuck calls:", error.message);
        }
    }
    async handleScheduledCalls() {
        if (this.isProcessingCalls) {
            this.logger.log("Previous call processing job still running, skipping this execution");
            return;
        }
        this.isProcessingCalls = true;
        console.log("Checking for scheduled calls due for execution...");
        const now = new Date();
        try {
            this.cleanupRecentlyProcessedContacts();
            const scheduledCalls = await this.scheduledCallService.getScheduledCallsByStatusAndTime("pending", now);
            if (!scheduledCalls || scheduledCalls.length === 0) {
                this.isProcessingCalls = false;
                return;
            }
            this.logger.log(`Found ${scheduledCalls.length} scheduled calls due for execution`);
            const BATCH_SIZE = 5;
            const BATCH_DELAY_MS = 5000;
            const uniqueCallsMap = new Map();
            for (const call of scheduledCalls) {
                if (call.contacts && call.contacts.length > 0) {
                    const contact = call.contacts[0];
                    const contactKey = `${contact.Name}:${contact.MobileNumber}`;
                    if (this.recentlyProcessedContacts.has(contactKey)) {
                        this.logger.log(`Skipping duplicate call for ${contact.Name} - already processed recently`);
                        await this.scheduledCallService.updateScheduledCall(call._id, { status: "executed" });
                        continue;
                    }
                    if (!uniqueCallsMap.has(contactKey) ||
                        new Date(call.scheduledTime) < new Date(uniqueCallsMap.get(contactKey).scheduledTime)) {
                        uniqueCallsMap.set(contactKey, call);
                    }
                }
                else {
                    uniqueCallsMap.set(call._id.toString(), call);
                }
            }
            const uniqueScheduledCalls = Array.from(uniqueCallsMap.values());
            this.logger.log(`After deduplication: ${uniqueScheduledCalls.length} unique calls to process`);
            const batches = [];
            for (let i = 0; i < uniqueScheduledCalls.length; i += BATCH_SIZE) {
                batches.push(uniqueScheduledCalls.slice(i, i + BATCH_SIZE));
            }
            this.logger.log(`Processing calls in ${batches.length} batches of up to ${BATCH_SIZE} calls each`);
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                this.logger.log(`Processing batch ${batchIndex + 1} of ${batches.length} with ${batch.length} calls`);
                for (const scheduledCall of batch) {
                    try {
                        if (scheduledCall.contacts && scheduledCall.contacts.length > 0) {
                            const firstContact = scheduledCall.contacts[0];
                            const contactKey = `${firstContact.Name}:${firstContact.MobileNumber}`;
                            if (this.recentlyProcessedContacts.has(contactKey)) {
                                this.logger.log(`Skipping call for ${firstContact.Name} - processed by another batch`);
                                await this.scheduledCallService.updateScheduledCall(scheduledCall._id, { status: "executed" });
                                continue;
                            }
                            const { isActive, campaignName } = await this.checkCampaignStatus(firstContact.Name, firstContact.MobileNumber);
                            if (!isActive) {
                                await this.scheduledCallService.updateScheduledCall(scheduledCall._id, { status: "cancelled" });
                                this.logger.log(`Scheduled call for ${firstContact.Name} cancelled because campaign ${campaignName} is not active`);
                                continue;
                            }
                            const hasSufficientCredits = await this.usersService.hasSufficientCredits('system', 1);
                            if (!hasSufficientCredits) {
                                await this.scheduledCallService.updateScheduledCall(scheduledCall._id, { status: "failed" });
                                this.logger.error(`Scheduled call for ${firstContact.Name} failed due to insufficient credits`);
                                continue;
                            }
                            this.recentlyProcessedContacts.set(contactKey, Date.now());
                            this.logger.log(`Initiating call for ${firstContact.Name} (${contactKey})`);
                        }
                        await this.scheduledCallService.updateScheduledCall(scheduledCall._id, {
                            status: "processing",
                            lastProcessedAt: new Date()
                        });
                        try {
                            await this.vapiService.callContacts(scheduledCall.contacts, scheduledCall.agentId, scheduledCall.region);
                            await this.usersService.deductCredits('system', 1);
                            await this.scheduledCallService.updateScheduledCall(scheduledCall._id, { status: "executed" });
                        }
                        catch (error) {
                            await this.scheduledCallService.updateScheduledCall(scheduledCall._id, { status: "failed" });
                            throw error;
                        }
                        if (scheduledCall.contacts && scheduledCall.contacts.length > 0) {
                            this.logger.log(`Successfully executed scheduled call for ${scheduledCall.contacts[0].Name}`);
                        }
                        else {
                            this.logger.log(`Successfully executed scheduled call for agent ${scheduledCall.agentId}`);
                        }
                    }
                    catch (error) {
                        await this.scheduledCallService.updateScheduledCall(scheduledCall._id, { status: "failed" });
                        if (scheduledCall.contacts && scheduledCall.contacts.length > 0) {
                            this.logger.error(`Failed to initiate call for ${scheduledCall.contacts[0].Name}`, error.message);
                        }
                        else {
                            this.logger.error(`Failed to initiate call for agent ${scheduledCall.agentId}`, error.message);
                        }
                    }
                }
                if (batchIndex < batches.length - 1) {
                    this.logger.log(`Waiting ${BATCH_DELAY_MS}ms before processing next batch`);
                    await new Promise(resolve => setTimeout(resolve, BATCH_DELAY_MS));
                }
            }
        }
        catch (error) {
            this.logger.error("Error checking scheduled calls:", error.message);
        }
        finally {
            this.isProcessingCalls = false;
        }
    }
};
exports.CallSchedulerService = CallSchedulerService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_10_MINUTES),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CallSchedulerService.prototype, "recoverStuckCalls", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CallSchedulerService.prototype, "handleScheduledCalls", null);
exports.CallSchedulerService = CallSchedulerService = CallSchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, mongoose_1.InjectModel)("Contact")),
    __param(4, (0, mongoose_1.InjectModel)("Campaign")),
    __metadata("design:paramtypes", [scheduled_call_service_1.ScheduledCallService,
        vapi_service_1.VapiService,
        users_service_1.UsersService,
        mongoose_2.Model,
        mongoose_2.Model])
], CallSchedulerService);
//# sourceMappingURL=call-scheduler.service.js.map