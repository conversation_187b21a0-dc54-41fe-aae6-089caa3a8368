"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Check, Globe } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { useRouter } from "next/router";

// Match your existing language files
const languages = [
  { code: "en", name: "English" },
  { code: "fr", name: "Français" },
  { code: "ar", name: "العربية" }
];

export function LanguageSwitcher() {
    const router = useRouter();
    const { locale, pathname, query, asPath } = router;
    
    const changeLanguage = (newLocale: string) => {
      router.push({ pathname, query }, asPath, { locale: newLocale });
    };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="h-9 w-9 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <Globe className="h-5 w-5 text-gray-700 dark:text-gray-300" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
      {languages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onClick={() => changeLanguage(lang.code)}
            className="flex items-center justify-between cursor-pointer"
          >
            <span>{lang.name}</span>
            {locale === lang.code && (
              <Check className="h-4 w-4 ml-2 text-green-500" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}