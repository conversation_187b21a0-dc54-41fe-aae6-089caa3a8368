/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, memo, useMemo } from "react";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { Button } from "@/components/ui/button";
import { Loader2, CreditCard } from "lucide-react";
import { processPayment } from "@/app/api/billing";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface CardPaymentFormProps {
  amount: number;
  currency: string;
  description?: string;
  onSuccess: () => void;
  onError: (message: string) => void;
  showSaveOption?: boolean;
}

const CardPaymentForm = ({
  amount,
  currency,
  description,
  onSuccess,
  onError,
  showSaveOption = true,
}: CardPaymentFormProps) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [saveCard, setSaveCard] = useState(false);
  const [setAsDefault, setSetAsDefault] = useState(true);

  // Memoize the formatted amount to prevent unnecessary re-renders
  const formattedAmount = useMemo(() => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount / 100);
  }, [amount, currency]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      onError("Stripe has not loaded yet. Please try again.");
      return;
    }

    setIsProcessing(true);

    try {
      // Get the CardElement
      const cardElement = elements.getElement(CardElement);

      if (!cardElement) {
        throw new Error("Card element not found");
      }

      // Create a payment method
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: "card",
        card: cardElement,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!paymentMethod) {
        throw new Error("Failed to create payment method");
      }

      // Process the payment on the server
      const result = await processPayment({
        paymentMethodId: paymentMethod.id,
        amount,
        currency,
        description,
        savePaymentMethod: saveCard,
        setAsDefault: saveCard && setAsDefault,
      });

      // Handle different payment statuses
      if (result.success) {
        // Payment succeeded
        cardElement.clear();

        // Force refresh of user credits from the server
        try {
          // Small delay to ensure the backend has processed the payment
          await new Promise(resolve => setTimeout(resolve, 500));

          // Call onSuccess which should trigger a credit refresh
          onSuccess();
        } catch (refreshError) {
          console.error("Error refreshing credits:", refreshError);
          // Still consider payment successful even if refresh fails
          onSuccess();
        }
      } else if ((result.status === 'requires_action' || result.status === 'requires_confirmation') && result.clientSecret) {
        // Payment requires additional authentication
        const { error, paymentIntent } = await stripe.confirmCardPayment(result.clientSecret);

        if (error) {
          throw new Error(error.message);
        } else if (paymentIntent.status === 'succeeded') {
          // Payment succeeded after authentication
          cardElement.clear();

          // Force refresh of user credits from the server
          try {
            // Small delay to ensure the backend has processed the payment
            await new Promise(resolve => setTimeout(resolve, 500));

            // Call onSuccess which should trigger a credit refresh
            onSuccess();
          } catch (refreshError) {
            console.error("Error refreshing credits:", refreshError);
            // Still consider payment successful even if refresh fails
            onSuccess();
          }
        } else {
          throw new Error(`Payment failed: ${paymentIntent.status}`);
        }
      } else if (result.status === 'requires_action' || result.status === 'requires_confirmation') {
        // Missing client secret
        throw new Error("Authentication required but client secret is missing");
      } else {
        throw new Error(`Payment failed: ${result.status}`);
      }
    } catch (err: any) {
      console.error("Payment error:", err);
      onError(err.message || "Payment processing failed");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm">
            <div className="mb-3 flex items-center justify-between">
              <div className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-primary" />
                <span className="text-sm font-medium">Enter Card Details</span>
              </div>
            </div>
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: "16px",
                    color: "#424770",
                    fontFamily: 'system-ui, -apple-system, "Segoe UI", Roboto, sans-serif',
                    "::placeholder": {
                      color: "#aab7c4",
                    },
                    ":-webkit-autofill": {
                      color: "#424770",
                    },
                  },
                  invalid: {
                    color: "#9e2146",
                    iconColor: "#9e2146",
                  },
                },
              }}
            />
          </div>
          <p className="text-xs text-muted-foreground flex items-center">
            <CreditCard className="h-3 w-3 mr-1" />
            Your card information is securely processed by Stripe.
          </p>
        </div>

        {showSaveOption && (
          <div className="space-y-3 p-3 border rounded-md bg-primary/5 border-primary/10">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="save-card"
                checked={saveCard}
                onCheckedChange={(checked) => setSaveCard(checked === true)}
              />
              <Label htmlFor="save-card" className="flex items-center cursor-pointer">
                <CreditCard className="h-4 w-4 mr-2 text-primary" />
                Save card for future payments
              </Label>
            </div>

            {saveCard && (
              <div className="flex items-center space-x-2 ml-6">
                <Checkbox
                  id="default-card"
                  checked={setAsDefault}
                  onCheckedChange={(checked) => setSetAsDefault(checked === true)}
                />
                <Label htmlFor="default-card" className="cursor-pointer">
                  Set as default payment method
                </Label>
              </div>
            )}
          </div>
        )}
      </div>

      <Button
        type="submit"
        className="w-full"
        disabled={!stripe || isProcessing || amount < 1000}
      >
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          `Pay ${formattedAmount}`
        )}
      </Button>
    </form>
  );
};

// Use React.memo to prevent unnecessary re-renders
export default memo(CardPaymentForm);
