import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsOptional, IsArray, IsNumber, IsDate, IsISO8601 } from "class-validator";

export class ContactDto {
  @ApiProperty({ example: "<PERSON>", description: "Name of the contact" })
  @IsString()
  @IsNotEmpty()
  contactName: string;

  @ApiProperty({ example: "+1234567890", description: "Phone number" })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  @IsOptional()
  customerId?: string;

  @ApiProperty({
    example: "2024-03-26T12:00:00Z",
    required: false,
    description: "Last call date",
  })
  @IsOptional()
  @IsDate()
  lastCall?: Date;

  @ApiProperty({
    example: ["client", "sales"],
    required: false,
    description: "Contact labels",
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  campaigns?: string[];

  @ApiProperty({ example: "Asia/Dubai", description: "Region in timezone format" })
  @IsOptional()
  @IsString()
  region?: string;

  // New fields (all optional)
  @ApiProperty({ example: "Project Alpha", required: false, description: "Project Name" })
  @IsOptional()
  @IsString()
  projectName?: string;

  @ApiProperty({ example: "A-102", required: false, description: "Unit Number" })
  @IsOptional()
  @IsString()
  unitNumber?: string;

  @ApiProperty({ example: 50000, required: false, description: "Total Payable Amount" })
  @IsOptional()
  @IsNumber()
  totalPayableAmount?: number;

  @ApiProperty({ example: 5000, required: false, description: "Pending Payable Amount for the current installment" })
  @IsOptional()
  @IsNumber()
  pendingPayableAmount?: number;

  @ApiProperty({ example: "2024-04-01T00:00:00Z", required: false, description: "Due date of the current installment" })
  @IsOptional()
  @IsDate()
  dueDate?: Date;

  @ApiProperty({ example: 12, required: false, description: "Total Number of Installments" })
  @IsOptional()
  @IsNumber()
  totalInstallments?: number;

  @ApiProperty({ example: "Credit Card", required: false, description: "Preferred Payment Type" })
  @IsOptional()
  @IsString()
  paymentType?: string;

  @ApiProperty({ example: 5, required: false, description: "Number of Pending Installments" })
  @IsOptional()
  @IsNumber()
  pendingInstallments?: number;

  @ApiProperty({ example: "2024-03-15T00:00:00Z", required: false, description: "Last Payment Date" })
  @IsOptional()
  @IsDate()
  lastPaymentDate?: Date;

  @ApiProperty({ example: 2000, required: false, description: "Last Payment Amount" })
  @IsOptional()
  @IsNumber()
  lastPaymentAmount?: number;

  @ApiProperty({ example: "Bank Transfer", required: false, description: "Last Payment Type" })
  @IsOptional()
  @IsString()
  lastPaymentType?: string;

  @ApiProperty({ example: "Bucket A", required: false, description: "Collection Bucket" })
  @IsOptional()
  @IsString()
  collectionBucket?: string;

  @ApiProperty({ example: 1500, required: false, description: "Unit Price" })
  @IsOptional()
  @IsNumber()
  unitPrice?: number;

  @ApiProperty({ example: 2000, required: false, description: "Paid Amount Including" })
  @IsOptional()
  @IsNumber()
  paidAmtIncluding?: number;

  // Event related fields
  @ApiProperty({ example: "2025-04-28T14:30:00Z", required: false, description: "Event date and time" })
  @IsOptional()
  @IsISO8601()
  eventDate?: string;

  @ApiProperty({ example: "Dubai Marina", required: false, description: "Event location" })
  @IsOptional()
  @IsString()
  eventLocation?: string;

  @ApiProperty({ example: "2:30 PM", required: false, description: "Event time" })
  @IsOptional()
  @IsString()
  eventTime?: string;

  @ApiProperty({ example: "John Smith", required: false, description: "Name of the registrant" })
  @IsOptional()
  @IsString()
  nameOfRegistrant?: string;

  @ApiProperty({ example: "john Doe", required: false, description: "Name of the user that added the contact" })
  @IsOptional()
  @IsString()
  addedBy?: string;

  @ApiProperty({ example: "2024-03-26T12:00:00Z", required: false, description: "Date when the contact was created" })
  @IsOptional()
  @IsDate()
  createdAt?: Date;

  @ApiProperty({ example: 'manual', required: false, description: "Source of the contact" })
  @IsOptional()
  @IsString()
  source?: 'manual' | 'file Upload' | 'CRM' | '-';
}

export class UploadContactDto {
  @IsString()
  @IsNotEmpty()
  contactName: string;

  @IsString()
  @IsOptional()
  customerId: string;

  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  @IsOptional()
  campaignNames: string;

  @IsString()
  @IsOptional()
  region: string;

}