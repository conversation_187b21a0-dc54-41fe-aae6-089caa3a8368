/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Check, FileUp, Link, Loader2, Search, Users } from "lucide-react";
import { Contact, fetchContacts } from "@/app/api/contacts";
import { RadioGroup } from "@/components/ui/radio-group";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

type SourceType = "contacts" | "import" | "thirdparty";

interface SourcesStepProps {
  data: {
    name: string;
    sources?: Contact[];
    sourceType?: SourceType;
    
  };
  updateData: (newData: Partial<{
    name: string;
    sources?: Contact[];
    sourceType?: SourceType;
  }>) => void;
}

export default function SourcesStep({ data, updateData }: SourcesStepProps) {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>(data.sources || []);
  const [sourceType, setSourceType] = useState<SourceType>(data.sourceType || "contacts");
  const [searchQuery, setSearchQuery] = useState("");
  const [errors, setErrors] = useState<{name?: string; contacts?: string;}>({});

  
  // states for paginations
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const loadingRef = useRef<HTMLTableRowElement>(null);
    const ITEMS_PER_PAGE = 20;


  useEffect(() => {
    if (data.sources && data.sources.length > 0) {
      setSelectedContacts(data.sources);
    }
  }, [data.sources]);
  

  // Load contacts when component mounts
  useEffect(() => {
    loadContacts();
  }, []);

   const loadContacts = async (pageNum: number = 1, search?: string) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setIsLoadingMore(true);
      }
  
      const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contacts?page=${pageNum}&limit=${ITEMS_PER_PAGE}${searchParam}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );
  
      if (!response.ok) throw new Error("Failed to fetch contacts");
      const data = await response.json();
      
      if (pageNum === 1) {
        setContacts(data);
      } else {
        setContacts(prev => [...prev, ...data]);
      }
      
      // Update hasMore based on received data length
      setHasMore(data.length === ITEMS_PER_PAGE);
  
    } catch (error) {
      console.error("Error loading contacts:", error);
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  };


   const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    loadContacts(1, searchQuery);
  };

  useEffect(() => {
      const observer = new IntersectionObserver(
        entries => {
          const firstEntry = entries[0];
          if (firstEntry.isIntersecting && hasMore && !loading && !isLoadingMore) {
            setPage(prev => prev + 1);
          }
        },
        { 
          threshold: 0.1 // Trigger when even 10% of the element is visible
        }
      );
    
      const currentRef = loadingRef.current;
      if (currentRef) {
        observer.observe(currentRef);
      }
    
      return () => {
        if (currentRef) {
          observer.unobserve(currentRef);
        }
      };
    }, [hasMore, loading, isLoadingMore]);

    

    useEffect(() => {
    if (page > 1) {
      loadContacts(page, searchQuery);
    }
  }, [page, searchQuery]);


  // Handle campaign name change
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateData({ name: e.target.value });
  };

  // Handle contact selection
  const toggleContactSelection = (contact: Contact) => {
    // Check if contact is already selected by comparing contactName and phoneNumber
    const isSelected = selectedContacts.some(
      c => c.contactName === contact.contactName && c.phoneNumber === contact.phoneNumber
    );
    
    let newSelection: Contact[];
    if (isSelected) {
      // Remove contact if already selected
      newSelection = selectedContacts.filter(
        c => !(c.contactName === contact.contactName && c.phoneNumber === contact.phoneNumber)
      );
    } else {
      // Add contact if not selected
      newSelection = [...selectedContacts, contact];
    }
  
    setSelectedContacts(newSelection);
  
    // Update parent state with full contact objects
    updateData({ 
      sources: newSelection,
      sourceType 
    });
  };

  // Handle source type change
  const handleSourceTypeChange = (value: SourceType) => {
    setSourceType(value);
    updateData({ sourceType: value });
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Here you would process the file
      console.log("File selected:", file.name);
      // For now, just update the sources with the file name
      updateData({
        sources: [{
          _id: Date.now().toString(),
          contactName: file.name, // Using contactName instead of name
          phoneNumber: "", // Required field for Contact type
          email: "", // Add other required fields
          type: 'file' // This might need to be handled differently if not in Contact type
        } as unknown as Contact],
        sourceType
      });
    }
  };

 
 

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="name">Campaign Name*</Label>
        <Input
          id="name"
          value={data.name}
          onChange={handleNameChange}
          placeholder="Enter campaign name"
          className={errors.name ? "border-red-500" : ""}
        />
        {errors.name && (
          <p className="text-red-500 text-sm">{errors.name}</p>
        )}
      </div>

      <div className="space-y-4">
        <Label>Source Type</Label>
        <RadioGroup
          value={sourceType}
          onValueChange={handleSourceTypeChange}
          className="grid grid-cols-3 gap-4"
        >
          <Card className={`cursor-pointer transition-all ${sourceType === "contacts" ? "border-primary" : ""}`}
          onClick={() => handleSourceTypeChange("contacts")}
          >
            <CardContent className="p-4 flex flex-col items-center justify-center space-y-2">
              <Users className="h-8 w-8 text-primary" />
              <Label htmlFor="contacts" className="font-medium">Contact List</Label>
            </CardContent>
          </Card>

          <Card className={`cursor-pointer transition-all ${sourceType === "import" ? "border-primary" : ""}`}
          onClick={() => handleSourceTypeChange("import")}
          >
            <CardContent className="p-4 flex flex-col items-center justify-center space-y-2">
              <FileUp className="h-8 w-8 text-primary" />
              <Label htmlFor="import" className="font-medium">Import File</Label>
            </CardContent>
          </Card>

          <Card className={`cursor-pointer transition-all ${sourceType === "thirdparty" ? "border-primary" : ""}`}
          onClick={() => handleSourceTypeChange("thirdparty")}
          >
            <CardContent className="p-4 flex flex-col items-center justify-center space-y-2">
              <Link className="h-8 w-8 text-primary" />
              <Label htmlFor="thirdparty" className="font-medium">3rd Party App</Label>
            </CardContent>
          </Card>
        </RadioGroup>
      </div>

      {/* Content based on selected source type */}
      <div className="space-y-4 mt-6">
      {sourceType === "contacts" && (
          <>
            <div className="flex justify-between items-center">
              <Label>Select Contact Sources</Label>
              <form onSubmit={handleSearch}>
              <div className="relative w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search contacts..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  />
              </div>
                  </form>
            </div>

            {loading ? (
              <div className="text-center py-4">Loading contacts...</div>
            ) : contacts.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                {contacts.length === 0 ? "No contacts available. Please create contacts first." : "No contacts match your search."}
              </div>
            ) : (
              <div className="border max-h-[300px] overflow-y-auto rounded-md mt-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12"></TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Phone Number</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contacts.map((contact, index) => (
                      <TableRow 
                        key={`${contact._id}-${index}`}
                        className="cursor-pointer hover:bg-muted"
                        onClick={() => toggleContactSelection(contact)}
                      >
                        <TableCell>
                          <div className={`w-5 h-5 rounded-full border ${
                            selectedContacts.some(c => 
                              c.contactName === contact.contactName && c.phoneNumber === contact.phoneNumber
                            )
                              ? "bg-primary border-primary"
                              : "border-gray-300"
                          }`}>
                            {selectedContacts.some(c => 
                            c.contactName === contact.contactName && c.phoneNumber === contact.phoneNumber
                          ) && (
                              <Check className="h-4.5 w-4.5 text-white" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{contact.contactName}</TableCell>
                        <TableCell>{contact.phoneNumber}</TableCell>
                      </TableRow>
                    ))}

                     {hasMore && (
                  <TableRow ref={loadingRef} className="h-20">
                    <TableCell colSpan={7}>
                      <div className="flex items-center justify-center py-4">
                        {isLoadingMore ? (
                          <Loader2 className="h-6 w-6 animate-spin text-primary" />
                        ) : (
                          <div className="h-8" />
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                        )}
                  </TableBody>
                </Table>
                {errors.contacts && (
                  <p className="text-red-500 text-sm mt-2">{errors.contacts}</p>
                )}
              </div>
            )}

            <div className="mt-4">
              <p className="text-sm text-muted-foreground">
                Selected {selectedContacts.length} contacts
              </p>
            </div>
          </>
        )}

        {sourceType === "import" && (
          <div className="space-y-4">
            <Label>Upload Contact File</Label>
            <Card className="border-dashed border-2">
              <CardContent className="p-6 flex flex-col items-center justify-center space-y-4">
                <FileUp className="h-12 w-12 text-muted-foreground" />
                <div className="text-center">
                  <p className="font-medium">Drag and drop your file here</p>
                  <p className="text-sm text-muted-foreground">Supports CSV and XLSX files</p>
                </div>
                <Input
                  type="file"
                  accept=".csv,.xlsx"
                  className="hidden"
                  id="file-upload"
                  onChange={handleFileUpload}
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  Browse Files
                </Button>
              </CardContent>
            </Card>

            {selectedContacts.length > 0 && selectedContacts[0].type === 'file' && (
              <div className="mt-4">
                <p className="text-sm font-medium">Selected file:</p>
                <p className="text-sm text-muted-foreground">{selectedContacts[0].contactName}</p>
              </div>
            )}
          </div>
        )}

        {sourceType === "thirdparty" && (
          <div className="space-y-4">
            <Label>Connect to Third-Party App</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {['Zapier', 'Encharge'].map(provider => (
                <Card
                  key={provider}
                  className="cursor-pointer hover:border-primary transition-all"
                >
                  <CardContent className="p-4 flex flex-col items-center justify-center space-y-2">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <span className="text-primary font-medium">{provider[0]}</span>
                    </div>
                    <p className="font-medium">{provider}</p>
                    <Button variant="outline" size="sm">Connect</Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}