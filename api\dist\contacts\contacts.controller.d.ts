import { ContactsService } from "./contacts.service";
import { ContactDto } from "./dto/contact.dto";
import { LoggerService } from "src/logger/logger.service";
export declare class ContactsController {
    private readonly contactsService;
    private readonly loggerService;
    constructor(contactsService: ContactsService, loggerService: LoggerService);
    private normalizePhoneNumber;
    callData(body: any): Promise<{
        results: {
            toolCallId: any;
            result: any;
        }[];
    }>;
    create(createContactDto: ContactDto, req: any): Promise<import("./interfaces/contact.interface").ContactDocument>;
    importContacts(req: any): Promise<import("./interfaces/contact.interface").ContactDocument[]>;
    webhookContacts(payload: {
        value: any[];
    }): Promise<any>;
    uploadContacts(file: Express.Multer.File, req: any): Promise<any>;
    findAll(page: number, limit: number, search?: string, filterType?: 'name' | 'campaign', campaignId?: string, noCampaign?: string): Promise<import("./interfaces/contact.interface").ContactDocument[]>;
    findById(id: string): Promise<import("./interfaces/contact.interface").ContactDocument>;
    update(id: string, updateContactDto: ContactDto): Promise<import("./interfaces/contact.interface").ContactDocument>;
    remove(id: string): Promise<void>;
}
