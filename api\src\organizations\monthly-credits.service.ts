import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { OrganizationDocument } from './interfaces/organization.interface';
import { GlobalSettingsService } from '../global-settings/global-settings.service';

@Injectable()
export class MonthlyCreditsService {
  private readonly logger = new Logger(MonthlyCreditsService.name);

  constructor(
    @InjectModel('Organization')
    private organizationModel: Model<OrganizationDocument>,
    private globalSettingsService: GlobalSettingsService,
  ) {}

  /**
   * Check if an organization needs monthly credits reset
   */
  async checkAndResetMonthlyCredits(organizationId: string): Promise<void> {
    const organization = await this.organizationModel.findById(organizationId).exec();
    if (!organization) {
      return;
    }

    const resetDay = organization.monthlyResetDate || 1;

    const now = new Date();
    const lastReset = new Date(organization.lastMonthlyReset);

    // Check if we need to reset based on the custom reset date
    if (this.shouldResetCredits(now, lastReset, resetDay)) {
      await this.resetMonthlyCredits(organization);
    }
  }

  /**
   * Determine if credits should be reset based on custom reset date
   */
  private shouldResetCredits(now: Date, lastReset: Date, resetDay: number): boolean {
    // If it's been more than a month since last reset, definitely reset
    const monthsDiff = (now.getFullYear() - lastReset.getFullYear()) * 12 + (now.getMonth() - lastReset.getMonth());
    if (monthsDiff > 1) {
      return true;
    }

    // If we're in the same month, no reset needed
    if (monthsDiff === 0) {
      return false;
    }

    // If we're in the next month, check if we've passed the reset day
    if (monthsDiff === 1) {
      return now.getDate() >= resetDay;
    }

    return false;
  }

  /**
   * Reset monthly credits for an organization
   */
  async resetMonthlyCredits(organization: OrganizationDocument): Promise<void> {
    // Use the organization's own monthly minutes allowance setting
    const monthlyMinutesAllowance = organization.monthlyMinutesAllowance || 0;

    // Reset monthly free credits to match the allowance
    organization.monthlyFreeCredits = monthlyMinutesAllowance;
    organization.monthlyFreeCreditsUsed = 0;
    organization.lastMonthlyReset = new Date();
    organization.usingFreeCredits = monthlyMinutesAllowance > 0;

    await organization.save();

    this.logger.log(`Reset monthly credits for organization ${organization._id}: ${monthlyMinutesAllowance} minutes`);
  }

  /**
   * Get available credits for an organization (free + paid)
   */
  async getAvailableCredits(organizationId: string): Promise<{
    freeCreditsRemaining: number;
    paidCredits: number;
    totalAvailable: number;
    usingFreeCredits: boolean;
  }> {
    await this.checkAndResetMonthlyCredits(organizationId);

    const organization = await this.organizationModel.findById(organizationId).exec();
    if (!organization) {
      return {
        freeCreditsRemaining: 0,
        paidCredits: 0,
        totalAvailable: 0,
        usingFreeCredits: false,
      };
    }

    const freeCreditsRemaining = Math.max(0, organization.monthlyFreeCredits - organization.monthlyFreeCreditsUsed);
    const paidCredits = organization.credits;
    const totalAvailable = freeCreditsRemaining + paidCredits;

    return {
      freeCreditsRemaining,
      paidCredits,
      totalAvailable,
      usingFreeCredits: organization.usingFreeCredits && freeCreditsRemaining > 0,
    };
  }

  /**
   * Deduct credits from an organization (free first, then paid)
   */
  async deductCredits(organizationId: string, amount: number): Promise<{
    success: boolean;
    freeCreditsDeducted: number;
    paidCreditsDeducted: number;
    remainingFreeCredits: number;
    remainingPaidCredits: number;
  }> {
    await this.checkAndResetMonthlyCredits(organizationId);

    const organization = await this.organizationModel.findById(organizationId).exec();
    if (!organization) {
      throw new Error(`Organization with ID ${organizationId} not found`);
    }

    const freeCreditsRemaining = Math.max(0, organization.monthlyFreeCredits - organization.monthlyFreeCreditsUsed);
    const totalAvailable = freeCreditsRemaining + organization.credits;

    if (totalAvailable < amount) {
      return {
        success: false,
        freeCreditsDeducted: 0,
        paidCreditsDeducted: 0,
        remainingFreeCredits: freeCreditsRemaining,
        remainingPaidCredits: organization.credits,
      };
    }

    let freeCreditsDeducted = 0;
    let paidCreditsDeducted = 0;

    // First, use free credits
    if (freeCreditsRemaining > 0 && amount > 0) {
      freeCreditsDeducted = Math.min(freeCreditsRemaining, amount);
      organization.monthlyFreeCreditsUsed += freeCreditsDeducted;
      amount -= freeCreditsDeducted;
    }

    // Then, use paid credits if needed
    if (amount > 0) {
      paidCreditsDeducted = amount;
      organization.credits -= paidCreditsDeducted;
      organization.usingFreeCredits = false; // Switch to paid credits
    }

    // Update usingFreeCredits status
    const newFreeCreditsRemaining = Math.max(0, organization.monthlyFreeCredits - organization.monthlyFreeCreditsUsed);
    if (newFreeCreditsRemaining === 0 && organization.usingFreeCredits) {
      organization.usingFreeCredits = false;
      this.logger.log(`Organization ${organizationId} has exhausted free credits, switching to paid billing`);
    }

    await organization.save();

    return {
      success: true,
      freeCreditsDeducted,
      paidCreditsDeducted,
      remainingFreeCredits: newFreeCreditsRemaining,
      remainingPaidCredits: organization.credits,
    };
  }

  /**
   * Cron job to check and reset monthly credits for all organizations daily
   * This checks if any organization needs a reset based on their individual reset date
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkAllOrganizationsForReset(): Promise<void> {
    this.logger.log('Checking all organizations for monthly credits reset');

    try {
      const organizations = await this.organizationModel.find({ status: 'active' }).exec();

      const now = new Date();
      let resetCount = 0;

      for (const organization of organizations) {
        const resetDay = organization.monthlyResetDate || 1;
        const lastReset = new Date(organization.lastMonthlyReset);

        if (this.shouldResetCredits(now, lastReset, resetDay)) {
          await this.resetMonthlyCredits(organization);
          resetCount++;
          this.logger.log(`Reset credits for organization ${organization.name} on day ${resetDay}`);
        }
      }

      if (resetCount > 0) {
        this.logger.log(`Monthly credits reset completed for ${resetCount} organizations`);
      }
    } catch (error) {
      this.logger.error('Error during monthly credits reset check:', error);
    }
  }

  /**
   * Initialize monthly credits for a new organization
   */
  async initializeMonthlyCredits(organizationId: string): Promise<void> {
    const organization = await this.organizationModel.findById(organizationId).exec();
    if (!organization) {
      return;
    }

    // Use the organization's own monthly minutes allowance setting
    const monthlyMinutesAllowance = organization.monthlyMinutesAllowance || 0;

    organization.monthlyFreeCredits = monthlyMinutesAllowance;
    organization.monthlyFreeCreditsUsed = 0;
    organization.lastMonthlyReset = new Date();
    organization.usingFreeCredits = monthlyMinutesAllowance > 0;

    await organization.save();

    this.logger.log(`Initialized monthly credits for organization ${organizationId}: ${monthlyMinutesAllowance} minutes`);
  }
}
