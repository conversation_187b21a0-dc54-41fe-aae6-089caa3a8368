/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {Search,RotateCcw,Play,FastForward,Pause,ArrowLeft,Filter,MoreVertical,Trash2,Download,AlertCircle} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { useCredits } from "@/contexts/CreditContext";
import { LowCreditAlert } from "@/components/LowCreditAlert";
import { CreditWarningAlert } from "@/components/CreditWarningAlert";

import { AvatarFallback } from "@/components/ui/avatar";
import { usePara<PERSON>, useRouter } from "next/navigation";
import avatarLisa from "@/assets/img/Binghatti-Lisa.jpeg";

import {DropdownMenu,DropdownMenuContent,DropdownMenuItem,DropdownMenuTrigger,} from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle} from "@/components/ui/alert-dialog";
import { authFetch } from "@/lib/authFetch";
import { UserInfo } from "@/lib/auth-client";

// Enum for Emotions
enum Emotions {
  Positive = "Positive",
  Neutral = "Neutral",
  SlightlyPositive = "Slightly Positive",
  SlightlyNegative = "Slightly Negative",
  Negative = "Negative",
}

// Interface for Call
interface Call {
  _id: string;
  fullName: string;
  mobileNumber: string;
  interest: string;
  timezone: string | null;
  callTranscript: string;
  callSummary: string;
  callStartTime: string;
  callEndTime: string;
  callDuration: string;
  callRoute: string | null;
  callPurpose: string | null;
  callEndReason: string;
  callCost: string;
  bookedStatus: string | null;
  confirmedStatus: string | null;
  additionalQuestions: string | null;
  recordingUrl: string;
  preferredProject: string;
  preferredLocation: string;
  preferredUnitType: string;
  projectType: string;
  investmentType: string;
  budget: string;
  recentContact: boolean;
  agent: string;
  createdAt: string;
  updatedAt: string;
  emotions: Emotions;

  // New fields
  brokenPromise: string | null;
  callBackLanguage: string | null;
  callBackRequest: string | null;
  claimedPaidAwaitingPOP: string | null;
  doNotCall: string | null;
  followingPaymentPlan: string | null;
  fullyPaid: string | null;
  fullyPaidByPDC: string | null;
  incorrectContactDetails: string | null;
  mortgage: string | null;
  notResponding: string | null;
  notRespondingSOASent: string | null;
  notWillingToPay: string | null;
  popRaised: string | null;
  promiseToPay: string | null;
  promiseToPayPartial: string | null;
  refuseToPay: string | null;
  thirdParty: string | null;
  willingToPay: string | null;
  Response: string | null;
  Notes: string | null;
  GuestRequest: string | null;
  Channel: string | null;
  totalCalls?: number;
  filteredCalls?: number;
}


interface Agent {
  id: string;
  name: string;
  avatar: string;
  // Add other fields as needed
}

const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

async function fetchAgents(): Promise<Agent[]> {
  const token = localStorage.getItem("access_token");
  if (!token) {
    throw new Error("No access token available");
  }

  const response = await fetch(`${API_BASE_URL}/api/agents`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch agents: ${response.status}`);
  }

  return response.json();
}


async function deleteCall(id: string): Promise<boolean> {
  try {
    const token = localStorage.getItem("access_token");
    if (!token) {
      console.error("No access token available");
      return false;
    }

    const response = await fetch(`${API_BASE_URL}/api/history/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete call: ${response.status}`);
    }

    return true;
  } catch (error) {
    console.error("Error deleting call:", error);
    return false;
  }
}

// Format duration in seconds to MM:SS format
function formatDuration(seconds: number): string {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
}

function formatDate(dateString: string): string {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return isNaN(date.getTime())
    ? "N/A"
    : date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
}

function formatDateTime(dateString: string): string {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return isNaN(date.getTime())
    ? "N/A"
    : date.toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
      });
}

const sentiments: Record<Emotions, { emoji: string; color: string }> = {
  [Emotions.Positive]: { emoji: "😊", color: "bg-green-500" },
  [Emotions.Neutral]: { emoji: "😐", color: "bg-blue-500" },
  [Emotions.SlightlyPositive]: { emoji: "🙂", color: "bg-cyan-500" },
  [Emotions.SlightlyNegative]: { emoji: "😕", color: "bg-slate-500" },
  [Emotions.Negative]: { emoji: "😟", color: "bg-red-500" },
};

// Helper function to get sentiment safely
const getSentiment = (emotion: string): { emoji: string; color: string } => {
  return sentiments[emotion as Emotions] || sentiments[Emotions.Neutral];
};

interface HistoryContentProps {
  contactName?: string;
}

export default function HistoryContent({ contactName }: HistoryContentProps) {
  const params = useParams();
  const fullNameParam = contactName || (params?.fullName as string | undefined);
  const router = useRouter();
  const decodedFullName = fullNameParam
    ? decodeURIComponent(fullNameParam)
    : "";
  const [userRole, setUserRole] = useState<string>("");
  const { credits, organizationCreditThreshold } = useCredits();

  const [calls, setCalls] = useState<Call[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedCall, setSelectedCall] = useState<Call | null>(null);
  const [totalCallCount, setTotalCallCount] = useState<number>(0);
  const [activeTab, setActiveTab] = useState("overview");
  // Initialize search query with the name parameter
  const [searchQuery, setSearchQuery] = useState(decodedFullName || "");
  const [audioProgress, setAudioProgress] = useState(0);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  // new state for filter type
  const [filterType, setFilterType] = useState<"all" | "name" | "agent">("all");

  //states for infinite scroll
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const observerTarget = useRef(null);

  //  state variables for deleting a call
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [callToDelete, setCallToDelete] = useState<Call | null>(null);

  // state variables for responsive behavior
  const [isMobile, setIsMobile] = useState(false);
  const [showCallList, setShowCallList] = useState(true);

  useEffect(() => {
    async function fetchUserData() {
      try {
        const response = await authFetch(`${API_BASE_URL}/api/auth/me`);
        const userData: UserInfo = await response.json();
        setUserRole(userData.role);
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    }
    fetchUserData();
  }, []);

  async function fetchCalls(pageNum = 1, append = false): Promise<Call[]> {
    try {
      const token = localStorage.getItem("access_token");
      if (!token) {
        console.error("No access token available");
        return [];
      }

      // Only set loading state on initial load, not when loading more
      if (!append) setIsLoading(true);
      if (append) setIsLoadingMore(true);

       // Build the URL with query parameters
      const params = new URLSearchParams();
      params.append('page', pageNum.toString());
      params.append('limit', '20');

      // Add search parameters if present
      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
        params.append('filterType', filterType);
      }

      const response = await fetch(`${API_BASE_URL}/api/history?${params.toString()}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch calls: ${response.status}`);
      }

      const newCalls = await response.json();

      if (!Array.isArray(newCalls)) {
        console.error("Unexpected response format from API:", newCalls);
        throw new Error("Invalid response format from API");
      }

      // If we're appending, add to existing calls, otherwise replace
      if (append) {
        setCalls(prevCalls => {
          // Create a map of existing call IDs to avoid duplicates
          const existingIds = new Set(prevCalls.map(call => call._id));

          // Only add calls that don't already exist
          const uniqueNewCalls = newCalls.filter(call => !existingIds.has(call._id));

          return [...prevCalls, ...uniqueNewCalls];
        });
      } else {
        setCalls(newCalls);
      }

      // If we received fewer calls than requested, we've reached the end
      setHasMore(newCalls.length === 20);

      return newCalls;
    } catch (error) {
      console.error("Error fetching calls:", error);
      return [];
    } finally {
      if (!append) setIsLoading(false);
      if (append) setIsLoadingMore(false);
    }
  }


  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !isLoading && !isLoadingMore) {
          // Load more data when the target element is visible
          setPage(prevPage => {
            const nextPage = prevPage + 1;
            fetchCalls(nextPage, true);
            return nextPage;
          });
        }
      },
      { threshold: 0.1 }
    );

    const currentTarget = observerTarget.current;
    if (currentTarget) observer.observe(currentTarget);

    return () => {
      if (currentTarget) observer.unobserve(currentTarget);
    };
  }, [hasMore, isLoadingMore, isLoading]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchCalls(1);
  };



  // Function to compare call arrays and check if there are new calls
  const hasNewCalls = (oldCalls: Call[], newCalls: Call[]): boolean => {
    if (oldCalls.length !== newCalls.length) return true;

    // Create a set of existing call IDs for faster lookup
    const existingCallIds = new Set(oldCalls.map((call) => call._id));

    // Check if any new call IDs are not in the existing set
    return newCalls.some((call) => !existingCallIds.has(call._id));
  };

  // Function to update calls with new data
  const updateCallsData = (historyCalls: Call[]) => {
    if (historyCalls.length > 0) {
      // Update the calls list
      setCalls(historyCalls);

      // If a call is currently selected, check if it still exists in the new data
      // and update it with the latest data if it does
      if (selectedCall) {
        const updatedSelectedCall = historyCalls.find(
          (call) => call._id === selectedCall._id
        );
        if (updatedSelectedCall) {
          setSelectedCall(updatedSelectedCall);
        }
      }

      // If we came from a contact name URL, filter and select a relevant call
      if (fullNameParam) {
        const nameFilteredCalls = historyCalls.filter(
          (call) =>
            call.fullName
              .toLowerCase()
              .includes(decodedFullName.toLowerCase()) ||
            decodedFullName.toLowerCase().includes(call.fullName.toLowerCase())
        );

        // If we found matching calls and no call is currently selected, select the most recent one
        if (nameFilteredCalls.length > 0 && !selectedCall) {
          // Sort by call date (newest first)
          const sortedFilteredCalls = [...nameFilteredCalls].sort((a, b) => {
            return (
              new Date(b.callStartTime).getTime() -
              new Date(a.callStartTime).getTime()
            );
          });

          // Select the most recent call
          setSelectedCall(sortedFilteredCalls[0]);
        } else if (nameFilteredCalls.length === 0 && !selectedCall) {
          // Only show error if no call is selected and no matching calls found
          setError(`No calls found for ${decodedFullName}`);
        }
      } else if (!selectedCall) {
        // Default behavior - select most recent call if none is selected
        // Sort by call date (newest first)
        const sortedCalls = [...historyCalls].sort((a, b) => {
          return (
            new Date(b.callStartTime).getTime() -
            new Date(a.callStartTime).getTime()
          );
        });
        setSelectedCall(sortedCalls[0]);
      }
    } else {
      setError("No call history found.");
    }
  };

  // Initial data loading
  useEffect(() => {
    async function loadData() {
      setIsLoading(true);

      try {
        // Step 1: Load agents first
        const agentsList = await fetchAgents();
        setAgents(agentsList);

        setPage(1);
        await fetchCalls(1, false);
      } finally {
        setIsLoading(false);
      }
    }

    loadData();
  }, []);

  // Auto-refresh polling
  const [isRefreshing, setIsRefreshing] = useState(false);

  // useEffect(() => {
  //   // Set up polling interval for auto-refresh
  //   const intervalId = setInterval(async () => {
  //     if (!isRefreshing) {
  //       setIsRefreshing(true);
  //       try {
  //         const newCalls = await fetchCalls();

  //         // Only update if there are new calls
  //         if (hasNewCalls(calls, newCalls)) {
  //           updateCallsData(newCalls);
  //         }
  //       } catch (error) {
  //         console.error("Error refreshing call data:", error);
  //       } finally {
  //         setIsRefreshing(false);
  //       }
  //     }
  //   }, 10000); // Poll every 10 seconds

  //   // Clean up interval on component unmount
  //   return () => clearInterval(intervalId);
  // }, [isRefreshing]);

  // Only depend on isRefreshing to avoid unnecessary re-renders

  const handleDelete = async () => {
    if (!callToDelete) return;

    try {
      const success = await deleteCall(callToDelete._id);
      if (success) {
        // Refresh the calls list
        const updatedCalls = await fetchCalls();
        setCalls(updatedCalls);
        setDeleteDialogOpen(false);
        setCallToDelete(null);

        // If the deleted call was selected, clear the selection
        if (selectedCall?._id === callToDelete._id) {
          setSelectedCall(null);
        }
      }
    } catch (error) {
      console.error("Error in delete handler:", error);
    }
  };

  // Reset audio when changing calls
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    setIsPlaying(false);
    setAudioProgress(0);

    // If the call was not answered, force the tab to be "overview"
    if (selectedCall && isCallNotAnswered(selectedCall)) {
      setActiveTab("overview");
    }
  }, [selectedCall]);


  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      // On mobile, if a call is selected, hide the call list
      if (mobile && selectedCall) {
        setShowCallList(false);
      } else {
        setShowCallList(true);
      }
    };
    // Initial check
    checkScreenSize();
    // Add event listener
    window.addEventListener("resize", checkScreenSize);

    // Cleanup
    return () => window.removeEventListener("resize", checkScreenSize);
  }, [selectedCall]);

  const getCallDuration = (call: Call): number => {
    return parseInt(call.callDuration) / 1000;
  };

  // Check if call was not answered (voicemail, customer-did-not-answer, or customer-busy)
  const isCallNotAnswered = (call: Call): boolean => {
    return (
      call.callEndReason === "voicemail" ||
      call.callEndReason === "customer-did-not-answer" ||
      call.callEndReason === "customer-busy" ||
      call.callEndReason === "customer-out-of-reach"
    );
  };

  const handlePlayAudio = () => {
    if (!selectedCall?.recordingUrl) return;
    if (!audioRef.current) {
      audioRef.current = new Audio(selectedCall.recordingUrl);

      audioRef.current.addEventListener("timeupdate", () => {
        if (audioRef.current) {
          const duration = getCallDuration(selectedCall);
          const currentTime = audioRef.current.currentTime;
          const progress = (currentTime / duration) * 100;
          setAudioProgress(progress);
        }
      });

      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false);
        setAudioProgress(0);
      });
    }

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleWaveformClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !selectedCall) return;
    const rect = e.currentTarget.getBoundingClientRect();
    const position = (e.clientX - rect.left) / rect.width;
    const duration = getCallDuration(selectedCall);
    audioRef.current.currentTime = duration * position;
    setAudioProgress(position * 100);
    if (!isPlaying && audioRef.current.paused) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const sortedCalls = [...calls].sort((a, b) => {
    const dateA = new Date(a.callStartTime).getTime();
    const dateB = new Date(b.callStartTime).getTime();
    return dateB - dateA; // Descending order (newest first)
  });

  const headerTitle = fullNameParam
    ? `Calls for ${decodedFullName}`
    : "Recent Calls";

  function getAgentAvatar(agentName: string): string | null {
    if (!agentName) return null;

    // Normalize the agent name for comparison
    const normalizedName = agentName.toLowerCase().trim();

    // Find the agent with matching name
    const matchedAgent = agents.find(
      (agent) =>
        agent.name.toLowerCase().trim() === normalizedName ||
        normalizedName.includes(agent.name.toLowerCase().trim()) ||
        agent.name.toLowerCase().trim().includes(normalizedName)
    );

    // Return the avatar URL if found
    if (matchedAgent && matchedAgent.avatar) {
      return matchedAgent.avatar;
    }

    return avatarLisa.src;
  }

  return (
    <>
      {/* Credit Alerts */}
      <CreditWarningAlert credits={credits} threshold={organizationCreditThreshold} />
      <LowCreditAlert credits={credits} threshold={organizationCreditThreshold} />

      <div className="flex h-[calc(100vh-60px)] w-full overflow-hidden overflow-x-auto bg-background">
        {/* Left sidebar - Call list */}
        <div className="w-80 border-r border-border flex flex-col h-full dark:bg-card">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {fullNameParam && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => router.back()}
                    className="mr-1"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                )}
                <h2 className="text-lg font-semibold">{headerTitle}</h2>
              </div>
              {calls.length > 0 && !isLoading ? (
              <Badge className="rounded-full bg-primary/10 hover:bg-primary/10 text-primary">
                {searchQuery.trim() && calls[0].filteredCalls ?
                  `${calls.length} of ${calls[0].filteredCalls} results` :
                  `${calls.length} of ${calls[0].totalCalls}`}
              </Badge>
            ) : null}
            </div>
            <div className="relative mt-3 flex gap-2">
            <form onSubmit={handleSearch} className="relative flex-1">
              <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={
                  filterType === "agent"
                    ? "Filter by agent..."
                    : "Filter by name..."
                }
                className="pl-9 h-9 bg-background dark:bg-muted"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </form>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-9 w-9">
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className={filterType === "all" ? "bg-muted" : ""}
                    onClick={() => setFilterType("all")}
                  >
                    All Fields
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className={filterType === "name" ? "bg-muted" : ""}
                    onClick={() => setFilterType("name")}
                  >
                    Filter by Name
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className={filterType === "agent" ? "bg-muted" : ""}
                    onClick={() => setFilterType("agent")}
                  >
                    Filter by Agent
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="overflow-y-auto flex-1">
            {isLoading ? (
              <div className="flex justify-center items-center h-24">
                <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : (
              calls.map((call, index) => (
                <div
                  key={`${call._id}-${index}`}
                  className={`w-full text-left px-4 py-3 border-b border-border hover:bg-muted/50 cursor-pointer transition-colors ${
                    selectedCall?._id === call._id ? "bg-muted/50" : ""
                  }`}
                  onClick={() => setSelectedCall(call)}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <div className="flex-shrink-0">
                      <Avatar className="h-8 w-8">
                        {getAgentAvatar(call.agent) ? (
                          <div className="relative h-full w-full rounded-full overflow-hidden">
                            <AvatarImage
                              src={getAgentAvatar(call.agent)!}
                              alt="Agent"
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <AvatarFallback className="bg-muted text-muted-foreground">
                            <svg
                              className="h-6 w-6"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 122.88 119.35"
                            >
                              <title>chatbot</title>
                              <path d="M57.49,29.2V23.53a14.41,14.41,0,0,1-2-.93A12.18,12.18,0,0,1,50.44,7.5a12.39,12.39,0,0,1,2.64-3.95A12.21,12.21,0,0,1,57,.92,12,12,0,0,1,61.66,0,12.14,12.14,0,0,1,72.88,7.5a12.14,12.14,0,0,1,0,9.27,12.08,12.08,0,0,1-2.64,3.94l-.06.06a12.74,12.74,0,0,1-2.36,1.83,11.26,11.26,0,0,1-2,.93V29.2H94.3a15.47,15.47,0,0,1,15.42,15.43v2.29H115a7.93,7.93,0,0,1,7.9,7.91V73.2A7.93,7.93,0,0,1,115,81.11h-5.25v2.07A15.48,15.48,0,0,1,94.3,98.61H55.23L31.81,118.72a2.58,2.58,0,0,1-3.65-.29,2.63,2.63,0,0,1-.63-1.85l1.25-18h-.21A15.45,15.45,0,0,1,13.16,83.18V81.11H7.91A7.93,7.93,0,0,1,0,73.2V54.83a7.93,7.93,0,0,1,7.9-7.91h5.26v-2.3A15.45,15.45,0,0,1,28.57,29.2H57.49ZM82.74,47.32a9.36,9.36,0,1,1-9.36,9.36,9.36,9.36,0,0,1,9.36-9.36Zm-42.58,0a9.36,9.36,0,1,1-9.36,9.36,9.36,9.36,0,0,1,9.36-9.36Zm6.38,31.36a2.28,2.28,0,0,1-.38-.38,2.18,2.18,0,0,1-.52-1.36,2.21,2.21,0,0,1,.46-1.39,2.4,2.4,0,0,1,.39-.39,3.22,3.22,0,0,1,3.88-.08A22.36,22.36,0,0,0,56,78.32a14.86,14.86,0,0,0,5.47,1A16.18,16.18,0,0,0,67,78.22,25.39,25.39,0,0,0,72.75,75a3.24,3.24,0,0,1,3.89.18,3,3,0,0,1,.37.41,2.22,2.22,0,0,1,.42,1.4,2.33,2.33,0,0,1-.58,1.35,2.29,2.29,0,0,1-.43.38,30.59,30.59,0,0,1-7.33,4,22.28,22.28,0,0,1-7.53,1.43A21.22,21.22,0,0,1,54,82.87a27.78,27.78,0,0,1-7.41-4.16l0,0ZM94.29,34.4H28.57A10.26,10.26,0,0,0,18.35,44.63V83.18A10.26,10.26,0,0,0,28.57,93.41h3.17a2.61,2.61,0,0,1,2.41,2.77l-1,14.58L52.45,94.15a2.56,2.56,0,0,1,1.83-.75h40a10.26,10.26,0,0,0,10.22-10.23V44.62A10.24,10.24,0,0,0,94.29,34.4Z" />
                            </svg>
                          </AvatarFallback>
                        )}
                      </Avatar>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium truncate">
                          {call.fullName || "Unknown Agent"}
                        </p>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {formatDate(call.callStartTime)}
                          </span>
                          {userRole === "superadmin" && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 hover:bg-muted"
                                >
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setCallToDelete(call);
                                    setDeleteDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-xs text-muted-foreground mt-0.5">
                        <div className="flex items-center">
                          {isCallNotAnswered(call) ? (
                            <Badge className="bg-red-500 text-white hover:bg-red-600 px-1 py-0 text-[10px]">
                              didn&apos;t pick
                            </Badge>
                          ) : (
                            <>
                              <svg
                                className="h-3 w-3 mr-1"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              {formatDuration(getCallDuration(call))}
                            </>
                          )}
                        </div>
                        <div className="flex items-center">
                          <svg
                            className="h-3 w-3 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          {new Date(call.callStartTime).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}

            {/* Loading indicator and observer target */}
      <div ref={observerTarget} className="py-4 text-center">
        {isLoadingMore ? (
          <div className="flex justify-center items-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        ) : hasMore ? (
          <span className="text-sm text-muted-foreground">Scroll for more</span>
        ) : (
          <span className="text-sm text-muted-foreground">No more calls</span>
        )}
      </div>

            {error && <div className="p-4 text-sm text-amber-600">{error}</div>}
          </div>
        </div>

        {/* Right content - Call details */}
        <div className="flex-1 min-w-[500px] flex flex-col overflow-hidden">
          {selectedCall ? (
            <>
              {/* Call Header */}
              <div className="p-4 border-b border-border">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10 mr-3">
                      {selectedCall.agent ? (
                        <div className="relative h-full w-full rounded-full overflow-hidden">
                          <AvatarImage
                            src={getAgentAvatar(selectedCall.agent)!}
                            alt={selectedCall.agent || "Agent"}
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <AvatarFallback className="bg-primary/10 text-primary">
                          {selectedCall.agent?.charAt(0) || "A"}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div>
                      <h2 className="text-lg font-semibold">
                        {selectedCall.fullName || "Caller"}
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        Agent: {selectedCall.agent || ""}
                      </p>
                    </div>
                  </div>

                  {isCallNotAnswered(selectedCall) ? (
                    <div className="flex items-center">
                      <Badge className="bg-red-500 text-white px-2 py-1">
                        didn&apos;t pick
                      </Badge>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      {/* Audio control buttons */}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-9 w-9"
                        onClick={() => {
                          if (audioRef.current) {
                            audioRef.current.currentTime = 0;
                            setAudioProgress(0);
                            if (isPlaying) audioRef.current.play();
                          }
                        }}
                      >
                        <RotateCcw size={16} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-9 w-9"
                        onClick={handlePlayAudio}
                      >
                        {isPlaying ? <Pause size={16} /> : <Play size={16} />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-9 w-9"
                        onClick={() => {
                          if (audioRef.current) {
                            audioRef.current.currentTime = Math.min(
                              audioRef.current.currentTime + 10,
                              getCallDuration(selectedCall)
                            );
                          }
                        }}
                      >
                        <FastForward size={16} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-9 w-9"
                        onClick={() => {
                          if (selectedCall?.recordingUrl) {
                            // Open in a new tab
                            window.open(selectedCall.recordingUrl, "_blank");
                          }
                        }}
                      >
                        <Download size={16} />
                      </Button>
                    </div>
                  )}
                </div>

                {/* Audio waveform - only show for answered calls */}
                {!isCallNotAnswered(selectedCall) && (
                  <div className="mt-6 flex items-center">
                    <div
                      className="relative w-full h-12 bg-muted rounded-md overflow-hidden cursor-pointer"
                      onClick={handleWaveformClick}
                    >
                      {/* Progress bar */}
                      <div
                        className="absolute top-0 left-0 h-full bg-primary/20 pointer-events-none"
                        style={{ width: `${audioProgress}%` }}
                      ></div>

                      {/* Static waveform visualization */}
                      <div className="absolute inset-0 flex items-center justify-between px-1 pointer-events-none">
                        {Array.from({ length: 80 }).map((_, i) => {
                          const seed =
                            i *
                            selectedCall._id.charCodeAt(
                              i % selectedCall._id.length
                            );
                          const height =
                            30 +
                            Math.sin(seed * 0.1) * 25 +
                            Math.cos(seed * 0.2) * 20;
                          return (
                            <div
                              key={i}
                              className={
                                i < audioProgress * 0.8
                                  ? "bg-primary/50"
                                  : "bg-muted-foreground/20"
                              }
                              style={{
                                height: `${Math.max(
                                  15,
                                  Math.min(85, height)
                                )}%`,
                                width: "1px",
                              }}
                            />
                          );
                        })}
                      </div>

                      {/* Playhead */}
                      <div
                        className="absolute top-0 w-0.5 h-full bg-primary z-10 pointer-events-none"
                        style={{ left: `${audioProgress}%` }}
                      ></div>
                    </div>
                    <div className="ml-2 text-sm text-foreground whitespace-nowrap">
                      {`${formatDuration(
                        (getCallDuration(selectedCall) * audioProgress) / 100
                      )} / ${formatDuration(getCallDuration(selectedCall))}`}
                    </div>
                  </div>
                )}
              </div>

              {/* Tabs for call details */}
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="flex-1 overflow-hidden"
              >
                <div className="border-b border-border">
                  <TabsList className="h-12 bg-transparent border-b-0 px-4 gap-6">
                    <TabsTrigger
                      value="overview"
                      className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground"
                    >
                      Overview
                    </TabsTrigger>
                    {!isCallNotAnswered(selectedCall) && (
                      <>
                        <TabsTrigger
                          value="summary"
                          className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground"
                        >
                          Summary
                        </TabsTrigger>
                        <TabsTrigger
                          value="transcript"
                          className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground"
                        >
                          Transcript
                        </TabsTrigger>
                        <TabsTrigger
                          value="additionalInfo"
                          className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground"
                        >
                          Additional Info
                        </TabsTrigger>
                      </>
                    )}
                  </TabsList>
                </div>

                {/* Overview Tab */}
                <TabsContent
                  value="overview"
                  className="flex-1 p-6 overflow-y-auto"
                >
                  <h3 className="text-lg font-medium mb-4">Information</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="w-32 text-sm font-medium text-muted-foreground">
                        Full Name
                      </div>
                      <div className="flex items-center">
                        <span className="text-sm">
                          {selectedCall.fullName || "Unknown"}
                        </span>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-muted-foreground">
                        Agent
                      </div>
                      <div className="text-sm">{selectedCall.agent || ""}</div>
                    </div>
                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-muted-foreground">
                        Mobile
                      </div>
                      <div className="text-sm">
                        {selectedCall.mobileNumber || "N/A"}
                      </div>
                    </div>
                    {!isCallNotAnswered(selectedCall) && (
                      <div className="flex">
                        <div className="w-32 text-sm font-medium text-muted-foreground">
                          Duration
                        </div>
                        <div className="text-sm">
                          {formatDuration(getCallDuration(selectedCall))}
                        </div>
                      </div>
                    )}

                    <div className="flex">
                      <div className="w-32 text-sm font-medium text-muted-foreground">
                        Started at
                      </div>
                      <div className="text-sm">
                        {formatDateTime(selectedCall.callStartTime)}
                      </div>
                    </div>

                    {!isCallNotAnswered(selectedCall) && (
                      <div className="flex">
                        <div className="w-32 text-sm font-medium text-muted-foreground">
                          Ended at
                        </div>
                        <div className="text-sm">
                          {formatDateTime(selectedCall.callEndTime)}
                        </div>
                      </div>
                    )}
                  </div>

                  <h3 className="text-lg font-medium mt-8 mb-4">Analysis</h3>
                  <div className="space-y-4">
                    {isCallNotAnswered(selectedCall) ? (
                      <div className="flex items-start">
                        <div className="w-32 text-sm font-medium text-muted-foreground">
                          End call reason
                        </div>
                        <div className="flex items-center">
                          <Badge className="bg-red-500 text-white">
                            {selectedCall.callEndReason || "N/A"}
                          </Badge>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-start">
                          <div className="w-32 text-sm font-medium text-muted-foreground">
                            Call sentiment
                          </div>
                          <div className="flex items-center">
                            {(() => {
                              const sentiment = getSentiment(
                                selectedCall.emotions
                              );
                              return (
                                <>
                                  <div
                                    className={`h-2 w-2 rounded-full ${sentiment.color} mr-2 mt-1`}
                                  ></div>
                                  <span className="text-sm flex items-center">
                                    {sentiment.emoji}{" "}
                                    <span className="ml-1">
                                      {selectedCall.emotions}
                                    </span>
                                  </span>
                                </>
                              );
                            })()}
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="w-32 text-sm font-medium text-muted-foreground">
                            Call status
                          </div>
                          <div className="flex items-center">
                            <div className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-1"></div>
                            <span className="text-sm">Completed</span>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <div className="w-32 text-sm font-medium text-muted-foreground">
                            End call reason
                          </div>
                          <div className="flex items-center">
                            <div className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-1"></div>
                            <span className="text-sm">
                              {selectedCall.callEndReason || "N/A"}
                            </span>
                          </div>
                        </div>
                      </>
                    )}
                    {/* <div className="flex items-start">
                      <div className="w-32 text-sm font-medium text-muted-foreground">Call cost</div>
                      <div className="flex items-center">
                        <span className="text-sm">${selectedCall.callCost || "N/A"}</span>
                      </div>
                    </div> */}
                  </div>
                </TabsContent>

                {/* Summary Tab */}
                <TabsContent
                  value="summary"
                  className="flex-1 p-6 overflow-y-auto"
                >
                  <div className="text-sm">
                    <h3 className="text-md font-medium mb-4 text-muted-foreground">
                      Summary
                    </h3>
                    <div className="p-4 bg-muted/50 rounded-md">
                      <p>
                        {selectedCall.callSummary
                          ? selectedCall.callSummary
                          : "No summary available."}
                      </p>
                    </div>
                  </div>
                </TabsContent>

                {/* Transcript Tab */}
                <TabsContent
                  value="transcript"
                  className="flex-1 overflow-hidden flex flex-col"
                >
                  <div className="p-4 border-b border-border">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Search"
                        className="pl-10 pr-10 bg-background dark:bg-card"
                      />
                    </div>
                  </div>

                  <div className="flex-1 overflow-y-auto p-4">
                    <div className="space-y-6">
                      {selectedCall.callTranscript ? (
                        // Parse and display transcript
                        selectedCall.callTranscript
                          .split("\n")
                          .map((line, idx) => {
                            if (
                              line.startsWith("AI:") ||
                              line.startsWith("User:")
                            ) {
                              const isUser = line.startsWith("User:");
                              const content = line
                                .substring(line.indexOf(":") + 1)
                                .trim();
                              return (
                                <div
                                  key={idx}
                                  className={`flex ${
                                    isUser ? "justify-end" : ""
                                  }`}
                                >
                                  {!isUser && (
                                    <div className="flex-shrink-0 mr-3">
                                      <Avatar className="h-8 w-8">
                                        <AvatarFallback className="bg-primary/10 text-primary">
                                          {selectedCall.agent?.charAt(0) || "A"}
                                        </AvatarFallback>
                                      </Avatar>
                                    </div>
                                  )}
                                  <div
                                    className={`max-w-[75%] ${
                                      !isUser ? "mr-auto" : "ml-auto"
                                    }`}
                                  >
                                    <div className="text-xs text-muted-foreground mb-1">
                                      {isUser ? "You" : selectedCall.agent}
                                    </div>
                                    <div
                                      className={`text-sm p-3 rounded-lg ${
                                        !isUser ? "bg-muted" : "bg-primary/10"
                                      }`}
                                    >
                                      {content}
                                    </div>
                                  </div>
                                  {isUser && (
                                    <div className="flex-shrink-0 ml-3">
                                      <Avatar className="h-8 w-8">
                                        <AvatarFallback className="bg-muted">
                                          {selectedCall.fullName?.charAt(0) ||
                                            "U"}
                                        </AvatarFallback>
                                      </Avatar>
                                    </div>
                                  )}
                                </div>
                              );
                            }
                            return null;
                          })
                      ) : (
                        <div className="text-center text-muted-foreground">
                          No transcript available
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                {/* Additional Info Tab */}
                <TabsContent
                  value="additionalInfo"
                  className="flex-1 p-6 overflow-y-auto"
                >
                  <h3 className="text-lg font-medium mb-4">Preferences</h3>
                  <div className="space-y-4">
                    {selectedCall.interest && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Interest
                        </div>
                        <div className="text-sm">{selectedCall.interest}</div>
                      </div>
                    )}

                    {selectedCall.preferredProject && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Preferred Project
                        </div>
                        <div className="text-sm">
                          {selectedCall.preferredProject}
                        </div>
                      </div>
                    )}

                    {selectedCall.preferredLocation && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Location
                        </div>
                        <div className="text-sm">
                          {selectedCall.preferredLocation}
                        </div>
                      </div>
                    )}

                    {selectedCall.preferredUnitType && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Unit Type
                        </div>
                        <div className="text-sm">
                          {selectedCall.preferredUnitType}
                        </div>
                      </div>
                    )}

                    {selectedCall.projectType && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Project Type
                        </div>
                        <div className="text-sm">
                          {selectedCall.projectType}
                        </div>
                      </div>
                    )}

                    {selectedCall.investmentType && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Investment Type
                        </div>
                        <div className="text-sm">
                          {selectedCall.investmentType}
                        </div>
                      </div>
                    )}

                    {selectedCall.budget && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Budget
                        </div>
                        <div className="text-sm">{selectedCall.budget}</div>
                      </div>
                    )}

                    {/* New fields (string | null) */}
                    {selectedCall.brokenPromise && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Broken Promise
                        </div>
                        <div className="text-sm">
                          {selectedCall.brokenPromise}
                        </div>
                      </div>
                    )}

                    {selectedCall.callBackLanguage && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Call Back Language
                        </div>
                        <div className="text-sm">
                          {selectedCall.callBackLanguage}
                        </div>
                      </div>
                    )}

                    {selectedCall.callBackRequest && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Call Back Request
                        </div>
                        <div className="text-sm">
                          {selectedCall.callBackRequest}
                        </div>
                      </div>
                    )}

                    {selectedCall.claimedPaidAwaitingPOP && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Claimed Paid Awaiting POP
                        </div>
                        <div className="text-sm">
                          {selectedCall.claimedPaidAwaitingPOP}
                        </div>
                      </div>
                    )}

                    {selectedCall.doNotCall && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Do Not Call
                        </div>
                        <div className="text-sm">{selectedCall.doNotCall}</div>
                      </div>
                    )}

                    {selectedCall.followingPaymentPlan && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Following Payment Plan
                        </div>
                        <div className="text-sm">
                          {selectedCall.followingPaymentPlan}
                        </div>
                      </div>
                    )}

                    {selectedCall.fullyPaid && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Fully Paid
                        </div>
                        <div className="text-sm">{selectedCall.fullyPaid}</div>
                      </div>
                    )}

                    {selectedCall.fullyPaidByPDC && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Fully Paid By PDC
                        </div>
                        <div className="text-sm">
                          {selectedCall.fullyPaidByPDC}
                        </div>
                      </div>
                    )}

                    {selectedCall.incorrectContactDetails && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Incorrect Contact Details
                        </div>
                        <div className="text-sm">
                          {selectedCall.incorrectContactDetails}
                        </div>
                      </div>
                    )}

                    {selectedCall.mortgage && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Mortgage
                        </div>
                        <div className="text-sm">{selectedCall.mortgage}</div>
                      </div>
                    )}

                    {selectedCall.notResponding && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Not Responding
                        </div>
                        <div className="text-sm">
                          {selectedCall.notResponding}
                        </div>
                      </div>
                    )}

                    {selectedCall.notRespondingSOASent && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Not Responding (SOA Sent)
                        </div>
                        <div className="text-sm">
                          {selectedCall.notRespondingSOASent}
                        </div>
                      </div>
                    )}

                    {selectedCall.notWillingToPay && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Not Willing To Pay
                        </div>
                        <div className="text-sm">
                          {selectedCall.notWillingToPay}
                        </div>
                      </div>
                    )}

                    {selectedCall.popRaised && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          POP Raised
                        </div>
                        <div className="text-sm">{selectedCall.popRaised}</div>
                      </div>
                    )}

                    {selectedCall.promiseToPay && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Promise To Pay
                        </div>
                        <div className="text-sm">
                          {selectedCall.promiseToPay}
                        </div>
                      </div>
                    )}

                    {selectedCall.promiseToPayPartial && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Promise To Pay (Partial)
                        </div>
                        <div className="text-sm">
                          {selectedCall.promiseToPayPartial}
                        </div>
                      </div>
                    )}

                    {selectedCall.refuseToPay && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Refuse To Pay
                        </div>
                        <div className="text-sm">
                          {selectedCall.refuseToPay}
                        </div>
                      </div>
                    )}

                    {selectedCall.thirdParty && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Third Party
                        </div>
                        <div className="text-sm">{selectedCall.thirdParty}</div>
                      </div>
                    )}

                    {selectedCall.willingToPay && (
                      <div className="flex">
                        <div className="w-40 text-sm font-medium text-muted-foreground">
                          Willing To Pay
                        </div>
                        <div className="text-sm">
                          {selectedCall.willingToPay}
                        </div>
                      </div>
                    )}
                  </div>

                  {selectedCall.bookedStatus && (
                    <>
                      <h3 className="text-lg font-medium mt-8 mb-4">
                        Booking Information
                      </h3>
                      <div className="space-y-4">
                        <div className="flex">
                          <div className="w-40 text-sm font-medium text-muted-foreground">
                            Booked Status
                          </div>
                          <div className="text-sm">
                            {selectedCall.bookedStatus}
                          </div>
                        </div>
                        <div className="flex">
                          <div className="w-40 text-sm font-medium text-muted-foreground">
                            Confirmed Status
                          </div>
                          <div className="text-sm">
                            {selectedCall.confirmedStatus || "N/A"}
                          </div>
                        </div>
                      </div>
                    </>
                  )}

                  {selectedCall.additionalQuestions && (
                    <>
                      <h3 className="text-lg font-medium mt-8 mb-4">
                        Additional Questions
                      </h3>
                      <div className="p-4 bg-muted/50 rounded-md">
                        <p className="text-sm">
                          {selectedCall.additionalQuestions}
                        </p>
                      </div>
                    </>
                  )}

                  {selectedCall.Response && (
                    <div className="flex mt-5">
                      <div className="w-40 text-sm font-medium text-muted-foreground">
                        Response
                      </div>
                      <div className="text-sm">{selectedCall.Response}</div>
                    </div>
                  )}
                  {selectedCall.Notes && (
                    <div className="flex mt-3">
                      <div className="w-40 text-sm font-medium text-muted-foreground">
                        Notes
                      </div>
                      <div className="text-sm">{selectedCall.Notes}</div>
                    </div>
                  )}
                  {selectedCall.Channel && (
                    <div className="flex mt-3">
                      <div className="w-40 text-sm font-medium text-muted-foreground">
                        Channel
                      </div>
                      <div className="text-sm">{selectedCall.Channel}</div>
                    </div>
                  )}
                  {selectedCall.GuestRequest && (
                    <div className="flex mt-3">
                      <div className="w-40 text-sm font-medium text-muted-foreground">
                        GuestRequest
                      </div>
                      <div className="text-sm">{selectedCall.GuestRequest}</div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </>
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              {isLoading ? (
                <div className="flex flex-col items-center">
                  <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mb-4"></div>
                  <p>Loading call data...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-lg font-medium">
                    {error || "Select a call to view details"}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete this call history record. This
                action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}
