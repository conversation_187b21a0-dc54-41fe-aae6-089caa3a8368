import { Module } from '@nestjs/common';
import { HistoryModule } from 'src/history/history.module';
import { AgentModule } from 'src/agent/agent.module';
import { CampaignModule } from 'src/campaign/campaign.module';
import { ScheduledCallModule } from 'src/scheduled-call/scheduled-call.module';
import { MongooseModule } from '@nestjs/mongoose';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { HistorySchema } from 'src/history/schemas/history.schema';
import { AgentSchema } from 'src/agent/schemas/agent.schema';
import { CampaignSchema } from 'src/campaign/schemas/campaign.schema';
import { ScheduledCallSchema } from 'src/scheduled-call/schemas/scheduled-call.schema';
import { ContactSchema } from 'src/contacts/schemas/contact.schema';
import { DashboardStatsSchema } from './schemas/dashboard.schema';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
        { name: 'History', schema: HistorySchema },
        { name: 'Agent', schema: AgentSchema },
        { name: 'Campaign', schema: CampaignSchema },
        { name: 'ScheduledCall', schema: ScheduledCallSchema },
        { name: 'Contact', schema: ContactSchema },
        { name: 'DashboardStats', schema: DashboardStatsSchema },
      ]),
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
  exports: [DashboardService, MongooseModule],
})
export class DashboardModule {}