{"version": 3, "file": "history.service.js", "sourceRoot": "", "sources": ["../../src/history/history.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,+CAA+C;AAMxC,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YAEmB,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;IAC1C,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,gBAA4B;QACvC,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAC/D,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,OAAO,CACX,IAAa,EACb,KAAc,EACd,MAAe,EACf,aAAuC,KAAK;QAG5C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,CAAC;QAEnE,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAExC,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;YAEnD,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;gBACzB,MAAM,CAAC,GAAG,GAAG;oBACX,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE;oBACrC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE;iBACnC,CAAC;YACJ,CAAC;iBAAM,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;gBACjC,MAAM,CAAC,QAAQ,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;YAC5C,CAAC;iBAAM,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;gBAClC,MAAM,CAAC,KAAK,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;YACzC,CAAC;QACH,CAAC;QAGD,IAAI,aAAa,GAAG,UAAU,CAAC;QAC/B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;QACxE,CAAC;QAID,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAGvE,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YAC9C,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAChC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;QAGD,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;QAGjC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAEnB,IAAI,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC;YACjC,IAAI,CAAC,gBAAgB,CAAC,GAAG,aAAa,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;CAEF,CAAA;AApFY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,SAAS,CAAC,CAAA;;GAFd,cAAc,CAoF1B"}