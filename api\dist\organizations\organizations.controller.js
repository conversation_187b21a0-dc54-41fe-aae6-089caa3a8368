"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const organizations_service_1 = require("./organizations.service");
const organization_dto_1 = require("./dto/organization.dto");
const update_organization_settings_dto_1 = require("./dto/update-organization-settings.dto");
const users_service_1 = require("../users/users.service");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let OrganizationsController = class OrganizationsController {
    constructor(organizationsService, usersService, organizationModel, userModel) {
        this.organizationsService = organizationsService;
        this.usersService = usersService;
        this.organizationModel = organizationModel;
        this.userModel = userModel;
    }
    async create(createOrganizationDto) {
        try {
            return await this.organizationsService.create(createOrganizationDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to create organization', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll() {
        try {
            return await this.organizationsService.findAll();
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to fetch organizations', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findMyOrganizations(req) {
        try {
            const userId = req.user.userId;
            return await this.organizationsService.findByUser(userId);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to fetch organizations', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOne(id, req) {
        try {
            const organization = await this.organizationsService.findOne(id);
            if (req.user.role !== 'superadmin') {
                const userOrgs = await this.organizationsService.findByUser(req.user.userId);
                const hasAccess = userOrgs.some(org => org._id.toString() === id);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            return organization;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to fetch organization', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAvailableCredits(id, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userOrgs = await this.organizationsService.findByUser(req.user.userId);
                const hasAccess = userOrgs.some(org => org._id.toString() === id);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            return await this.organizationsService.getAvailableCredits(id);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to fetch credits information', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async update(id, updateOrganizationDto, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            return await this.organizationsService.update(id, updateOrganizationDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to update organization', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateBilling(id, updateBillingDto) {
        try {
            return await this.organizationsService.updateBilling(id, updateBillingDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to update organization billing', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateSettings(id, updateSettingsDto, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            return await this.organizationsService.updateSettings(id, updateSettingsDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to update organization settings', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async remove(id) {
        try {
            await this.organizationsService.delete(id);
            return { message: 'Organization deleted successfully' };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to delete organization', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async addUser(id, userId, isAdmin, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            return await this.organizationsService.addUserToOrganization(id, userId, isAdmin);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to add user to organization', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removeUser(id, userId, req) {
        try {
            if (req.user.role !== 'superadmin') {
                const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
                const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);
                if (!hasAccess) {
                    throw new common_1.HttpException('Unauthorized access to organization', common_1.HttpStatus.FORBIDDEN);
                }
            }
            return await this.organizationsService.removeUserFromOrganization(id, userId);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to remove user from organization', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async initializeMonthlyCredits() {
        try {
            const organizations = await this.organizationModel.find().exec();
            const results = [];
            for (const organization of organizations) {
                const orgId = organization._id.toString();
                try {
                    await this.organizationsService.initializeMonthlyCreditsForOrganization(orgId);
                    console.log(`Initialized monthly credits for organization ${orgId}`);
                    results.push({ organizationId: orgId, status: 'success' });
                }
                catch (error) {
                    console.error(`Error initializing monthly credits for organization ${orgId}:`, error);
                    results.push({ organizationId: orgId, status: 'error', message: error.message });
                }
            }
            return {
                message: 'Monthly credits initialization completed',
                results
            };
        }
        catch (error) {
            console.error('Error initializing monthly credits:', error);
            throw new common_1.HttpException(error.message || 'Failed to initialize monthly credits', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async fixOrganizationUsers() {
        try {
            const organizations = await this.organizationModel.find().exec();
            const results = [];
            for (const organization of organizations) {
                const orgId = organization._id.toString();
                const allUsers = [...new Set([
                        ...organization.adminUsers.map(id => id.toString()),
                        ...organization.users.map(id => id.toString())
                    ])];
                console.log(`Processing organization ${orgId} with ${allUsers.length} users`);
                for (const userId of allUsers) {
                    try {
                        const user = await this.userModel.findById(userId).exec();
                        if (!user) {
                            console.error(`User ${userId} not found`);
                            results.push({ userId, status: 'error', message: 'User not found' });
                            continue;
                        }
                        await this.usersService.updateUser(userId, { organizationId: organization._id.toString() });
                        console.log(`Updated organizationId for user ${userId} to ${orgId}`);
                        results.push({ userId, status: 'success', organizationId: orgId });
                    }
                    catch (error) {
                        console.error(`Error updating user ${userId}:`, error);
                        results.push({ userId, status: 'error', message: error.message });
                    }
                }
            }
            return {
                message: 'Organization users fixed successfully',
                results
            };
        }
        catch (error) {
            console.error('Error fixing organization users:', error);
            throw new common_1.HttpException(error.message || 'Failed to fix organization users', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.OrganizationsController = OrganizationsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new organization' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Organization created successfully' }),
    (0, swagger_1.ApiBody)({ type: organization_dto_1.CreateOrganizationDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organization_dto_1.CreateOrganizationDto]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all organizations' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns all organizations' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('my-organizations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get organizations for the current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns organizations for the current user' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "findMyOrganizations", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get an organization by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns the organization' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/credits'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get available credits information for an organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns credits information' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "getAvailableCredits", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update an organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization updated successfully' }),
    (0, swagger_1.ApiBody)({ type: organization_dto_1.UpdateOrganizationDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, organization_dto_1.UpdateOrganizationDto, Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/billing'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update organization billing settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization billing updated successfully' }),
    (0, swagger_1.ApiBody)({ type: organization_dto_1.UpdateOrganizationBillingDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, organization_dto_1.UpdateOrganizationBillingDto]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "updateBilling", null);
__decorate([
    (0, common_1.Patch)(':id/settings'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update organization settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization settings updated successfully' }),
    (0, swagger_1.ApiBody)({ type: update_organization_settings_dto_1.UpdateOrganizationSettingsDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_organization_settings_dto_1.UpdateOrganizationSettingsDto, Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "updateSettings", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization deleted successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/users/:userId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Add a user to an organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User added to organization successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Body)('isAdmin')),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Boolean, Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "addUser", null);
__decorate([
    (0, common_1.Delete)(':id/users/:userId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin', 'admin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Remove a user from an organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User removed from organization successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "removeUser", null);
__decorate([
    (0, common_1.Post)('initialize-monthly-credits'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Initialize monthly credits for all existing organizations' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Monthly credits initialized successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "initializeMonthlyCredits", null);
__decorate([
    (0, common_1.Post)('fix-organization-users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Fix organization users by updating their organizationId field' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization users fixed successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrganizationsController.prototype, "fixOrganizationUsers", null);
exports.OrganizationsController = OrganizationsController = __decorate([
    (0, swagger_1.ApiTags)('organizations'),
    (0, common_1.Controller)('organizations'),
    __param(2, (0, mongoose_1.InjectModel)('Organization')),
    __param(3, (0, mongoose_1.InjectModel)('User')),
    __metadata("design:paramtypes", [organizations_service_1.OrganizationsService,
        users_service_1.UsersService,
        mongoose_2.Model,
        mongoose_2.Model])
], OrganizationsController);
//# sourceMappingURL=organizations.controller.js.map