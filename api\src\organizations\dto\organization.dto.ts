import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsArray, IsBoolean, IsNumber, Min, Max } from 'class-validator';

export class CreateOrganizationDto {
  @ApiProperty({ example: 'Acme Corporation', description: 'Name of the organization' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'A global leader in technology solutions', description: 'Description of the organization', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'active', description: 'Status of the organization', enum: ['active', 'inactive', 'suspended'] })
  @IsEnum(['active', 'inactive', 'suspended'])
  @IsOptional()
  status?: 'active' | 'inactive' | 'suspended';

  @ApiProperty({ example: ['60d0fe4f5311236168a109ca'], description: 'IDs of admin users', required: false })
  @IsArray()
  @IsOptional()
  adminUsers?: string[];
}

export class UpdateOrganizationDto {
  @ApiProperty({ example: 'Acme Corporation', description: 'Name of the organization', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ example: 'A global leader in technology solutions', description: 'Description of the organization', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'active', description: 'Status of the organization', enum: ['active', 'inactive', 'suspended'], required: false })
  @IsEnum(['active', 'inactive', 'suspended'])
  @IsOptional()
  status?: 'active' | 'inactive' | 'suspended';

  @ApiProperty({ example: ['60d0fe4f5311236168a109ca'], description: 'IDs of admin users', required: false })
  @IsArray()
  @IsOptional()
  adminUsers?: string[];

  @ApiProperty({ example: ['60d0fe4f5311236168a109cb'], description: 'IDs of regular users', required: false })
  @IsArray()
  @IsOptional()
  users?: string[];
}

export class UpdateOrganizationBillingDto {
  @ApiProperty({ example: 100, description: 'Credits amount', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  credits?: number;

  @ApiProperty({ example: true, description: 'Auto-recharge enabled', required: false })
  @IsBoolean()
  @IsOptional()
  autoRechargeEnabled?: boolean;

  @ApiProperty({ example: 1.0, description: 'Auto-recharge threshold', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  autoRechargeThreshold?: number;

  @ApiProperty({ example: 10.0, description: 'Auto-recharge amount', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  autoRechargeAmount?: number;

  @ApiProperty({ example: 0.10, description: 'Price per minute for calls', required: false })
  @IsNumber()
  @Min(0.01)
  @IsOptional()
  callPricePerMinute?: number;

  @ApiProperty({ example: 1.0, description: 'Minimum credits threshold to block calls', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  minimumCreditsThreshold?: number;

  @ApiProperty({ example: 500, description: 'Monthly minutes allowance for this organization', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  monthlyMinutesAllowance?: number;

  @ApiProperty({ example: 1, description: 'Day of month (1-28) when monthly credits reset', required: false })
  @IsNumber()
  @Min(1)
  @Max(28)
  @IsOptional()
  monthlyResetDate?: number;
}
