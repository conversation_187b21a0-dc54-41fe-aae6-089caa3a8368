import { WebSocketGateway, WebSocketServer, OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect, SubscribeMessage } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class WebsocketGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;
  private logger: Logger = new Logger('WebsocketGateway');
  private userSocketMap: Map<string, string[]> = new Map();

  afterInit() {
    this.logger.log('WebSocket Gateway initialized');
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);

    // Remove client from userSocketMap
    for (const [userId, sockets] of this.userSocketMap.entries()) {
      const index = sockets.indexOf(client.id);
      if (index !== -1) {
        sockets.splice(index, 1);
        if (sockets.length === 0) {
          this.userSocketMap.delete(userId);
        } else {
          this.userSocketMap.set(userId, sockets);
        }
        break;
      }
    }
  }

  // Register a user's socket connection via WebSocket
  @SubscribeMessage('registerUser')
  handleRegisterUser(client: Socket, payload: { userId: string }) {
    const { userId } = payload;

    if (!userId) {
      this.logger.error('No userId provided for socket registration');
      return { success: false, message: 'No userId provided' };
    }

    if (!this.userSocketMap.has(userId)) {
      this.userSocketMap.set(userId, []);
    }

    const sockets = this.userSocketMap.get(userId);
    if (!sockets.includes(client.id)) {
      sockets.push(client.id);
      this.userSocketMap.set(userId, sockets);
    }

    this.logger.log(`User ${userId} registered with socket ${client.id}`);
    return { success: true, message: 'Socket registered successfully' };
  }

  // Handle ping messages from clients to keep connection alive
  @SubscribeMessage('ping')
  handlePing(client: Socket, payload: any) {
    this.logger.debug(`Received ping from client ${client.id}`);
    return { success: true, timestamp: new Date().toISOString() };
  }

  // Send credit update to a specific user
  sendCreditUpdate(userId: string, credits: number) {
    const sockets = this.userSocketMap.get(userId);

    if (sockets && sockets.length > 0) {
      this.logger.log(`Sending credit update to user ${userId}: ${credits}`);

      // Emit to all sockets associated with this user
      sockets.forEach(socketId => {
        this.server.to(socketId).emit('creditUpdate', { credits });
      });

      return true;
    }

    return false;
  }

  // Send credit update to all users in an organization
  sendOrganizationCreditUpdate(organizationId: string, credits: number) {
    if (!organizationId) {
      this.logger.error('Cannot send organization credit update: organizationId is empty');
      return;
    }

    this.logger.log(`Broadcasting credit update to organization ${organizationId}: ${credits}`);

    // Ensure organizationId is a string
    const orgId = organizationId.toString();

    // Emit to all connected clients
    this.server.emit('organizationCreditUpdate', {
      organizationId: orgId,
      credits: Number(credits.toFixed(2)) // Format to 2 decimal places
    });

    this.logger.log(`Sent organization credit update: ${JSON.stringify({ organizationId: orgId, credits })}`);
  }
}
