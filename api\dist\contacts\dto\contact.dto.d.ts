export declare class ContactDto {
    contactName: string;
    phoneNumber: string;
    customerId?: string;
    lastCall?: Date;
    campaigns?: string[];
    region?: string;
    projectName?: string;
    unitNumber?: string;
    totalPayableAmount?: number;
    pendingPayableAmount?: number;
    dueDate?: Date;
    totalInstallments?: number;
    paymentType?: string;
    pendingInstallments?: number;
    lastPaymentDate?: Date;
    lastPaymentAmount?: number;
    lastPaymentType?: string;
    collectionBucket?: string;
    unitPrice?: number;
    paidAmtIncluding?: number;
    eventDate?: string;
    eventLocation?: string;
    eventTime?: string;
    nameOfRegistrant?: string;
    addedBy?: string;
    createdAt?: Date;
    source?: 'manual' | 'file Upload' | 'CRM' | '-';
}
export declare class UploadContactDto {
    contactName: string;
    customerId: string;
    phoneNumber: string;
    campaignNames: string;
    region: string;
}
