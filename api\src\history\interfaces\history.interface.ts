import { Model } from 'mongoose';
import { Document } from 'mongoose';

export enum Emotions {
  Positive = "Positive",
  Neutral = "Neutral",
  SlightlyPositive = "Slightly Positive",
  SlightlyNegative = "Slightly Negative",
  Negative = "Negative"
}


export interface History extends Document {
  fullName: string;
  mobileNumber: string;
  interest: string;
  timezone?: string;
  callTranscript?: string;
  callSummary?: string;
  callStartTime: Date;
  callEndTime: Date;
  callDuration: string;
  callRoute?: string;
  callPurpose?: string;
  callEndReason: string;
  callCost: string;
  bookedStatus?: string;
  confirmedStatus?: string;
  additionalQuestions?: string;
  recordingUrl: string;
  preferredProject: string;
  preferredLocation: string;
  preferredUnitType: string;
  projectType: string;
  investmentType: string;
  budget: string;
  recentContact: boolean;
  agent: string;
  createdAt: Date;
  updatedAt: Date;
  emotions: Emotions;

  // New fields
  brokenPromise: boolean;
  callBackLanguage: string;
  callBackRequest: boolean;
  claimedPaidAwaitingPOP: boolean;
  doNotCall: boolean;
  followingPaymentPlan: boolean;
  fullyPaid: boolean;
  fullyPaidByPDC: boolean;
  incorrectContactDetails: boolean;
  mortgage: boolean;
  notResponding: boolean;
  notRespondingSOASent: boolean;
  notWillingToPay: boolean;
  popRaised: boolean;
  promiseToPay: boolean;
  promiseToPayPartial: boolean;
  refuseToPay: boolean;
  thirdParty: boolean;
  willingToPay: boolean;
  Response?:  String,
  Channel?:String ,
  GuestRequest?: String ,
  Notes?: String ,

  // Virtual field for total count
  totalCount?: number;
  
  // Internal property for storing the total count (not exposed directly)
  _totalCount?: number;
}

export interface HistoryModel extends Model<History> {
  getTotalCount(): Promise<number>;
}

