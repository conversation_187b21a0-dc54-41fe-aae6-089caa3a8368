// Add this new function to your auth-client.ts file

import { refreshAccessToken } from "./auth-client";

/**
 * Authenticated fetch utility that handles token refresh automatically
 * @param url The URL to fetch
 * @param options Fetch options
 * @returns Response from the fetch call
 */
export async function authFetch(url: string, options: RequestInit = {}): Promise<Response> {
    let accessToken = localStorage.getItem('access_token');
    
    // If no access token is available, try to refresh
    if (!accessToken) {
      const refreshResult = await refreshAccessToken();
      if (!refreshResult.success) {
        throw new Error('No authentication token available');
      }
      accessToken = refreshResult.newAccessToken as string;
    }
  
    // Add authorization header if not already present
    const headers = new Headers(options.headers || {});
    if (!headers.has('Authorization')) {
      headers.set('Authorization', `Bearer ${accessToken}`);
    }
  
    // Make the request with the current token
    const response = await fetch(url, {
      ...options,
      headers
    });
  
    // If unauthorized or forbidden, try to refresh the token and retry the request
    if (response.status === 401 || response.status === 403) {
      console.log('Token expired, attempting refresh...');
      const refreshResult = await refreshAccessToken();
      
      if (!refreshResult.success) {
        console.error('Token refresh failed');
        // Handle auth failure - redirect to login or show message
        if (typeof window !== 'undefined') {
          // Only redirect in browser
          window.location.href = '/login';
        }
        throw new Error('Authentication failed');
      }
      
      // Retry the request with the new token
      console.log('Token refreshed, retrying request...');
      const newHeaders = new Headers(options.headers || {});
      newHeaders.set('Authorization', `Bearer ${refreshResult.newAccessToken as string}`);
      
      return fetch(url, {
        ...options,
        headers: newHeaders
      });
    }
    
    return response;
  }