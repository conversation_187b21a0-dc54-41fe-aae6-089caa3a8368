
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Agent, AgentQueryData } from '@/types/agent.types';


async function fetchAgentData(agentId: string) {
  const token = localStorage.getItem('access_token');
  const [agentRes, phoneRes] = await Promise.all([
    fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents/${agentId}`, {
      headers: { 'Authorization': `Bearer ${token}` },
    }),
    fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/phone-numbers`, {
      headers: { 'Authorization': `Bearer ${token}` },
    })
  ]);

  if (!agentRes.ok || !phoneRes.ok) {
    throw new Error('Failed to fetch agent data');
  }

  const [agentData, phoneData] = await Promise.all([
    agentRes.json(),
    phoneRes.json()
  ]);

  return { agent: agentData, phoneNumbers: phoneData };
}

export function useAgent(agentId?: string) {
  const queryClient = useQueryClient();


  const { data: phoneNumbersData } = useQuery({
    queryKey: ['phone-numbers'],
    queryFn: async () => {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/phone-numbers`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch phone numbers');
      }

      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000,    // 15 minutes
  });

  const { data, isLoading: agentIsLoading, error: agentError} = useQuery({
    queryKey: ['agent', agentId],
    queryFn: () => fetchAgentData(agentId!),
    enabled: !!agentId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000,    // 15 minutes
  });

  const updateAgentMutation = useMutation({
    mutationFn: async (updatedAgent: Agent) => {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error("Authentication required");

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents/${updatedAgent.id}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedAgent),
      });

      if (!response.ok) {
        throw new Error(`Failed to update agent: ${response.status}`);
      }

      return response.json();
    },
    onMutate: async (newAgent) => {
      // Cancel queries
      await queryClient.cancelQueries({ queryKey: ['agent', agentId] });
      await queryClient.cancelQueries({ queryKey: ['agents'] });

      // Cache snapshot
      const previousAgent = queryClient.getQueryData(['agent', agentId]);
      const previousAgents = queryClient.getQueryData(['agents']);

      // Optimistic updates
       queryClient.setQueryData<AgentQueryData>(['agent', agentId], old => ({
        ...(old ?? { phoneNumbers: [] }),
        agent: newAgent
      }));

      queryClient.setQueryData<Agent[]>(['agents'], (old = []) => 
        old.map(agent => agent.id === newAgent.id ? newAgent : agent)
      );

      return { previousAgent, previousAgents };
    },
    onSettled: () => {
      // Refetch to ensure sync
      queryClient.invalidateQueries({ queryKey: ['agent', agentId] });
      queryClient.invalidateQueries({ queryKey: ['agents'] });
    },
  });

  const createAgentMutation = useMutation({
    mutationFn: async (newAgent: Agent) => {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error("Authentication required");

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newAgent),
      });

      if (!response.ok) {
        throw new Error(`Failed to create agent: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate agents list query to include new agent
      queryClient.invalidateQueries({ queryKey: ['agents'] });
    }
  });

  const deleteAgentMutation = useMutation({
    mutationFn: async (agentIdToDelete: string) => {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error("Authentication required");

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents/${agentIdToDelete}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete agent: ${response.status}`);
      }
    },
    onSuccess: (_, deletedAgentId) => {
      // Remove the agent from cache
      queryClient.removeQueries({ queryKey: ['agent', deletedAgentId] });
      
      // Update agents list
      queryClient.setQueryData<Agent[]>(['agents'], (old = []) => 
        old.filter(agent => agent.id !== deletedAgentId)
      );
    }
  });


  const setAgent = (newAgent: Agent) => {
    if (!newAgent) return;
    updateAgentMutation.mutate(newAgent);
  };

  return {
    agent: data?.agent,
    setAgent,
    phoneNumbers: agentId ? data?.phoneNumbers : (phoneNumbersData ?? []),
    agentIsLoading,
    agentError: agentError instanceof Error ? agentError.message : null,
    createAgentMutation,
    updateAgentMutation,
    deleteAgentMutation,
  };
}