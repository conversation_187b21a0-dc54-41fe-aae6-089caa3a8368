"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const billing_service_1 = require("./billing.service");
const create_payment_method_dto_1 = require("./dto/create-payment-method.dto");
const create_payment_intent_dto_1 = require("./dto/create-payment-intent.dto");
const process_payment_dto_1 = require("./dto/process-payment.dto");
const logger_service_1 = require("../logger/logger.service");
let BillingController = class BillingController {
    constructor(billingService, loggerService) {
        this.billingService = billingService;
        this.loggerService = loggerService;
    }
    async createPaymentMethod(req, createPaymentMethodDto) {
        try {
            const userId = req.user.userId;
            const user = await this.billingService.getUserWithOrganization(userId);
            if (!user.organizationId) {
                throw new common_1.HttpException('User does not belong to an organization', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.billingService.createOrganizationPaymentMethod(user.organizationId.toString(), createPaymentMethodDto);
        }
        catch (error) {
            this.loggerService.error('Error creating payment method', error);
            throw new common_1.HttpException(error.message || 'Failed to create payment method', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPaymentMethods(req) {
        try {
            const userId = req.user.userId;
            const user = await this.billingService.getUserWithOrganization(userId);
            if (!user.organizationId) {
                throw new common_1.HttpException('User does not belong to an organization', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.billingService.getOrganizationPaymentMethods(user.organizationId.toString());
        }
        catch (error) {
            this.loggerService.error('Error retrieving payment methods', error);
            throw new common_1.HttpException(error.message || 'Failed to retrieve payment methods', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async setDefaultPaymentMethod(req, id) {
        try {
            const userId = req.user.userId;
            const user = await this.billingService.getUserWithOrganization(userId);
            if (!user.organizationId) {
                throw new common_1.HttpException('User does not belong to an organization', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.billingService.setDefaultOrganizationPaymentMethod(user.organizationId.toString(), id);
        }
        catch (error) {
            this.loggerService.error('Error setting default payment method', error);
            throw new common_1.HttpException(error.message || 'Failed to set default payment method', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removePaymentMethod(req, id) {
        try {
            const userId = req.user.userId;
            const user = await this.billingService.getUserWithOrganization(userId);
            if (!user.organizationId) {
                throw new common_1.HttpException('User does not belong to an organization', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.billingService.removeOrganizationPaymentMethod(user.organizationId.toString(), id);
        }
        catch (error) {
            this.loggerService.error('Error removing payment method', error);
            throw new common_1.HttpException(error.message || 'Failed to remove payment method', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createPaymentIntent(req, createPaymentIntentDto) {
        try {
            const userId = req.user.userId;
            const email = req.user.email;
            const name = req.user.name;
            const user = await this.billingService.getUserWithOrganization(userId);
            if (!user.organizationId) {
                throw new common_1.HttpException('User does not belong to an organization', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.billingService.createOrganizationPaymentIntent(user.organizationId.toString(), userId, email, createPaymentIntentDto, name);
        }
        catch (error) {
            this.loggerService.error('Error creating payment intent', error);
            throw new common_1.HttpException(error.message || 'Failed to create payment intent', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getTransactionHistory(req, page = 1, limit = 10) {
        try {
            const userId = req.user.userId;
            const user = await this.billingService.getUserWithOrganization(userId);
            if (!user.organizationId) {
                throw new common_1.HttpException('User does not belong to an organization', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.billingService.getOrganizationTransactionHistory(user.organizationId.toString(), page, limit);
        }
        catch (error) {
            this.loggerService.error('Error retrieving transaction history', error);
            throw new common_1.HttpException(error.message || 'Failed to retrieve transaction history', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async processPayment(req, processPaymentDto) {
        try {
            const userId = req.user.userId;
            const email = req.user.email;
            const name = req.user.name;
            const user = await this.billingService.getUserWithOrganization(userId);
            if (!user.organizationId) {
                throw new common_1.HttpException('User does not belong to an organization', common_1.HttpStatus.BAD_REQUEST);
            }
            const { paymentMethodId, amount, currency, description, savePaymentMethod, setAsDefault } = processPaymentDto;
            return await this.billingService.processOrganizationPayment(user.organizationId.toString(), userId, email, paymentMethodId, {
                amount,
                currency,
                description,
            }, name, savePaymentMethod, setAsDefault);
        }
        catch (error) {
            this.loggerService.error('Error processing payment', error);
            throw new common_1.HttpException(error.message || 'Failed to process payment', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.BillingController = BillingController;
__decorate([
    (0, common_1.Post)('payment-methods'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new payment method for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment method created successfully' }),
    (0, swagger_1.ApiBody)({ type: create_payment_method_dto_1.CreatePaymentMethodDto }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_payment_method_dto_1.CreatePaymentMethodDto]),
    __metadata("design:returntype", Promise)
], BillingController.prototype, "createPaymentMethod", null);
__decorate([
    (0, common_1.Get)('payment-methods'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all payment methods for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of payment methods' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BillingController.prototype, "getPaymentMethods", null);
__decorate([
    (0, common_1.Post)('payment-methods/:id/default'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Set a payment method as default for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment method set as default' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BillingController.prototype, "setDefaultPaymentMethod", null);
__decorate([
    (0, common_1.Delete)('payment-methods/:id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Remove a payment method from the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment method removed successfully' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BillingController.prototype, "removePaymentMethod", null);
__decorate([
    (0, common_1.Post)('payment-intent'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a payment intent for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment intent created successfully' }),
    (0, swagger_1.ApiBody)({ type: create_payment_intent_dto_1.CreatePaymentIntentDto }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_payment_intent_dto_1.CreatePaymentIntentDto]),
    __metadata("design:returntype", Promise)
], BillingController.prototype, "createPaymentIntent", null);
__decorate([
    (0, common_1.Get)('transactions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get transaction history for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Transaction history' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], BillingController.prototype, "getTransactionHistory", null);
__decorate([
    (0, common_1.Post)('process-payment'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Process a payment directly (server-side) for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment processed successfully' }),
    (0, swagger_1.ApiBody)({ type: process_payment_dto_1.ProcessPaymentDto }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, process_payment_dto_1.ProcessPaymentDto]),
    __metadata("design:returntype", Promise)
], BillingController.prototype, "processPayment", null);
exports.BillingController = BillingController = __decorate([
    (0, swagger_1.ApiTags)('Billing'),
    (0, common_1.Controller)('billing'),
    __metadata("design:paramtypes", [billing_service_1.BillingService,
        logger_service_1.LoggerService])
], BillingController);
//# sourceMappingURL=billing.controller.js.map