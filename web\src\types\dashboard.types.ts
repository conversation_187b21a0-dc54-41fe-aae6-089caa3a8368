
export interface DashboardData {
  callMetrics: {
    totalCalls: number;
    totalMinutes: number;
    averageLength: number;
    connectionRate: number;
    answerRate: number;
  };
  totalCounts: {
    totalCampaigns: number;
    totalScheduledCalls: number;
    totalContacts: number;
  };
  sentiments: {
    positive: number;
    neutral: number;
    negative: number;
  };
  callEndReasons: Array<{
    reason: string;
    count: number;
    percentage: number;
  }>;
  topAgents: Array<{
    id: string;
    name: string;
    avatar: string | null;
    role: string;
    status: string;
    callCount: number;
  }>;
  recentCalls: Array<{
    _id: string;
    fullName: string;
    mobileNumber: string;
    callStartTime: string;
    callEndTime: string;
    callDuration: string;
    callRoute: string;
    callPurpose: string;
    callEndReason: string;
    agent: string;
  }>;
  recentSchedules: Array<{
    _id: string;
    agentId: string;
    contacts: { Name: string; MobileNumber: string }[];
    scheduledTime: string;
    region: string;
    status: string;
    scheduledByName: string;
    scheduledByTimestamp: string;
  }>;
  recentCampaigns: Array<{
    _id: string;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
  }>;
  agentRoles: string[];
}