import { Controller, Get, Patch, Body, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { GlobalSettingsService } from './global-settings.service';
import { UpdateGlobalSettingsDto } from './dto/update-global-settings.dto';

interface RequestWithUser extends Request {
  user: {
    userId: string;
    email: string;
    role: string;
  };
}

@ApiTags('global-settings')
@Controller('global-settings')
export class GlobalSettingsController {
  constructor(private readonly globalSettingsService: GlobalSettingsService) {}

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get global settings' })
  @ApiResponse({ status: 200, description: 'Returns the global settings' })
  async getGlobalSettings() {
    return this.globalSettingsService.getGlobalSettings();
  }

  @Patch()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update global settings' })
  @ApiResponse({ status: 200, description: 'Global settings updated successfully' })
  async updateGlobalSettings(
    @Body() updateGlobalSettingsDto: UpdateGlobalSettingsDto,
    @Req() req: RequestWithUser,
  ) {
    const userId = req.user.userId;
    return this.globalSettingsService.updateGlobalSettings(
      updateGlobalSettingsDto,
      userId,
    );
  }
}
