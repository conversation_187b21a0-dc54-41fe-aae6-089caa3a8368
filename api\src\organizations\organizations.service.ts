import { Injectable, NotFoundException, BadRequestException, forwardRef, Inject, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Organization, OrganizationDocument } from './interfaces/organization.interface';
import { CreateOrganizationDto, UpdateOrganizationDto, UpdateOrganizationBillingDto } from './dto/organization.dto';
import { UsersService } from '../users/users.service';
import { ModuleRef } from '@nestjs/core';
import { WebsocketGateway } from '../websocket/websocket.gateway';
import { CreditGateway } from '../credit/credit.gateway';
import { MonthlyCreditsService } from './monthly-credits.service';
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class OrganizationsService implements OnModuleInit {
  private websocketGateway: WebsocketGateway;
  private creditGateway: CreditGateway;

  constructor(
    @InjectModel('Organization') private organizationModel: Model<OrganizationDocument>,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
    private moduleRef: ModuleRef,
    private monthlyCreditsService: MonthlyCreditsService,
    private notificationsService: NotificationsService,
  ) {
    // We'll initialize the gateways in onModuleInit
  }

  onModuleInit() {
    // Get the WebsocketGateway instance
    this.websocketGateway = this.moduleRef.get(WebsocketGateway, { strict: false });
    // Get the CreditGateway instance
    this.creditGateway = this.moduleRef.get(CreditGateway, { strict: false });
  }

  async create(createOrganizationDto: CreateOrganizationDto): Promise<Organization> {
    const newOrganization = new this.organizationModel({
      ...createOrganizationDto,
      credits: 0,
      autoRechargeEnabled: false,
      autoRechargeThreshold: 1.0,
      autoRechargeAmount: 0,
    });
    const savedOrganization = await newOrganization.save();

    // Initialize monthly credits for the new organization
    await this.monthlyCreditsService.initializeMonthlyCredits(savedOrganization._id.toString());

    return savedOrganization;
  }

  async findAll(): Promise<Organization[]> {
    return this.organizationModel.find().exec();
  }

  async findOne(id: string): Promise<Organization> {
    const organization = await this.organizationModel.findById(id).exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }
    return organization;
  }

  async update(id: string, updateOrganizationDto: UpdateOrganizationDto): Promise<Organization> {
    const organization = await this.organizationModel.findById(id).exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Update organization
    Object.assign(organization, {
      ...updateOrganizationDto,
      updatedAt: new Date()
    });

    return organization.save();
  }

  async updateBilling(id: string, updateBillingDto: UpdateOrganizationBillingDto): Promise<Organization> {
    const organization = await this.organizationModel.findById(id).exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Check if monthlyMinutesAllowance is being updated
    const oldMonthlyAllowance = organization.monthlyMinutesAllowance;
    const newMonthlyAllowance = updateBillingDto.monthlyMinutesAllowance;

    // Update billing information
    Object.assign(organization, {
      ...updateBillingDto,
      updatedAt: new Date()
    });

    // If monthly allowance changed, sync the monthlyFreeCredits
    if (newMonthlyAllowance !== undefined && newMonthlyAllowance !== oldMonthlyAllowance) {
      organization.monthlyFreeCredits = newMonthlyAllowance;
      // Reset usage if allowance increased or reset to 0 if allowance is 0
      if (newMonthlyAllowance === 0) {
        organization.monthlyFreeCreditsUsed = 0;
        organization.usingFreeCredits = false;
      } else if (newMonthlyAllowance > oldMonthlyAllowance) {
        // If allowance increased, don't reset usage but ensure we're using free credits
        organization.usingFreeCredits = true;
      }
      console.log(`Updated monthly allowance for organization ${id} from ${oldMonthlyAllowance} to ${newMonthlyAllowance} minutes`);
    }

    // Save the updated organization
    const updatedOrganization = await organization.save();

    // Log the update
    if (updateBillingDto.credits !== undefined) {
      console.log(`Updated credits for organization ${id} to ${updateBillingDto.credits}`);

      // Get total available credits (free + paid) for consistent notifications
      const availableCredits = await this.monthlyCreditsService.getAvailableCredits(id);

      // Broadcast credit update via WebSocket (using total available credits)
      if (this.websocketGateway) {
        this.websocketGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
      }

      // Also broadcast via CreditGateway
      if (this.creditGateway) {
        this.creditGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
      }

      // Process credit notifications (use total available credits for notification logic)
      const orgForNotification = { ...updatedOrganization.toObject(), credits: availableCredits.totalAvailable };
      await this.notificationsService.processCreditNotifications(orgForNotification);
    }

    return updatedOrganization;
  }

  async addCredits(id: string, amount: number): Promise<Organization> {
    if (amount <= 0) {
      throw new BadRequestException('Credit amount must be positive');
    }

    const organization = await this.organizationModel.findById(id).exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    organization.credits += amount;
    organization.updatedAt = new Date();
    const updatedOrganization = await organization.save();

    // Get total available credits (free + paid) for consistent notifications
    const availableCredits = await this.monthlyCreditsService.getAvailableCredits(id);

    // Broadcast credit update via WebSocket (using total available credits)
    if (this.websocketGateway) {
      this.websocketGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
    }

    // Also broadcast via CreditGateway
    if (this.creditGateway) {
      this.creditGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
    }

    // Process credit notifications (use total available credits for notification logic)
    const orgForNotification = { ...updatedOrganization.toObject(), credits: availableCredits.totalAvailable };
    await this.notificationsService.processCreditNotifications(orgForNotification);

    return updatedOrganization;
  }

  async deductCredits(id: string, amount: number): Promise<Organization> {
    if (amount <= 0) {
      throw new BadRequestException('Credit amount must be positive');
    }

    // Use monthly credits service to handle deduction logic
    const deductionResult = await this.monthlyCreditsService.deductCredits(id, amount);

    if (!deductionResult.success) {
      throw new BadRequestException('Insufficient credits');
    }

    // Get the updated organization
    const updatedOrganization = await this.organizationModel.findById(id).exec();
    if (!updatedOrganization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Broadcast credit update via WebSocket (using total available credits)
    const availableCredits = await this.monthlyCreditsService.getAvailableCredits(id);
    if (this.websocketGateway) {
      this.websocketGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
    }

    // Also broadcast via CreditGateway
    if (this.creditGateway) {
      this.creditGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
    }

    // Process credit notifications (use total available credits for notification logic)
    const orgForNotification = { ...updatedOrganization.toObject(), credits: availableCredits.totalAvailable };
    await this.notificationsService.processCreditNotifications(orgForNotification);

    return updatedOrganization;
  }

  /**
   * Get available credits information for an organization
   */
  async getAvailableCredits(id: string): Promise<{
    freeCreditsRemaining: number;
    paidCredits: number;
    totalAvailable: number;
    usingFreeCredits: boolean;
  }> {
    return this.monthlyCreditsService.getAvailableCredits(id);
  }

  async addUserToOrganization(organizationId: string, userId: string, isAdmin: boolean = false): Promise<Organization> {
    const organization = await this.organizationModel.findById(organizationId).exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${organizationId} not found`);
    }

    // Add user to organization
    if (isAdmin) {
      if (!organization.adminUsers.includes(userId)) {
        organization.adminUsers.push(userId);
      }
    } else {
      if (!organization.users.includes(userId)) {
        organization.users.push(userId);
      }
    }

    // Make sure user is in the users array regardless of admin status
    if (!organization.users.includes(userId)) {
      organization.users.push(userId);
    }

    organization.updatedAt = new Date();
    await organization.save();

    // Update the user's organizationId field
    try {
      await this.usersService.updateUser(userId, { organizationId: organizationId });
      console.log(`Updated organizationId for user ${userId} to ${organizationId}`);
    } catch (error) {
      console.error(`Failed to update organizationId for user ${userId}:`, error);
      // Don't throw here, as we still want to return the updated organization
    }

    return organization;
  }

  async removeUserFromOrganization(organizationId: string, userId: string): Promise<Organization> {
    const organization = await this.organizationModel.findById(organizationId).exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${organizationId} not found`);
    }

    // Remove from both admin and regular users arrays
    organization.adminUsers = organization.adminUsers.filter(id => id.toString() !== userId);
    organization.users = organization.users.filter(id => id.toString() !== userId);

    organization.updatedAt = new Date();
    await organization.save();

    // Clear the user's organizationId field
    try {
      await this.usersService.updateUser(userId, { organizationId: null });
      console.log(`Cleared organizationId for user ${userId}`);
    } catch (error) {
      console.error(`Failed to clear organizationId for user ${userId}:`, error);
      // Don't throw here, as we still want to return the updated organization
    }

    return organization;
  }

  async delete(id: string): Promise<void> {
    const result = await this.organizationModel.deleteOne({ _id: id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }
  }

  async findByUser(userId: string): Promise<Organization[]> {
    return this.organizationModel.find({
      $or: [
        { adminUsers: userId },
        { users: userId }
      ]
    }).exec();
  }

  async findByAdmin(adminId: string): Promise<Organization[]> {
    return this.organizationModel.find({
      adminUsers: adminId
    }).exec();
  }

  async setStripeCustomerId(id: string, stripeCustomerId: string): Promise<Organization> {
    const organization = await this.organizationModel.findById(id).exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    organization.stripeCustomerId = stripeCustomerId;
    organization.updatedAt = new Date();
    return organization.save();
  }

  /**
   * Initialize monthly credits for an existing organization
   */
  async initializeMonthlyCreditsForOrganization(id: string): Promise<Organization> {
    await this.monthlyCreditsService.initializeMonthlyCredits(id);
    return this.findOne(id);
  }

  /**
   * Update organization settings
   */
  async updateSettings(id: string, updateSettingsDto: any): Promise<Organization> {
    const organization = await this.organizationModel.findById(id).exec();
    if (!organization) {
      throw new Error('Organization not found');
    }

    // Update monthly reset date if provided
    if (updateSettingsDto.monthlyResetDate !== undefined) {
      organization.monthlyResetDate = updateSettingsDto.monthlyResetDate;
    }

    // Update email notification settings if provided
    if (updateSettingsDto.fullName !== undefined) {
      organization.fullName = updateSettingsDto.fullName;
    }

    if (updateSettingsDto.email !== undefined) {
      organization.email = updateSettingsDto.email;
    }

    organization.updatedAt = new Date();
    return organization.save();
  }

  /**
   * Update monthly credits for all organizations
   */
  async updateAllOrganizationsMonthlyCredits(newMonthlyMinutesAllowance: number): Promise<void> {
    try {
      // Get all organizations
      const organizations = await this.organizationModel.find({ status: 'active' }).exec();

      console.log(`Updating monthly credits for ${organizations.length} organizations to ${newMonthlyMinutesAllowance} minutes`);

      // Update each organization
      for (const organization of organizations) {
        try {
          // Update the organization's monthly credits
          organization.monthlyFreeCredits = newMonthlyMinutesAllowance;
          // Reset monthly usage and set as using free credits if allowance > 0
          organization.monthlyFreeCreditsUsed = 0;
          organization.usingFreeCredits = newMonthlyMinutesAllowance > 0;
          organization.lastMonthlyReset = new Date();
          organization.updatedAt = new Date();

          await organization.save();

          console.log(`Updated monthly credits for organization ${organization.name}: ${newMonthlyMinutesAllowance} minutes`);

          // Broadcast credit update via WebSocket
          const availableCredits = await this.monthlyCreditsService.getAvailableCredits(organization._id.toString());
          if (this.websocketGateway) {
            this.websocketGateway.sendOrganizationCreditUpdate(organization._id.toString(), availableCredits.totalAvailable);
          }

          // Also broadcast via CreditGateway
          if (this.creditGateway) {
            this.creditGateway.sendOrganizationCreditUpdate(organization._id.toString(), availableCredits.totalAvailable);
          }

        } catch (error) {
          console.error(`Error updating monthly credits for organization ${organization.name}:`, error);
        }
      }

      console.log('Completed updating monthly credits for all organizations');
    } catch (error) {
      console.error('Error in updateAllOrganizationsMonthlyCredits:', error);
      throw error;
    }
  }
}
