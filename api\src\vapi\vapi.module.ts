import { forwardRef, Module } from '@nestjs/common';
import { VapiController } from './vapi.controller';
import { VapiService } from './vapi.service';
import { HistoryModule } from 'src/history/history.module';
import { LoggerModule } from 'src/logger/logger.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ContactsModule } from 'src/contacts/contacts.module';
import { ScheduledCallModule } from 'src/scheduled-call/scheduled-call.module';
import { CampaignModule } from 'src/campaign/campaign.module';
import { AgentModule } from 'src/agent/agent.module';
import { UsersModule } from 'src/users/users.module';
import { GlobalSettingsModule } from 'src/global-settings/global-settings.module';
import { OrganizationsModule } from 'src/organizations/organizations.module';
import { HistorySchema } from 'src/history/schemas/history.schema';
@Module({
  imports: [
    HistoryModule,
    LoggerModule,
    ContactsModule,
    MongooseModule.forFeature([{ name: 'History', schema: HistorySchema }]),
    MongooseModule,
    forwardRef(() => ScheduledCallModule),
    CampaignModule,
    AgentModule,
    UsersModule,
    GlobalSettingsModule,
    forwardRef(() => OrganizationsModule)
  ],
  controllers: [VapiController],
  providers: [VapiService],
  exports: [VapiService]
})
export class VapiModule {}
