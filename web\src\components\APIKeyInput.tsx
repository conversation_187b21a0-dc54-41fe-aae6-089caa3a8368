"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CheckCircle, Loader2 } from "lucide-react";
import { useState } from "react";

interface APIKeyInputProps {
  label: string;
  description?: string;
  value: string;
  onChange: (value: string) => void;
  onTest?: () => Promise<boolean>;
  placeholder?: string;
}

export function APIKeyInput({
  label,
  description,
  value,
  onChange,
  onTest,
  placeholder = "Enter API key"
}: APIKeyInputProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<boolean | null>(null);

  const handleTest = async () => {
    if (!onTest || !value.trim()) return;
    
    setIsTesting(true);
    setTestResult(null);
    
    try {
      const result = await onTest();
      setTestResult(result);
    } catch (error) {
      setTestResult(false);
      console.error("API test failed:", error);
    } finally {
      setIsTesting(false);
    }
    
    // Reset test result after 3 seconds
    setTimeout(() => setTestResult(null), 3000);
  };

  return (
    <>  
    <div className="space-y-2">
      <div className="flex items-baseline justify-between">
        <Label htmlFor={label.replace(/\s+/g, '-').toLowerCase()}>{label}</Label>
        {description && (
          <span className="text-xs text-muted-foreground">{description}</span>
        )}
      </div>
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Input
            id={label.replace(/\s+/g, '-').toLowerCase()}
            type={isVisible ? "text" : "password"}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            className="pr-20"
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-0 text-xs font-normal"
            onClick={() => setIsVisible(!isVisible)}
          >
            {isVisible ? "Hide" : "Show"}
          </Button>
        </div>
        {onTest && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleTest}
            disabled={isTesting || !value.trim()}
            className="flex items-center gap-1 whitespace-nowrap"
          >
            {isTesting ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : testResult === true ? (
              <CheckCircle className="h-3 w-3 text-green-500" />
            ) : testResult === false ? (
              <span className="text-red-500">Failed</span>
            ) : (
              "Test"
            )}
          </Button>
        )}
      </div>
    </div>
    </>
  );
}