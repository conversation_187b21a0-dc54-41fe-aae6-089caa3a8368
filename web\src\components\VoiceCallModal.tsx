/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Mi<PERSON>, MicOff, Phone, PhoneOff, Volume2 } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

// Define Agent type
type Agent = {
  id: string;
  name: string;
  role: string;
  description?: string;
  status: "online" | "offline";
  type: "text" | "voice" | "both";
  messagesHandled: number;
  lastActive: string;
  model: string;
  avatar: string;
  emoji: string;
};

type Message = {
  role: 'user' | 'assistant';
  content: string;
};

interface VoiceCallModalProps {
  agent: Agent;
  isOpen: boolean;
  onClose: () => void;
}

// Mock brain service for development
const brain = {
  process_voice: async ({ text, history }: { text: string; history: Message[] }) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      json: async () => ({
        response: `This is a simulated response to: "${text}". In a real app, this would come from your AI service.`
      })
    };
  }
};

// Add type definition for SpeechRecognition that might be missing
// declare global {
//   interface Window {
//     SpeechRecognition: typeof SpeechRecognition;
//     webkitSpeechRecognition: typeof SpeechRecognition;
//   }
// }

export function VoiceCallModal({ agent, isOpen, onClose }: VoiceCallModalProps) {
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(true);
  const [isCallActive, setIsCallActive] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentMessage, setCurrentMessage] = useState<Message | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const recognitionRef = useRef<any>(null);
//   const timerRef = useRef<NodeJS.Timeout>();

 

  // Format seconds to mm:ss
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };



  const handleEndCall = () => {
    setIsCallActive(false);
    setCallDuration(0);
    setCurrentMessage(null);
    setMessages([]);
  
    onClose();
  };

  const toggleMicrophone = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
    setIsMuted(!isMuted);
  };

  const startListening = () => {
    if (recognitionRef.current && isCallActive) {
      setIsListening(true);
      recognitionRef.current.start();
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      setIsListening(false);
      recognitionRef.current.stop();
    }
  };

  const processVoiceInput = async (text: string) => {
    try {
      // Show user message
      const userMessage = { role: 'user' as const, content: text };
      setCurrentMessage(userMessage);
      const newMessages = [...messages, userMessage];
      setMessages(newMessages);
      
      const response = await brain.process_voice({ 
        text,
        history: newMessages
      });
      
      const responseData = await response.json();
      
      // Show assistant response
      const assistantMessage = { role: 'assistant' as const, content: responseData.response };
      setCurrentMessage(assistantMessage);
      setMessages(prev => [...prev, assistantMessage]);
      
      if (isSpeakerOn) {
        speakResponse(responseData.response);
      }
    } catch (error) {
      console.error("Error processing voice input:", error);
      const errorMessage = { 
        role: 'assistant' as const, 
        content: "Sorry, I encountered an error processing your request." 
      };
      setCurrentMessage(errorMessage);
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const speakResponse = (text: string) => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => {
        setIsSpeaking(false);
        setCurrentMessage(null);
      };
      utterance.onerror = () => setIsSpeaking(false);
      window.speechSynthesis.speak(utterance);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) handleEndCall();
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Voice Call with {agent.name}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center p-6 space-y-6">
          {/* Agent Avatar */}
          <div className="relative">
            <div className={`h-24 w-24 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-4xl ${
              isCallActive ? 'ring-2 ring-green-500 ring-offset-2 dark:ring-offset-gray-900' : ''
            }`}>
              <Avatar className="h-16 w-16 bg-white/20 border border-white/30">
              {agent.avatar && agent.avatar.startsWith('http') ? (
                <AvatarImage src={agent.avatar} alt={agent.name} />
              ) : (
                <AvatarFallback className="text-lg bg-transparent">
                  {agent.emoji || agent.name.charAt(0)}
                </AvatarFallback>
              )}
            </Avatar>
            </div>
            {isCallActive && (
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full">
                  {formatDuration(callDuration)}
                </span>
              </div>
            )}
          </div>

          {/* Agent Info */}
          <div className="text-center">
            <h3 className="text-xl font-semibold">{agent.name}</h3>
            <p className="text-gray-600 dark:text-gray-400">{agent.role}</p>
          </div>

          {/* Current Message Display */}
          <div className="w-full max-h-32 overflow-y-auto flex items-center justify-center px-4">
            {currentMessage && (
              <div className={`text-center transition-opacity duration-300 py-2 ${
                currentMessage ? 'opacity-100' : 'opacity-0'
              }`}>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {currentMessage.role === 'user' ? 'You' : agent.name}
                </p>
                <p className="text-lg break-words">{currentMessage.content}</p>
              </div>
            )}
          </div>

          {/* Call Controls */}
          <div className="flex items-center justify-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              className={`rounded-full h-12 w-12 ${
                isListening ? "bg-red-50 text-red-500 dark:bg-red-900 dark:text-red-300 ring-2 ring-red-500" : ""
              }`}
              onClick={toggleMicrophone}
              disabled={!isCallActive || !recognitionRef.current}
            >
              {isListening ? (
                <MicOff className="h-5 w-5" />
              ) : (
                <Mic className="h-5 w-5" />
              )}
            </Button>

            <Button
              variant={isCallActive ? "destructive" : "default"}
              size="icon"
              className="rounded-full h-16 w-16"
            >
              {isCallActive ? (
                <PhoneOff className="h-6 w-6" />
              ) : (
                <Phone className="h-6 w-6" />
              )}
            </Button>

            <Button
              variant="outline"
              size="icon"
              className={`rounded-full h-12 w-12 ${
                !isSpeakerOn ? "bg-red-50 text-red-500 dark:bg-red-900 dark:text-red-300" : ""
              } ${
                isSpeaking ? "ring-2 ring-blue-500 animate-pulse" : ""
              }`}
              onClick={() => setIsSpeakerOn(!isSpeakerOn)}
              disabled={!isCallActive}
            >
              <Volume2 className="h-5 w-5" />
            </Button>
          </div>

          {/* Call Status */}
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {isCallActive
              ? isListening
                ? "Listening..."
                : isSpeaking
                ? "Speaking..."
                : "Click the microphone to speak"
              : "Click the phone button to start the call"}
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default VoiceCallModal;