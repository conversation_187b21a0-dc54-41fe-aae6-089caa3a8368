"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalSettingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const global_settings_service_1 = require("./global-settings.service");
const update_global_settings_dto_1 = require("./dto/update-global-settings.dto");
let GlobalSettingsController = class GlobalSettingsController {
    constructor(globalSettingsService) {
        this.globalSettingsService = globalSettingsService;
    }
    async getGlobalSettings() {
        return this.globalSettingsService.getGlobalSettings();
    }
    async updateGlobalSettings(updateGlobalSettingsDto, req) {
        const userId = req.user.userId;
        return this.globalSettingsService.updateGlobalSettings(updateGlobalSettingsDto, userId);
    }
};
exports.GlobalSettingsController = GlobalSettingsController;
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get global settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns the global settings' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GlobalSettingsController.prototype, "getGlobalSettings", null);
__decorate([
    (0, common_1.Patch)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update global settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Global settings updated successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_global_settings_dto_1.UpdateGlobalSettingsDto, Object]),
    __metadata("design:returntype", Promise)
], GlobalSettingsController.prototype, "updateGlobalSettings", null);
exports.GlobalSettingsController = GlobalSettingsController = __decorate([
    (0, swagger_1.ApiTags)('global-settings'),
    (0, common_1.Controller)('global-settings'),
    __metadata("design:paramtypes", [global_settings_service_1.GlobalSettingsService])
], GlobalSettingsController);
//# sourceMappingURL=global-settings.controller.js.map