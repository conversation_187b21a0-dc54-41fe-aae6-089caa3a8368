const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const numberToWords = require('number-to-words');
const wordsToNumbers = require('words-to-numbers').default;
const router = express.Router();
const app = express();

const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('combined')); // For logging

// Log each request
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl}`);
  if (req.method === 'POST' || req.method === 'PUT') {
    console.log('Body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// Digit word mapping for simpler sequences
const digitWordMap = {
  'zero': '0',
  'one': '1',
  'two': '2',
  'three': '3',
  'four': '4',
  'five': '5',
  'six': '6',
  'seven': '7',
  'eight': '8',
  'nine': '9'
};

// Helper functions
const cleanInput = (text) => {
  return String(text)
    .replace(',', '')
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' ');
};

const isDigitSequence = (text) => {
  const words = text.split(' ');
  return words.every(word => word in digitWordMap);
};

const convertDigitSequence = (text) => {
  const words = text.split(' ');
  const digits = words.map(word => digitWordMap[word]);
  return parseInt(digits.join(''));
};

// Format number endpoint
router.post('/format_number', (req, res) => {
  try {
    // Extract amount and currency from nested structure
    const { message } = req.body;
    const toolCall = message.toolCalls[0];
    const { amount, currency } = toolCall.function.arguments;
    const toolCallId = toolCall.id;

    const cleanedAmount = cleanInput(amount);
    let numericAmount;

    // Handle special digit sequences like "three zero zero"
    if (isDigitSequence(cleanedAmount)) {
      numericAmount = convertDigitSequence(cleanedAmount);
    } else {
      // Try parsing as a spelled-out number
      try {
        numericAmount = wordsToNumbers(cleanedAmount);
      } catch {
        // Fallback: try parsing as integer
        try {
          numericAmount = parseInt(cleanedAmount);
        } catch {
          throw new Error('Invalid amount format');
        }
      }
    }

    // Convert numeric amount to words
    let amountInWords = numberToWords.toWords(numericAmount)
      .replace(/-/g, ' ');

    // Append currency appropriately
    let speech;
    const currencyLower = currency.toLowerCase();
    
    if (['usd', 'dollar', 'dollars'].includes(currencyLower)) {
      speech = `${amountInWords} dollars`;
    } else if (['aed', 'dirham', 'dirhams'].includes(currencyLower)) {
      speech = `${amountInWords} dirhams`;
    } else if (['gbp', 'pound', 'pounds'].includes(currencyLower)) {
      speech = `${amountInWords} pounds`;
    } else {
      speech = `${amountInWords} ${currency}`;
    }

    // Log and return the result
    console.log(`Result for tool call ${toolCallId}:`, { speech_version: speech });

    res.json({
      results: [
        {
          toolCallId,
          result: {
            speech_version: speech
          }
        }
      ]
    });

  } catch (error) {
    console.error('Error processing request:', error);
    res.status(400).json({ error: error.message || 'Invalid input format' });
  }
});

app.use('/call', router);
// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

