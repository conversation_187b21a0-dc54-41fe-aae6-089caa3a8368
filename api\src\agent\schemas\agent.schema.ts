import { Schema, Types } from 'mongoose';

export const AgentSchema = new Schema({
  id: { type: String },
  name: { type: String, required: false, default: 'Agent' },
  role: { type: String, default: 'assistant' },
  avatar: { type: String, default: null },
  status: { type: String, default: 'active' },
  localPhoneNumberId: { type: String, default: '' },
  internationalPhoneNumberId: { type: String, default: '' },
  voice: {
    model: { type: String, default: '' },
    style: { type: Number , default: 0 },
    voiceId: { type: String, default: '' },
    provider: { type: String, default: '' },
    stability: { type: Number, default: 0.5 },
    similarityBoost: { type: Number, },
    useSpeakerBoost: { type: Boolean },
    inputMinCharacters: { type: Number },
    inputPunctuationBoundaries: { type: [String] }
  },
  
  createdAt: { type: Date },
  updatedAt: { type: Date, default: Date.now },
  
  model: {
    model: { type: String, default: 'gpt-4o' },
    messages: [{ 
      role: { type: String, default: '' },
      content: { type: String, default: '' },
      _id: false
    }],
    provider: { type: String },
    maxTokens: { type: Number },
    temperature: { type: Number },
    knowledgeBaseId: { type: String },
    emotionRecognitionEnabled: { type: Boolean }
  },
  
  recordingEnabled: { type: Boolean },
  firstMessage: { type: String },
  voicemailMessage: { type: String, default: '' },
  endCallFunctionEnabled: { type: Boolean },
  endCallMessage: { type: String },
  
  transcriber: {
    model: { type: String, default: 'nova-3' },
    language: { type: String, default: 'en' },
    numerals: { type: Boolean, default: false },
    provider: { type: String, default: 'deepgram' },
    confidenceThreshold: { type: Number, default: 0.4 }
  },
  
  clientMessages: { type: [String] },
  serverMessages: { type: [String] },
  server: {
    url: { type: String },
  },
  endCallPhrases: { type: [String] },
  hipaaEnabled: { type: Boolean },
  maxDurationSeconds: { type: Number },
  backgroundSound: { type: String },
  backchannelingEnabled: { type: Boolean },
  
  analysisPlan: {
    summaryPrompt: { type: String },
    structuredDataPrompt: { type: String },
    structuredDataSchema: { type: Object },
    structuredDataRequestTimeoutSeconds: { type: Number },
    successEvaluationPrompt: { type: String },
    successEvaluationRubric: { type: String }
  },
  
  voicemailDetection: {
    provider: { type: String }
  },
  
  backgroundDenoisingEnabled: { type: Boolean },
  
  messagePlan: {
    idleMessages: { type: [String] },
    idleMessageMaxSpokenCount: { type: Number },
    idleTimeoutSeconds: { type: Number }
  },
  
  startSpeakingPlan: {
    waitSeconds: { type: Number }
  },
  
  stopSpeakingPlan: {
    numWords: { type: Number }
  },
  
  compliancePlan: {
    hipaaEnabled: { type: Boolean },
    pciEnabled: { type: Boolean }
  },
  
  isServerUrlSecretSet: { type: Boolean }
}, 
{
  strict: false,
  timestamps: true
});

AgentSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});


AgentSchema.index({ role: 1 });