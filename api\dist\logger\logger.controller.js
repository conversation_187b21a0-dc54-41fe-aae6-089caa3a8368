"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogsController = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const logger_service_1 = require("./logger.service");
let LogsController = class LogsController {
    constructor(loggerService, logModel) {
        this.loggerService = loggerService;
        this.logModel = logModel;
    }
    async getAllLogs(level, search, startDate, endDate, page = 1, limit = 50) {
        const filter = {};
        if (level && ['INFO', 'WARN', 'ERROR'].includes(level.toUpperCase())) {
            filter.level = level.toUpperCase();
        }
        if (startDate || endDate) {
            filter.timestamp = {};
            if (startDate) {
                filter.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                const endDateTime = new Date(endDate);
                endDateTime.setHours(23, 59, 59, 999);
                filter.timestamp.$lte = endDateTime;
            }
        }
        if (search) {
            filter.$or = [
                { message: { $regex: search, $options: 'i' } },
                { trace: { $regex: search, $options: 'i' } }
            ];
        }
        const skip = (page - 1) * limit;
        const logs = await this.logModel.find(filter)
            .sort({ timestamp: -1 })
            .skip(skip)
            .limit(limit)
            .exec();
        const total = await this.logModel.countDocuments(filter).exec();
        const totalPages = Math.ceil(total / limit);
        return {
            logs,
            total,
            page,
            totalPages
        };
    }
    async cleanupOldLogs() {
        const deletedCount = await this.loggerService.deleteOldLogs();
        return {
            message: `Successfully deleted ${deletedCount} logs older than 4 days`,
            deletedCount
        };
    }
    async addLog(body) {
        const { level, message, trace } = body;
        if (!level || !message) {
            throw new common_1.HttpException("Missing required fields: level and message are required.", common_1.HttpStatus.BAD_REQUEST);
        }
        switch (level.toUpperCase()) {
            case "INFO":
                await this.loggerService.log(message);
                break;
            case "WARN":
                await this.loggerService.warn(message);
                break;
            case "ERROR":
                await this.loggerService.error(message, trace);
                break;
            default:
                throw new common_1.HttpException("Invalid log level provided. Use INFO, WARN, or ERROR.", common_1.HttpStatus.BAD_REQUEST);
        }
        return { message: "Log entry created successfully" };
    }
};
exports.LogsController = LogsController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('level')),
    __param(1, (0, common_1.Query)('search')),
    __param(2, (0, common_1.Query)('startDate')),
    __param(3, (0, common_1.Query)('endDate')),
    __param(4, (0, common_1.Query)('page')),
    __param(5, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Number, Number]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "getAllLogs", null);
__decorate([
    (0, common_1.Get)('cleanup'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "cleanupOldLogs", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "addLog", null);
exports.LogsController = LogsController = __decorate([
    (0, common_1.Controller)("logs"),
    __param(1, (0, mongoose_1.InjectModel)("Log")),
    __metadata("design:paramtypes", [logger_service_1.LoggerService,
        mongoose_2.Model])
], LogsController);
//# sourceMappingURL=logger.controller.js.map