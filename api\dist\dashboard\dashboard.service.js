"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DashboardService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const schedule_1 = require("@nestjs/schedule");
let DashboardService = DashboardService_1 = class DashboardService {
    constructor(historyModel, scheduledCallModel, campaignModel, agentModel, contactModel, dashboardStatsModel) {
        this.historyModel = historyModel;
        this.scheduledCallModel = scheduledCallModel;
        this.campaignModel = campaignModel;
        this.agentModel = agentModel;
        this.contactModel = contactModel;
        this.dashboardStatsModel = dashboardStatsModel;
        this.logger = new common_1.Logger(DashboardService_1.name);
    }
    async updateStats() {
        try {
            const timeRanges = ['all', '7', '14', '30', '90'];
            const allAgentRoles = await this.getAllAgentRoles();
            const agentTypes = ['all', ...allAgentRoles];
            for (const timeRange of timeRanges) {
                for (const agentType of agentTypes) {
                    await this.updateStatsForCombination(timeRange, agentType);
                }
            }
        }
        catch (error) {
            this.logger.error('Error updating dashboard statistics:', error);
        }
    }
    async getLatestStats(timeRange, agentType) {
        const stats = await this.dashboardStatsModel.findOne({
            dateRange: timeRange,
            agentType,
        }).sort({ lastUpdated: -1 }).exec();
        if (!stats || Date.now() - stats.lastUpdated.getTime() > 2 * 60 * 60 * 1000) {
            await this.updateStatsForCombination(timeRange, agentType);
            return this.dashboardStatsModel.findOne({
                dateRange: timeRange,
                agentType,
            }).exec();
        }
        return stats;
    }
    async updateStatsForCombination(timeRange, agentType) {
        try {
            const metrics = await this.getDashboardMetrics(timeRange, agentType, false);
            await this.dashboardStatsModel.findOneAndUpdate({ dateRange: timeRange, agentType }, {
                $set: {
                    ...metrics,
                    lastUpdated: new Date(),
                },
            }, { upsert: true, new: true });
        }
        catch (error) {
            this.logger.error(`Error updating stats for timeRange: ${timeRange}, agentType: ${agentType}`, error);
        }
    }
    async clearCachedStats() {
        try {
            const result = await this.dashboardStatsModel.deleteMany({}).exec();
        }
        catch (error) {
            throw error;
        }
    }
    async getDashboardMetrics(timeRange, agentType, fromCache = true) {
        try {
            const allAgentRoles = await this.agentModel.distinct('role');
            const normalizedAgentType = agentType === 'all' ? 'all' :
                allAgentRoles.find(role => role.toLowerCase() === agentType.toLowerCase());
            if (fromCache) {
                const cachedStats = await this.dashboardStatsModel.findOne({
                    dateRange: timeRange,
                    agentType: normalizedAgentType || agentType
                }).sort({ lastUpdated: -1 }).exec();
                if (cachedStats &&
                    Date.now() - cachedStats.lastUpdated.getTime() <= 2 * 60 * 60 * 1000) {
                    return cachedStats;
                }
            }
            const dateFilter = this.getDateFilter(timeRange);
            const agents = await this.agentModel.find(normalizedAgentType && normalizedAgentType !== 'all' ? { role: normalizedAgentType } : {}).exec();
            const agentIds = agents.map(agent => agent.id);
            const agentNames = agents.map(agent => agent.name);
            const callHistoryFilter = {};
            if (dateFilter) {
                callHistoryFilter.callStartTime = dateFilter;
            }
            if (normalizedAgentType && normalizedAgentType !== 'all') {
                callHistoryFilter.agent = { $in: agentNames };
            }
            const callHistory = await this.historyModel
                .find(callHistoryFilter)
                .sort({ callStartTime: -1 })
                .exec();
            const recentCalls = await this.historyModel
                .find(callHistoryFilter)
                .sort({ callStartTime: -1 })
                .limit(3)
                .exec();
            const scheduledCallsFilter = {};
            if (agentType !== 'all') {
                scheduledCallsFilter.agentId = { $in: agentIds };
            }
            const campaignFilter = {};
            if (agentType !== 'all') {
                campaignFilter['agentId'] = { $in: agentIds };
            }
            const recentSchedules = await this.scheduledCallModel
                .find(scheduledCallsFilter)
                .sort({ scheduledTime: 1 })
                .limit(3)
                .exec();
            const recentCampaigns = await this.campaignModel
                .find(campaignFilter)
                .sort({ startDate: -1 })
                .limit(3)
                .exec();
            const callMetrics = this.calculateCallMetrics(callHistory);
            const totalCounts = await this.calculateTotalCounts(normalizedAgentType || agentType, agentIds, agentNames);
            const callEndReasons = this.calculateCallEndReasons(callHistory);
            const topAgents = this.calculateTopAgents(callHistory, agents);
            const sentiments = this.calculateSentiments(callHistory);
            const metrics = {
                callMetrics,
                totalCounts,
                sentiments,
                callEndReasons,
                agentRoles: allAgentRoles,
                topAgents,
                recentCalls,
                recentSchedules,
                recentCampaigns,
            };
            await this.dashboardStatsModel.deleteMany({
                agentType: { $nin: [...allAgentRoles, 'all'] }
            });
            await this.dashboardStatsModel.findOneAndUpdate({ dateRange: timeRange, agentType }, {
                $set: {
                    ...metrics,
                    lastUpdated: new Date(),
                },
            }, { upsert: true, new: true });
            return metrics;
        }
        catch (error) {
            throw new Error(`Error fetching dashboard metrics: ${error.message}`);
        }
    }
    async getAllAgentRoles() {
        return this.agentModel.distinct('role').exec();
    }
    async calculateTotalCounts(agentType, agentIds, agentNames) {
        const campaignFilter = {};
        if (agentType !== 'all') {
            campaignFilter['agentId'] = { $in: agentIds };
        }
        const scheduledCallsFilter = {};
        if (agentType !== 'all') {
            scheduledCallsFilter['agentId'] = { $in: agentIds };
        }
        const historyFilter = {};
        if (agentType !== 'all') {
            historyFilter['agent'] = { $in: agentNames };
        }
        const totalCampaigns = await this.campaignModel.countDocuments(campaignFilter).exec();
        const totalScheduledCalls = await this.scheduledCallModel.countDocuments(scheduledCallsFilter).exec();
        const totalContacts = await this.contactModel.countDocuments().exec();
        const totalCalls = await this.historyModel.countDocuments(historyFilter).exec();
        return {
            totalCampaigns,
            totalScheduledCalls,
            totalContacts,
            totalCalls,
        };
    }
    getDateFilter(timeRange) {
        if (timeRange === 'all') {
            return null;
        }
        const days = parseInt(timeRange, 10) || 7;
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        return { $gte: startDate };
    }
    calculateCallMetrics(callHistory) {
        const totalCalls = callHistory.length;
        const totalMinutes = callHistory.reduce((total, call) => {
            if (call.callDuration) {
                const durationMs = typeof call.callDuration === 'string'
                    ? parseInt(call.callDuration)
                    : call.callDuration;
                return total + (durationMs / (1000 * 60));
            }
            return total;
        }, 0);
        const callsWithDuration = callHistory.filter(call => call.callDuration);
        let averageLength = 0;
        if (callsWithDuration.length > 0) {
            const totalDurationMs = callsWithDuration.reduce((total, call) => {
                const durationMs = typeof call.callDuration === 'string'
                    ? parseInt(call.callDuration)
                    : call.callDuration;
                return total + durationMs;
            }, 0);
            averageLength = totalDurationMs / callsWithDuration.length;
        }
        const connectedCalls = callHistory.filter(call => {
            if (call.callDuration) {
                const durationMs = typeof call.callDuration === 'string'
                    ? parseInt(call.callDuration)
                    : call.callDuration;
                return durationMs > 50000;
            }
            return false;
        }).length;
        const connectionRate = totalCalls > 0 ? Math.round((connectedCalls / totalCalls) * 100) : 0;
        const answeredCalls = callHistory.filter(call => {
            const reason = call.callEndReason?.toLowerCase() || '';
            return reason.includes('customer-ended-call') || reason.includes('assistant-ended-call');
        }).length;
        const answerRate = totalCalls > 0 ? Math.round((answeredCalls / totalCalls) * 100) : 0;
        return {
            totalCalls,
            totalMinutes: Math.round(totalMinutes * 100) / 100,
            averageLength,
            connectionRate,
            answerRate,
        };
    }
    calculateCallEndReasons(callHistory) {
        if (callHistory.length === 0) {
            return [];
        }
        const reasonCounts = callHistory.reduce((acc, call) => {
            const reason = call.callEndReason || 'unknown';
            acc[reason] = (acc[reason] || 0) + 1;
            return acc;
        }, {});
        const total = callHistory.length;
        const reasonsArray = Object.entries(reasonCounts).map(([reason, count]) => {
            const percentage = Math.round((count / total) * 100);
            return { reason, count, percentage };
        });
        return reasonsArray.sort((a, b) => b.percentage - a.percentage);
    }
    calculateTopAgents(callHistory, agents) {
        const agentCallCounts = callHistory.reduce((acc, call) => {
            if (call.agent) {
                acc[call.agent] = (acc[call.agent] || 0) + 1;
            }
            return acc;
        }, {});
        const topAgents = agents
            .map(agent => {
            const callCount = agentCallCounts[agent.name] || 0;
            return {
                id: agent.id,
                name: agent.name,
                avatar: agent.avatar,
                role: agent.role,
                status: agent.status,
                callCount,
            };
        })
            .sort((a, b) => b.callCount - a.callCount)
            .slice(0, 5);
        return topAgents;
    }
    calculateSentiments(callHistory) {
        const sentimentCounts = callHistory.reduce((acc, call) => {
            const emotion = call.emotions || 'Neutral';
            let sentiment = 'neutral';
            if (emotion === 'Positive' || emotion === 'Slightly Positive') {
                sentiment = 'positive';
            }
            else if (emotion === 'Negative' || emotion === 'Slightly Negative') {
                sentiment = 'negative';
            }
            acc[sentiment] = (acc[sentiment] || 0) + 1;
            return acc;
        }, { positive: 0, neutral: 0, negative: 0 });
        const total = callHistory.length || 1;
        return {
            positive: Math.round((sentimentCounts.positive / total) * 100),
            neutral: Math.round((sentimentCounts.neutral / total) * 100),
            negative: Math.round((sentimentCounts.negative / total) * 100),
        };
    }
};
exports.DashboardService = DashboardService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DashboardService.prototype, "updateStats", null);
exports.DashboardService = DashboardService = DashboardService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('History')),
    __param(1, (0, mongoose_1.InjectModel)('ScheduledCall')),
    __param(2, (0, mongoose_1.InjectModel)('Campaign')),
    __param(3, (0, mongoose_1.InjectModel)('Agent')),
    __param(4, (0, mongoose_1.InjectModel)('Contact')),
    __param(5, (0, mongoose_1.InjectModel)('DashboardStats')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model])
], DashboardService);
//# sourceMappingURL=dashboard.service.js.map