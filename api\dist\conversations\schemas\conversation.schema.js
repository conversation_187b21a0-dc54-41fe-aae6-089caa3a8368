"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationSchema = exports.MessageSchema = void 0;
const mongoose_1 = require("mongoose");
exports.MessageSchema = new mongoose_1.Schema({
    role: { type: String, enum: ['user', 'assistant'], required: true },
    content: { type: String, required: true },
    timestamp: { type: Date, default: Date.now },
});
exports.ConversationSchema = new mongoose_1.Schema({
    agentId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Agent', required: true },
    userId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
    type: { type: String, enum: ['chat', 'call'], required: true },
    status: { type: String, enum: ['active', 'ended'], default: 'active' },
    messages: { type: [exports.MessageSchema], default: [] },
    startedAt: { type: Date, default: Date.now },
    endedAt: { type: Date },
});
//# sourceMappingURL=conversation.schema.js.map