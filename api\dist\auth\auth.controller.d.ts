import { AuthService } from './auth.service';
import { FullUserType } from './interceptors/user-redaction.interceptor';
import { LoginDto } from './dto/login.dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    getLoggedInUser(user: FullUserType): Promise<FullUserType>;
    login(loginDto: LoginDto): Promise<{
        access_token: string;
        refresh_token: string;
    }>;
    refresh(body: {
        refreshToken: string;
    }): Promise<{
        access_token: string;
    }>;
}
