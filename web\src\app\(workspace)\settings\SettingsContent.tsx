
"use client";

import { APISettings } from "@/components/settingscomponents/APISettings";
import { GeneralSettings } from "@/components/settingscomponents/GeneralSettings";
import LogsSettings from "@/components/settingscomponents/LogsSettings";
import { VoiceSettings } from "@/components/settingscomponents/VoiceSettings";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { authFetch } from "@/lib/authFetch";
import { useState, useEffect } from "react";

const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

interface UserInfo {
  userId: string;
  email: string;
  role: string;
}

export default function SettingsContent() {
  const [activeTab, setActiveTab] = useState("general");
  const [user, setUser] = useState<UserInfo | null>(null);


  useEffect(() => {
      async function fetchUserData() {
          // Use authFetch instead of regular fetch
          const response = await authFetch(`${API_BASE_URL}/api/auth/me`);
          const userData = await response.json();
          setUser(userData);
      }
      fetchUserData();
    }, []);


  const showLogs = user?.role === "superadmin";

  // Determine grid columns based on visible tabs
  let tabsGridCols = "grid-cols-3"; // Default: general, api, voice
  if (showLogs) {
    tabsGridCols = "grid-cols-4"; // Add logs tab
  }

  return (
    <div className="container py-6 px-4 md:px-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className={`grid w-full ${tabsGridCols} mb-8`}>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="api">API Keys</TabsTrigger>
              <TabsTrigger value="voice">Voice Settings</TabsTrigger>
              {showLogs && <TabsTrigger value="logs">Logs</TabsTrigger>}
            </TabsList>

            <TabsContent value="general">
              <GeneralSettings />
            </TabsContent>

            <TabsContent value="api">
              <APISettings />
            </TabsContent>

            <TabsContent value="voice">
              <VoiceSettings />
            </TabsContent>

            {showLogs && (
              <TabsContent value="logs">
                <LogsSettings />
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
