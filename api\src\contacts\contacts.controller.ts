import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  BadRequestException,
  UseInterceptors,
  UploadedFile,
  Request,
} from "@nestjs/common";
import { ContactsService } from "./contacts.service";
import { ContactDto, UploadContactDto } from "./dto/contact.dto";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { LoggerService } from "src/logger/logger.service";
import { FileInterceptor } from "@nestjs/platform-express";
import * as XLSX from 'xlsx';

@ApiTags("Contacts")
@Controller("contacts")
export class ContactsController {
  constructor(
    private readonly contactsService: ContactsService,
    private readonly loggerService: LoggerService
  ) {}

  private normalizePhoneNumber(value: string): string {
    // Handle scientific notation (e.g., 9.72E+11)
    if (value.includes('E+')) {
      const [base, exponent] = value.split('E+');
      const number = parseFloat(base) * Math.pow(10, parseInt(exponent));
      return number.toString();
    }
    
    // Remove any non-digit characters except + sign
    return value.replace(/[^\d+]/g, '');
  }

  @Post("call-data")
  @ApiOperation({ summary: "Get contact call data by phone number and name" })
  async callData(@Body() body: any) {
    try {
      const toolCall = body.message.toolCalls[0];
      const toolCallId = toolCall.id;
      const { number, name } = toolCall.function.arguments;

      const data = await this.contactsService.callData(number, name);

      return {
        results: [
          {
            toolCallId,
            result: data,
          },
        ],
      };
    } catch (error) {
      throw new HttpException(
        error.message || "Error retrieving call data",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: "Create a new contact" })
  @ApiResponse({ status: 201, description: "Contact successfully created" })
  @ApiBody({ type: ContactDto })
  create(@Body() createContactDto: ContactDto, @Request() req: any) {
    const userName = req.user.fullName || req.user.email.split('@')[0];
    return this.contactsService.create(createContactDto, userName);
  }

  @Get("import-contacts")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: "Import contacts from external API and save to DB" })
  async importContacts(@Request() req) {
    try {
      const userName = req.user.fullName || req.user.email.split('@')[0];
      const contacts = await this.contactsService.importContacts(userName);
      return contacts;
    } catch (error) {
      throw new HttpException(
        error.message || "Error importing contacts",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post("webhook-contacts")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: "Receive contacts via webhook and save to DB" })
  async webhookContacts(@Body() payload: { value: any[] }): Promise<any> {
    try {
      await this.loggerService.log("Received webhook for contacts.");

      const contacts = await this.contactsService.processContacts(
        payload.value
      );

      await this.loggerService.log(
        `Webhook processing completed. ${contacts.length} new contacts imported.`
      );
      return {
        message: "Contacts processed successfully",
        contacts,
      };
    } catch (error) {
      await this.loggerService.error(
        "Error processing webhook contacts",
        error
      );
      throw new HttpException(
        error.message || "Error processing webhook contacts",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('upload')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: 'Upload contacts from CSV/Excel file' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadContacts(@UploadedFile() file: Express.Multer.File, @Request() req: any) {
    try {
       // Get user's name from the JWT token
    const userName = req.user.fullName || req.user.email.split('@')[0];

      await this.loggerService.log('Processing contact upload...');
      
      if (!file) {
        throw new BadRequestException('No file uploaded');
      }


      // Check file extension
      const extension = file.originalname.split('.').pop()?.toLowerCase();
      if (!['csv', 'xlsx', 'xls'].includes(extension)) {
        throw new BadRequestException('Invalid file type. Only CSV and Excel files are allowed');
      }

      let contacts = [];
      const fileName = file.originalname;

      if (extension === 'csv') {
        // Parse CSV
        const content = file.buffer.toString();
        const lines = content.split('\n');

         // Detect the delimiter (comma or semicolon)
        const firstLine = lines[0];
        const delimiter = firstLine.includes(';') ? ';' : ',';
  
        const headers = lines[0].toLowerCase().split(delimiter).map(h => h.trim());

        // Validate headers
        const nameIndex = headers.findIndex(h => 
          ['name', 'contactname', 'contact name', 'fullname'].includes(h)
        );
        const phoneIndex = headers.findIndex(h => 
          ['phone', 'phonenumber', 'phone number', 'mobile', 'number'].includes(h)
        );

         const customerIdIndex = headers.findIndex(h => 
        ['customerid', 'customer id', 'id', 'client id', 'contact id', 'clientid', 'contactid'].includes(h)
      );

      const campaignIndex = headers.findIndex(h => 
          ['campaign', 'campaigns'].includes(h)
        );
        // Add region index (optional)
        const regionIndex = headers.findIndex(h => 
          ['region', 'timezone', 'time zone', 'zone'].includes(h)
        );


        if (nameIndex === -1 || phoneIndex === -1) {
          throw new BadRequestException('CSV must contain name and phone number columns');
        }

        // Parse rows
        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue;
          const values = lines[i].split(delimiter).map(v => v.trim());

          const phoneNumber = this.normalizePhoneNumber(values[phoneIndex]);

          contacts.push({
            fileName,
            contactName: values[nameIndex],
            phoneNumber: phoneNumber,
            customerId: customerIdIndex !== -1 ? values[customerIdIndex] : null,
            campaignNames: campaignIndex !== -1 ? values[campaignIndex].split(',').map(c => c.trim()).filter(Boolean) : [],
            region: regionIndex !== -1 ? values[regionIndex] : ""
          });
        }
      } else {
      // Updated Excel parsing
      try {
        const workbook = XLSX.read(file.buffer, { type: 'buffer' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 }) as any[][];

        if (!Array.isArray(rows[0])) {
      throw new BadRequestException('Invalid Excel format: headers row is missing');
    }

        // Get headers from first row
        const headers = rows[0].map((h: any) => String(h).toLowerCase().trim());

        // Find required column indices
        const nameIndex = headers.findIndex((h: string) => 
          ['name', 'contactname', 'contact name', 'fullname'].includes(h)
        );
        const phoneIndex = headers.findIndex((h: string) => 
          ['phone', 'phonenumber', 'phone number', 'mobile'].includes(h)
        );

        const customerIdIndex = headers.findIndex((h: string) => 
        ['customerid', 'customer id', 'id', 'client id', 'contact id', 'clientid', 'contactid'].includes(h)
      );

        const campaignIndex = headers.findIndex((h: string) => 
          ['campaign', 'campaigns'].includes(h)
        );

         const regionIndex = headers.findIndex((h: string) => 
          ['region', 'timezone', 'time zone', 'zone'].includes(h)
        );

        if (nameIndex === -1 || phoneIndex === -1) {
          throw new BadRequestException('Excel file must contain name and phone number columns');
        }

        // Process data rows
        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          if (!row || !row[nameIndex] || !row[phoneIndex]) continue;

          contacts.push({
            contactName: String(row[nameIndex]).trim(),
            phoneNumber: String(row[phoneIndex]).trim(),
            customerId: customerIdIndex !== -1 ? String(row[customerIdIndex]).trim() : null,
            campaignNames: campaignIndex !== -1 ? String(row[campaignIndex]).split(',').map(c => c.trim()).filter(Boolean) : [],
            region: regionIndex !== -1 ? String(row[regionIndex]).trim() : ""
          });
        }

      } catch (error) {
        throw new BadRequestException(`Error parsing Excel file: ${error.message}`);
      }
    }

      // Filter out any empty entries
      contacts = contacts.filter(contact => 
        contact.contactName && contact.phoneNumber && 
        contact.contactName.trim() !== '' && 
        contact.phoneNumber.trim() !== ''
      );

      if (contacts.length === 0) {
        throw new BadRequestException('No valid contacts found in file');
      }

      const result = await this.contactsService.uploadContacts(contacts, userName);
      return {
        ...result,
        fileName
      };

    } catch (error) {
      await this.loggerService.error('Error processing contact upload:', error.message);
      throw new HttpException(
        error.message || 'Error processing contact upload',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: "Get all contacts" })
  @ApiResponse({ status: 200, description: "List of contacts" })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for contacts' })
  @ApiQuery({ name: 'campaign', required: false, description: 'Filter contacts by campaign ID' })
  async findAll(
    @Query('page') page: number,
    @Query('limit') limit: number,
    @Query('search') search?: string,
    @Query('filterType') filterType: 'name' | 'campaign' = 'name',
    @Query('campaignId') campaignId?: string,
    @Query('noCampaign') noCampaign?: string,
  ) {
    try {
       // If no pagination parameters, fetch all contacts
    if (!page && !limit) {
      const contacts = await this.contactsService.findAll(
        undefined, 
        undefined, 
        search, 
        filterType, 
        campaignId,
        noCampaign === 'true'
      );
      return contacts;
    }

    // Handle paginated request
    const pageNum = Number(page) || 1;
    const limitNum = Number(limit) || 20;
      
      // Validate pagination parameters
      if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
        throw new BadRequestException('Invalid pagination parameters');
      }
      
      // Get paginated contacts
      const result = await this.contactsService.findAll(
        pageNum,
        limitNum,
        search,
        filterType,
        campaignId,
        noCampaign === 'true'
      );
      
      return result;
    } catch (error) {
      throw new HttpException(
        error.message || 'Error retrieving paginated contacts',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  @Get(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: "Get a contact by ID" })
  @ApiResponse({ status: 200, description: "The requested contact" })
  findById(@Param("id") id: string) {
    return this.contactsService.findById(id);
  }

  @Patch(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: "Update a contact by ID" })
  @ApiResponse({ status: 200, description: "Contact updated" })
  @ApiBody({ type: ContactDto })
  update(@Param("id") id: string, @Body() updateContactDto: ContactDto) {
    return this.contactsService.update(id, updateContactDto);
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: "Delete a contact by ID" })
  @ApiResponse({ status: 200, description: "Contact deleted" })
  remove(@Param("id") id: string) {
    return this.contactsService.remove(id);
  }
}
