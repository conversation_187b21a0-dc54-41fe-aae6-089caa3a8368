"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var CampaignService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CampaignService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const scheduled_call_service_1 = require("../scheduled-call/scheduled-call.service");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
let CampaignService = CampaignService_1 = class CampaignService {
    constructor(campaignModel, contactModel, scheduledCallService) {
        this.campaignModel = campaignModel;
        this.contactModel = contactModel;
        this.scheduledCallService = scheduledCallService;
        this.logger = new common_1.Logger(CampaignService_1.name);
    }
    async create(createCampaignDto) {
        this.logger.log(`Creating campaign with createdBy: ${createCampaignDto.createdBy}`);
        const createdCampaign = new this.campaignModel(createCampaignDto);
        if (createCampaignDto.contacts && createCampaignDto.contacts.length > 0) {
            const contactIds = createCampaignDto.contacts.map((contact) => {
                if (typeof contact === 'string') {
                    return contact;
                }
                else if (contact && typeof contact === 'object' && 'contactId' in contact) {
                    return contact.contactId;
                }
                else {
                    this.logger.warn(`Invalid contact format: ${JSON.stringify(contact)}`);
                    return null;
                }
            }).filter((id) => id !== null);
            if (contactIds.length > 0) {
                await this.contactModel.updateMany({ _id: { $in: contactIds } }, { $addToSet: { campaigns: createdCampaign._id } });
                const contactsToAdd = await this.contactModel
                    .find({ _id: { $in: contactIds } })
                    .exec();
                const contactObjects = contactsToAdd.map(contact => ({
                    contactId: contact._id,
                    contactName: contact.contactName,
                    phoneNumber: contact.phoneNumber
                }));
                createdCampaign.contacts = contactObjects;
            }
            else {
                this.logger.warn('No valid contact IDs found in the contacts array');
            }
            const savedCampaign = await createdCampaign.save();
            try {
                await this.scheduleCallsForCampaign(savedCampaign);
            }
            catch (error) {
                this.logger.error(`Error scheduling calls for campaign ${savedCampaign.name}:`, error.message);
            }
            return savedCampaign;
        }
        return createdCampaign.save();
    }
    async findAll(statusFilter) {
        const query = statusFilter
            ? { status: statusFilter }
            : {};
        return this.campaignModel.find(query).populate("contacts", "contactName").exec();
    }
    async findById(id) {
        const campaign = await this.campaignModel
            .findById(id)
            .populate("contacts", "contactName")
            .exec();
        if (!campaign) {
            throw new common_1.NotFoundException(`Campaign with ID ${id} not found`);
        }
        return campaign;
    }
    async update(id, updateCampaignDto) {
        const existingCampaign = await this.campaignModel.findById(id).exec();
        if (!existingCampaign)
            throw new common_1.NotFoundException("Campaign not found");
        const existingContacts = await this.contactModel
            .find({ campaigns: existingCampaign._id })
            .exec();
        if (updateCampaignDto.contacts && Array.isArray(updateCampaignDto.contacts)) {
            const newContactIds = updateCampaignDto.contacts
                .map((contact) => {
                if (typeof contact === 'string') {
                    return contact;
                }
                else if (contact && typeof contact === 'object' && 'contactId' in contact) {
                    return contact.contactId;
                }
                return null;
            })
                .filter((id) => id !== null);
            const existingContactIds = existingContacts.map(contact => contact._id.toString());
            const removedContactIds = existingContactIds.filter((id) => !newContactIds.includes(id));
            const addedContactIds = newContactIds.filter((id) => !existingContactIds.includes(id));
            this.logger.log(`Campaign update: ${addedContactIds.length} contacts added, ${removedContactIds.length} contacts removed`);
            if (removedContactIds.length > 0) {
                try {
                    const removedContacts = existingContacts.filter(contact => removedContactIds.includes(contact._id.toString()));
                    const contactNames = removedContacts.map(contact => contact.contactName);
                    const contactPhones = removedContacts.map(contact => contact.phoneNumber);
                    const cancelledCount = await this.scheduledCallService.cancelPendingCallsForContacts(contactNames, contactPhones);
                    this.logger.log(`Cancelled ${cancelledCount} pending scheduled calls for removed contacts`);
                }
                catch (error) {
                    this.logger.error(`Error cancelling scheduled calls for removed contacts:`, error.message);
                }
            }
            await this.contactModel.updateMany({ campaigns: existingCampaign._id }, { $pull: { campaigns: existingCampaign._id } });
            if (newContactIds.length > 0) {
                await this.contactModel.updateMany({ _id: { $in: newContactIds } }, { $addToSet: { campaigns: existingCampaign._id } });
                const contactsToAdd = await this.contactModel
                    .find({ _id: { $in: newContactIds } })
                    .exec();
                for (const contact of contactsToAdd) {
                    await this.campaignModel.updateOne({ _id: existingCampaign._id }, {
                        $addToSet: {
                            contacts: {
                                contactId: contact._id,
                                contactName: contact.contactName,
                                phoneNumber: contact.phoneNumber
                            }
                        }
                    });
                }
            }
            const existingCampaignData = existingCampaign.toObject();
            const existingAgentId = existingCampaignData.agentId;
            const agentIdChanged = updateCampaignDto.agentId && existingAgentId !== updateCampaignDto.agentId;
            const existingConcurrentCalls = existingCampaignData.concurrentCalls || 1;
            const newConcurrentCalls = updateCampaignDto.concurrentCalls || existingConcurrentCalls;
            const concurrentCallsChanged = newConcurrentCalls !== existingConcurrentCalls;
            const updatedCampaign = await this.campaignModel
                .findByIdAndUpdate(id, updateCampaignDto, { new: true })
                .exec();
            if (agentIdChanged && updateCampaignDto.agentId) {
                try {
                    const allCampaignContacts = await this.contactModel
                        .find({ campaigns: existingCampaign._id })
                        .exec();
                    if (allCampaignContacts && allCampaignContacts.length > 0) {
                        const contactNames = allCampaignContacts.map(contact => contact.contactName);
                        const contactPhones = allCampaignContacts.map(contact => contact.phoneNumber);
                        const updatedCount = await this.scheduledCallService.updateAgentForPendingCalls(contactNames, contactPhones, updateCampaignDto.agentId);
                        this.logger.log(`Updated agent ID to ${updateCampaignDto.agentId} for ${updatedCount} pending scheduled calls`);
                    }
                }
                catch (error) {
                    this.logger.error(`Error updating agent ID for scheduled calls:`, error.message);
                }
            }
            if (concurrentCallsChanged && newConcurrentCalls > 0) {
                try {
                    const updatedCampaignData = updatedCampaign.toObject();
                    const agentId = updatedCampaignData.agentId;
                    const callWindow = {
                        startTime: updatedCampaignData.callWindow?.startTime || '09:00',
                        endTime: updatedCampaignData.callWindow?.endTime || '17:00',
                        daysOfWeek: updatedCampaignData.callWindow?.daysOfWeek || ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
                    };
                    const rescheduledCount = await this.scheduledCallService.rescheduleCampaignCalls(agentId, newConcurrentCalls, updatedCampaignData.batchIntervalMinutes || 3, callWindow);
                    this.logger.log(`Rescheduled ${rescheduledCount} calls for campaign ${updatedCampaign.name} with new concurrency level ${newConcurrentCalls}`);
                }
                catch (error) {
                    this.logger.error(`Error rescheduling calls for campaign:`, error.message);
                }
            }
            if (addedContactIds.length > 0) {
                try {
                    const addedContacts = await this.contactModel
                        .find({ _id: { $in: addedContactIds } })
                        .exec();
                    if (addedContacts.length > 0) {
                        this.logger.log(`Scheduling calls for ${addedContacts.length} newly added contacts`);
                        await this.scheduleCallsForContacts(updatedCampaign, addedContacts);
                    }
                }
                catch (error) {
                    this.logger.error(`Error scheduling calls for new contacts in campaign ${updatedCampaign.name}:`, error.message);
                }
            }
            return updatedCampaign;
        }
        return this.campaignModel
            .findByIdAndUpdate(id, updateCampaignDto, { new: true })
            .exec();
    }
    async updateStatus(id, statusDto) {
        const campaign = await this.campaignModel.findById(id).exec();
        if (!campaign) {
            throw new common_1.NotFoundException(`Campaign with ID ${id} not found`);
        }
        if (statusDto.status === 'paused' && campaign.status !== 'paused') {
            this.logger.log(`Campaign ${campaign.name} is being paused, cancelling pending scheduled calls`);
            try {
                const contacts = await this.contactModel
                    .find({ campaigns: campaign._id })
                    .exec();
                if (contacts && contacts.length > 0) {
                    const contactNames = contacts.map(contact => contact.contactName);
                    const contactPhones = contacts.map(contact => contact.phoneNumber);
                    const cancelledCount = await this.scheduledCallService.cancelPendingCallsForContacts(contactNames, contactPhones);
                    this.logger.log(`Cancelled ${cancelledCount} pending scheduled calls for campaign ${campaign.name}`);
                }
            }
            catch (error) {
                this.logger.error(`Error cancelling scheduled calls for campaign ${campaign.name}:`, error.message);
            }
        }
        campaign.status = statusDto.status;
        return campaign.save();
    }
    async remove(id) {
        const campaign = await this.campaignModel.findById(id).exec();
        if (!campaign) {
            throw new common_1.NotFoundException(`Campaign with ID ${id} not found`);
        }
        const contacts = await this.contactModel
            .find({ campaigns: campaign._id })
            .exec();
        await this.campaignModel.findByIdAndDelete(id).exec();
        await this.contactModel.updateMany({ campaigns: campaign._id }, { $pull: { campaigns: campaign._id } });
        if (contacts && contacts.length > 0) {
            try {
                const contactNames = contacts.map(contact => contact.contactName);
                const contactPhones = contacts.map(contact => contact.phoneNumber);
                const cancelledCount = await this.scheduledCallService.cancelPendingCallsForContacts(contactNames, contactPhones);
                this.logger.log(`Cancelled ${cancelledCount} pending scheduled calls for deleted campaign ${campaign.name}`);
            }
            catch (error) {
                this.logger.error(`Error cancelling scheduled calls for deleted campaign ${campaign.name}:`, error.message);
            }
        }
    }
    async search(term) {
        const searchRegex = new RegExp(term, "i");
        return this.campaignModel
            .find({
            name: { $regex: searchRegex },
        })
            .populate("contacts", "contactName")
            .exec();
    }
    async calculatePerformanceMetrics(id) {
        const campaign = await this.findById(id);
        return {
            id: campaign._id,
            successRate: campaign.successRate,
            sentiment: campaign.sentiment,
        };
    }
    async scheduleCallsForCampaign(campaign) {
        const contacts = await this.contactModel
            .find({ campaigns: campaign._id })
            .exec();
        if (!contacts || contacts.length === 0) {
            this.logger.warn(`No contacts found for campaign ${campaign.name}`);
            return;
        }
        await this.scheduleCallsForContacts(campaign, contacts);
    }
    async scheduleCallsForContactInCampaign(campaign, contact) {
        const data = campaign.toObject();
        const agentId = data.agentId;
        const concurrent = data.concurrentCalls || 1;
        const window = data.callWindow;
        const region = contact.region || 'UTC';
        if (!agentId)
            return;
        const pending = await this.scheduledCallService.getPendingCallsForCampaign(agentId);
        const slotCounts = new Map();
        pending.forEach(call => {
            const key = call.scheduledTime.toISOString();
            slotCounts.set(key, (slotCounts.get(key) || 0) + call.contacts.length);
        });
        const [wSH, wSM] = window.startTime.split(':').map(Number);
        const [wEH, wEM] = window.endTime.split(':').map(Number);
        const allowed = window.daysOfWeek.map((d) => d.toLowerCase());
        const batchIntervalMinutes = data.batchIntervalMinutes || 3;
        const INTERVAL = batchIntervalMinutes * 60 * 1000;
        const rollIntoWindow = (d) => {
            let momentDate = moment_timezone_1.default.utc(d).tz(region);
            if (momentDate.hours() < wSH || (momentDate.hours() === wSH && momentDate.minutes() < wSM)) {
                momentDate.hours(wSH).minutes(wSM).seconds(0).milliseconds(0);
            }
            else if (momentDate.hours() > wEH || (momentDate.hours() === wEH && momentDate.minutes() > wEM)) {
                momentDate.add(1, 'days').hours(wSH).minutes(wSM).seconds(0).milliseconds(0);
            }
            const currentDay = momentDate.format('dddd').toLowerCase();
            if (!allowed.includes(currentDay)) {
                this.logger.log(`Day ${currentDay} not in allowed days: ${allowed.join(', ')}. Finding next allowed day.`);
                let daysChecked = 0;
                while (!allowed.includes(momentDate.format('dddd').toLowerCase()) && daysChecked < 7) {
                    momentDate.add(1, 'days').hours(wSH).minutes(wSM).seconds(0).milliseconds(0);
                    daysChecked++;
                }
                this.logger.log(`Adjusted to ${momentDate.format('dddd')} in ${region} time zone`);
            }
            return momentDate.toDate();
        };
        const now = (0, moment_timezone_1.default)().tz(region).toDate();
        let slot;
        if (data.instantCall) {
            slot = rollIntoWindow(new Date(now.getTime() + 5 * 60000));
        }
        else {
            const tmr = (0, moment_timezone_1.default)(now).tz(region).add(1, 'days').toDate();
            slot = rollIntoWindow(tmr);
        }
        try {
            const latest = await this.scheduledCallService.getLatestScheduledCallForCampaign(agentId);
            if (latest.length) {
                const latestTime = new Date(latest[0].scheduledTime);
                const bumped = new Date(latestTime.getTime() + INTERVAL);
                if (data.instantCall) {
                    if (bumped > slot) {
                        slot = rollIntoWindow(bumped);
                        this.logger.log(`Instant call adjusted to after latest call: ${slot.toISOString()} in ${region} time zone`);
                    }
                }
                else {
                    slot = rollIntoWindow(bumped);
                    this.logger.log(`Adjusted start slot to after latest call: ${slot.toISOString()} in ${region} time zone`);
                }
            }
        }
        catch (e) {
            this.logger.error(`Error fetching latest call: ${e.message}`);
        }
        while ((slotCounts.get(slot.toISOString()) || 0) >= concurrent) {
            slot = rollIntoWindow(new Date(slot.getTime() + INTERVAL));
        }
        const slotInContactTz = (0, moment_timezone_1.default)(slot).tz(region).format();
        await this.scheduledCallService.createScheduledCall({
            agentId,
            contacts: [{ Name: contact.contactName, MobileNumber: contact.phoneNumber }],
            scheduledTime: slotInContactTz,
            region: region,
            scheduledByName: data.createdBy || 'system',
        });
        this.logger.log(`Scheduled ${contact.contactName} at ${slotInContactTz} (${region} time zone)`);
        slotCounts.set(slot.toISOString(), (slotCounts.get(slot.toISOString()) || 0) + 1);
    }
    async scheduleCallsForContacts(campaign, contacts) {
        if (!campaign.startDate) {
            this.logger.warn(`Campaign ${campaign.name} has no start date, skipping call scheduling`);
            return;
        }
        this.logger.log(`Scheduling ${contacts.length} contacts for campaign ${campaign.name}`);
        const now = new Date();
        const data = campaign.toObject();
        const agentId = data.agentId;
        const concurrent = data.concurrentCalls || 1;
        const window = data.callWindow || {
            startTime: '09:00',
            endTime: '17:00',
            daysOfWeek: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        };
        if (!agentId) {
            this.logger.warn(`No agent ID for campaign ${campaign.name}, skipping`);
            return;
        }
        const [wStartH, wStartM] = window.startTime.split(':').map(Number);
        const [wEndH, wEndM] = window.endTime.split(':').map(Number);
        const allowedDays = window.daysOfWeek.map((d) => d.toLowerCase());
        const rollIntoWindow = (d, contactRegion) => {
            let momentDate = moment_timezone_1.default.utc(d).tz(contactRegion);
            if (momentDate.hours() < wStartH || (momentDate.hours() === wStartH && momentDate.minutes() < wStartM)) {
                momentDate.hours(wStartH).minutes(wStartM).seconds(0).milliseconds(0);
            }
            else if (momentDate.hours() > wEndH || (momentDate.hours() === wEndH && momentDate.minutes() > wEndM)) {
                momentDate.add(1, 'days').hours(wStartH).minutes(wStartM).seconds(0).milliseconds(0);
            }
            const currentDay = momentDate.format('dddd').toLowerCase();
            if (!allowedDays.includes(currentDay)) {
                this.logger.log(`Day ${currentDay} not in allowed days: ${allowedDays.join(', ')}. Finding next allowed day.`);
                let daysChecked = 0;
                while (!allowedDays.includes(momentDate.format('dddd').toLowerCase()) && daysChecked < 7) {
                    momentDate.add(1, 'days').hours(wStartH).minutes(wStartM).seconds(0).milliseconds(0);
                    daysChecked++;
                }
                this.logger.log(`Adjusted to ${momentDate.format('dddd')} in ${contactRegion} time zone`);
            }
            return momentDate.toDate();
        };
        let slot;
        const defaultRegion = 'UTC';
        if (data.instantCall) {
            slot = new Date(now.getTime() + 5 * 60000);
            slot = rollIntoWindow(slot, defaultRegion);
            this.logger.log(`Instant call on, first slot: ${slot.toISOString()}`);
        }
        else {
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(wStartH, wStartM, 0, 0);
            slot = rollIntoWindow(tomorrow, defaultRegion);
            this.logger.log(`Instant call off, first slot: ${slot.toISOString()}`);
        }
        try {
            const latest = await this.scheduledCallService.getLatestScheduledCallForCampaign(agentId);
            if (latest && latest.length > 0) {
                const latestTime = new Date(latest[0].scheduledTime);
                const batchIntervalMinutes = data.batchIntervalMinutes || 3;
                const bumped = new Date(latestTime.getTime() + batchIntervalMinutes * 60000);
                if (data.instantCall) {
                    if (bumped > slot) {
                        slot = rollIntoWindow(bumped, defaultRegion);
                        this.logger.log(`Instant call adjusted to after latest call: ${slot.toISOString()}`);
                    }
                }
                else {
                    slot = rollIntoWindow(bumped, defaultRegion);
                    this.logger.log(`Adjusted start slot to after latest call: ${slot.toISOString()}`);
                }
            }
        }
        catch (err) {
            this.logger.error(`Failed fetching latest call time: ${err.message}`);
        }
        const batchIntervalMinutes = data.batchIntervalMinutes || 3;
        const INTERVAL_MS = batchIntervalMinutes * 60 * 1000;
        let current = slot;
        for (let i = 0; i < contacts.length; i += concurrent) {
            const batch = contacts.slice(i, i + concurrent);
            for (const ct of batch) {
                const contactRegion = ct.region || 'UTC';
                const contactSlot = rollIntoWindow(current, contactRegion);
                const slotInContactTz = (0, moment_timezone_1.default)(contactSlot).tz(contactRegion).format();
                await this.scheduledCallService.createScheduledCall({
                    agentId,
                    contacts: [{ Name: ct.contactName, MobileNumber: ct.phoneNumber }],
                    scheduledTime: slotInContactTz,
                    region: contactRegion,
                    scheduledByName: data.name || 'system',
                });
                this.logger.log(`Scheduled ${ct.contactName} at ${slotInContactTz} (${contactRegion} time zone)`);
            }
            current = new Date(current.getTime() + INTERVAL_MS);
            current = rollIntoWindow(current, defaultRegion);
        }
        this.logger.log(`Finished scheduling calls for campaign ${campaign.name}`);
    }
};
exports.CampaignService = CampaignService;
exports.CampaignService = CampaignService = CampaignService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)("Campaign")),
    __param(1, (0, mongoose_1.InjectModel)("Contact")),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        scheduled_call_service_1.ScheduledCallService])
], CampaignService);
//# sourceMappingURL=campaign.service.js.map