"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CreditCheckMiddleware_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditCheckMiddleware = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("../users/users.service");
const auto_recharge_service_1 = require("./auto-recharge.service");
const organizations_service_1 = require("../organizations/organizations.service");
let CreditCheckMiddleware = CreditCheckMiddleware_1 = class CreditCheckMiddleware {
    constructor(usersService, autoRechargeService, organizationsService) {
        this.usersService = usersService;
        this.autoRechargeService = autoRechargeService;
        this.organizationsService = organizationsService;
        this.logger = new common_1.Logger(CreditCheckMiddleware_1.name);
    }
    async use(req, res, next) {
        try {
            const skipRoutes = [
                '/api/auth',
                '/api/users/credits',
                '/api/billing',
                '/api/organizations',
            ];
            for (const route of skipRoutes) {
                if (req.path.startsWith(route)) {
                    return next();
                }
            }
            if (req.method === 'GET') {
                return next();
            }
            const userId = req.user?.userId;
            if (!userId) {
                return next();
            }
            this.logger.log(`Checking credits for user ${userId} on route: ${req.method} ${req.path}`);
            const hasSufficientCredits = await this.usersService.hasSufficientCredits(userId, 1);
            if (!hasSufficientCredits) {
                this.logger.log(`Insufficient credits for user ${userId}, attempting auto-recharge`);
                const autoRechargeSuccessful = await this.autoRechargeService.checkAndProcessAutoRecharge(userId);
                if (autoRechargeSuccessful) {
                    this.logger.log(`Auto-recharge successful for user ${userId}, allowing request to proceed`);
                    return next();
                }
                this.logger.warn(`Auto-recharge failed or not enabled for user ${userId}, returning payment required error`);
                return res.status(common_1.HttpStatus.PAYMENT_REQUIRED).json({
                    error: 'Insufficient credits. Please add funds to your account.',
                });
            }
            this.logger.log(`User ${userId} has sufficient credits, allowing request to proceed`);
            next();
        }
        catch (error) {
            console.error('Error in credit check middleware:', error);
            next();
        }
    }
};
exports.CreditCheckMiddleware = CreditCheckMiddleware;
exports.CreditCheckMiddleware = CreditCheckMiddleware = CreditCheckMiddleware_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        auto_recharge_service_1.AutoRechargeService,
        organizations_service_1.OrganizationsService])
], CreditCheckMiddleware);
//# sourceMappingURL=credit.middleware.js.map