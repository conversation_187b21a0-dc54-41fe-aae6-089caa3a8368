/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unescaped-entities */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2, ArrowLeft, Save } from 'lucide-react';
import { toast } from "sonner";
import { getOrganization, updateOrganization, updateOrganizationBilling, Organization } from '@/app/api/organizations';
import { updateOrganizationSettings } from '@/app/api/organizationSettings';
import { formatCurrency } from '@/lib/utils';

interface OrganizationDetailsContentProps {
  organizationId: string;
}

export default function OrganizationDetailsContent({ organizationId }: OrganizationDetailsContentProps) {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    status: 'active' | 'inactive' | 'suspended';
  }>({
    name: '',
    description: '',
    status: 'active',
  });
  const [billingData, setBillingData] = useState({
    credits: 0,
    autoRechargeEnabled: false,
    autoRechargeThreshold: 1.0,
    autoRechargeAmount: 0,
    callPricePerMinute: 0.10,
    minimumCreditsThreshold: 1.0,
    monthlyMinutesAllowance: 0,
    monthlyResetDate: 1,
  });
  const [notificationData, setNotificationData] = useState({
    fullName: '',
    email: '',
  });

  const router = useRouter();

  useEffect(() => {
    fetchOrganization();
  }, [organizationId]);

  const fetchOrganization = async () => {
    try {
      setLoading(true);
      const data = await getOrganization(organizationId);
      setOrganization(data);
      setFormData({
        name: data.name,
        description: data.description || '',
        status: data.status,
      });
      setBillingData({
        credits: data.credits || 0,
        autoRechargeEnabled: data.autoRechargeEnabled || false,
        autoRechargeThreshold: data.autoRechargeThreshold || 1.0,
        autoRechargeAmount: data.autoRechargeAmount || 0,
        callPricePerMinute: data.callPricePerMinute || 0.10,
        minimumCreditsThreshold: data.minimumCreditsThreshold || 1.0,
        monthlyMinutesAllowance: data.monthlyMinutesAllowance || 0,
        monthlyResetDate: data.monthlyResetDate || 1,
      });
      setNotificationData({
        fullName: data.fullName || '',
        email: data.email || '',
      });
    } catch (error) {
      console.error('Error fetching Workspace:', error);
      toast.error('Failed to fetch workspace details');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDetails = async () => {
    try {
      setIsSaving(true);
      const updated = await updateOrganization(organizationId, formData);
      setOrganization(updated);
      toast.success('Workspace details updated successfully');
    } catch (error) {
      console.error('Error updating Workspace:', error);
      toast.error('Failed to update Workspace details');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveBilling = async () => {
    try {
      setIsSaving(true);
      const updated = await updateOrganizationBilling(organizationId, billingData);
      setOrganization(updated);
      toast.success('Workspace billing settings updated successfully');
    } catch (error) {
      console.error('Error updating Workspace billing:', error);
      toast.error('Failed to update Workspace billing settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveNotifications = async () => {
    try {
      setIsSaving(true);
      await updateOrganizationSettings(organizationId, notificationData);
      // Refresh organization data to get updated values
      await fetchOrganization();
      toast.success('Notification settings updated successfully');
    } catch (error) {
      console.error('Error updating notification settings:', error);
      toast.error('Failed to update notification settings');
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-8 text-gray-500">
          Workspace not found.
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">{organization.name}</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Workspace Details</CardTitle>
              <CardDescription>Update the Workspace's basic information.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="md:text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="md:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="md:text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="md:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="md:text-right">
                  Status
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'active' | 'inactive' | 'suspended') =>
                    setFormData({ ...formData, status: value })
                  }
                >
                  <SelectTrigger className="md:col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveDetails} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="billing">
          <Card>
            <CardHeader>
              <CardTitle>Billing Settings</CardTitle>
              <CardDescription>Manage the Workspace's billing and credits.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="credits" className="md:text-right">
                  Credits
                </Label>
                <div className="md:col-span-3 flex items-center">
                  <Input
                    id="credits"
                    type="number"
                    min="0"
                    step="0.01"
                    value={billingData.credits}
                    onChange={(e) => setBillingData({ ...billingData, credits: parseFloat(e.target.value) || 0 })}
                    className="w-full"
                  />
                  <span className="ml-2 text-sm text-gray-500">
                    Current: {formatCurrency(organization.credits || 0)}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="callPricePerMinute" className="md:text-right">
                  Call Price Per Minute ($)
                </Label>
                <div className="md:col-span-3">
                  <Input
                    id="callPricePerMinute"
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={billingData.callPricePerMinute}
                    onChange={(e) => setBillingData({ ...billingData, callPricePerMinute: parseFloat(e.target.value) || 0.10 })}
                    className="w-full"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    The price charged per minute for calls
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="minimumCreditsThreshold" className="md:text-right">
                  Minimum Credits Threshold ($)
                </Label>
                <div className="md:col-span-3">
                  <Input
                    id="minimumCreditsThreshold"
                    type="number"
                    min="0"
                    step="0.01"
                    value={billingData.minimumCreditsThreshold}
                    onChange={(e) => setBillingData({ ...billingData, minimumCreditsThreshold: parseFloat(e.target.value) || 1.0 })}
                    className="w-full"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    When credits fall below this amount, calls will be blocked
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="monthlyMinutesAllowance" className="md:text-right">
                  Monthly Minutes Allowance
                </Label>
                <div className="md:col-span-3">
                  <Input
                    id="monthlyMinutesAllowance"
                    type="number"
                    min="0"
                    step="1"
                    value={billingData.monthlyMinutesAllowance}
                    onChange={(e) => setBillingData({ ...billingData, monthlyMinutesAllowance: parseFloat(e.target.value) || 0 })}
                    className="w-full"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Monthly minutes allowance for this workspace (0 to disable)
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="monthlyResetDate" className="md:text-right">
                  Monthly Reset Date
                </Label>
                <div className="md:col-span-3">
                  <Select
                    value={billingData.monthlyResetDate.toString()}
                    onValueChange={(value) => setBillingData({ ...billingData, monthlyResetDate: parseInt(value) })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select reset date" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 28 }, (_, i) => i + 1).map((day) => (
                        <SelectItem key={day} value={day.toString()}>
                          {day}{day === 1 ? 'st' : day === 2 ? 'nd' : day === 3 ? 'rd' : 'th'} of each month
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500 mt-1">
                    Day of the month when monthly credits and allowances reset
                  </p>
                </div>
              </div>

              {/* <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="autoRechargeEnabled" className="md:text-right">
                  Auto-Recharge
                </Label>
                <div className="md:col-span-3">
                  <Select
                    value={billingData.autoRechargeEnabled ? 'true' : 'false'}
                    onValueChange={(value) =>
                      setBillingData({ ...billingData, autoRechargeEnabled: value === 'true' })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Enable auto-recharge" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">Enabled</SelectItem>
                      <SelectItem value="false">Disabled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {billingData.autoRechargeEnabled && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                    <Label htmlFor="autoRechargeThreshold" className="md:text-right">
                      Recharge Threshold ($)
                    </Label>
                    <Input
                      id="autoRechargeThreshold"
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={billingData.autoRechargeThreshold}
                      onChange={(e) => setBillingData({ ...billingData, autoRechargeThreshold: parseFloat(e.target.value) || 1.0 })}
                      className="md:col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                    <Label htmlFor="autoRechargeAmount" className="md:text-right">
                      Recharge Amount ($)
                    </Label>
                    <Input
                      id="autoRechargeAmount"
                      type="number"
                      min="0"
                      step="1"
                      value={billingData.autoRechargeAmount}
                      onChange={(e) => setBillingData({ ...billingData, autoRechargeAmount: parseFloat(e.target.value) || 0 })}
                      className="md:col-span-3"
                    />
                    <div className="col-start-2 md:col-span-3 text-sm text-gray-500">
                      {billingData.autoRechargeAmount === 0 ?
                        "0 means use the last payment amount" :
                        `Will recharge $${billingData.autoRechargeAmount.toFixed(2)} when credits fall below $${billingData.autoRechargeThreshold.toFixed(2)}`
                      }
                    </div>
                  </div>
                </>
              )} */}
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveBilling} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Billing Settings
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Email Notification Settings</CardTitle>
              <CardDescription>Configure email notifications for credit alerts and warnings.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="fullName" className="md:text-right">
                  Client Full Name
                </Label>
                <div className="md:col-span-3">
                  <Input
                    id="fullName"
                    type="text"
                    value={notificationData.fullName}
                    onChange={(e) => setNotificationData({ ...notificationData, fullName: e.target.value })}
                    placeholder="Enter client's full name"
                    className="w-full"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    This name will be used in email notifications for personalization
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="md:text-right">
                  Notification Email
                </Label>
                <div className="md:col-span-3">
                  <Input
                    id="email"
                    type="email"
                    value={notificationData.email}
                    onChange={(e) => setNotificationData({ ...notificationData, email: e.target.value })}
                    placeholder="Enter email address for notifications"
                    className="w-full"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Credit alerts and warnings will be sent to this email address
                  </p>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Email Notification Types</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• <strong>Credit Runout Alert:</strong> Sent when credits are completely depleted</li>
                  <li>• <strong>Credit Warning:</strong> Sent when credits fall below 2x the minimum threshold</li>
                  <li>• Both emails include a direct link to add funds to the account</li>
                </ul>
              </div>

              {organization.minimumCreditsThreshold && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Current Thresholds</h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>• <strong>Minimum Credits Threshold:</strong> ${organization.minimumCreditsThreshold.toFixed(2)}</p>
                    <p>• <strong>Warning Threshold:</strong> ${(organization.minimumCreditsThreshold * 2).toFixed(2)}</p>
                    <p>• <strong>Current Credits:</strong> ${organization.credits.toFixed(2)}</p>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveNotifications} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Notification Settings
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
