import { API_URL } from "./config";

// Types
export interface PaymentMethod {
  _id: string;
  userId: string;
  type: string;
  stripePaymentMethodId: string;
  last4?: string;
  expMonth?: string;
  expYear?: string;
  cardholderName?: string;
  isDefault: boolean;
  brand?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Transaction {
  _id: string;
  userId: string;
  amount: number;
  currency: string;
  status: string;
  paymentMethodId?: string;
  stripePaymentIntentId: string;
  stripeCustomerId: string;
  description?: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionResponse {
  transactions: Transaction[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface CreatePaymentMethodRequest {
  type: string;
  stripePaymentMethodId: string;
  last4?: string;
  expMonth?: string;
  expYear?: string;
  cardholderName?: string;
  isDefault?: boolean;
  brand?: string;
}

export interface CreatePaymentIntentRequest {
  amount: number;
  currency: string;
  paymentMethodId?: string;
  paymentMethodType: string;
  description?: string;
}

export interface PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  status: string;
}

export interface ProcessPaymentRequest {
  paymentMethodId: string;
  amount: number;
  currency: string;
  description?: string;
  savePaymentMethod?: boolean;
  setAsDefault?: boolean;
}

export interface ProcessPaymentResponse {
  success: boolean;
  status: string;
  transactionId: string;
  paymentIntentId: string;
  paymentMethodId: string | null;
  clientSecret?: string;
  savedPaymentMethod?: boolean;
}

// API Functions
export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
  const response = await fetch(`${API_URL}/api/billing/payment-methods`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch payment methods');
  }

  return response.json();
};

export const createPaymentMethod = async (
  data: CreatePaymentMethodRequest
): Promise<PaymentMethod> => {
  const response = await fetch(`${API_URL}/api/billing/payment-methods`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create payment method');
  }

  return response.json();
};

export const setDefaultPaymentMethod = async (
  paymentMethodId: string
): Promise<PaymentMethod> => {
  const response = await fetch(
    `${API_URL}/api/billing/payment-methods/${paymentMethodId}/default`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('access_token')}`,
      },
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to set default payment method');
  }

  return response.json();
};

export const removePaymentMethod = async (
  paymentMethodId: string
): Promise<{ success: boolean; message: string }> => {
  const response = await fetch(
    `${API_URL}/api/billing/payment-methods/${paymentMethodId}`,
    {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('access_token')}`,
      },
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to remove payment method');
  }

  return response.json();
};

export const createPaymentIntent = async (
  data: CreatePaymentIntentRequest
): Promise<PaymentIntentResponse> => {
  const response = await fetch(`${API_URL}/api/billing/payment-intent`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create payment intent');
  }

  return response.json();
};

export const getTransactionHistory = async (
  page = 1,
  limit = 10
): Promise<TransactionResponse> => {
  const response = await fetch(
    `${API_URL}/api/billing/transactions?page=${page}&limit=${limit}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('access_token')}`,
      },
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch transaction history');
  }

  return response.json();
};

export const processPayment = async (
  data: ProcessPaymentRequest
): Promise<ProcessPaymentResponse> => {
  const response = await fetch(`${API_URL}/api/billing/process-payment`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to process payment');
  }

  return response.json();
};

