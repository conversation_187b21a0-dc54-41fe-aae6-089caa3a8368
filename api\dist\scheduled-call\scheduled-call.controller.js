"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduledCallController = void 0;
const common_1 = require("@nestjs/common");
const scheduled_call_service_1 = require("./scheduled-call.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const user_decorator_1 = require("../auth/decorators/user.decorator");
const users_service_1 = require("../users/users.service");
let ScheduledCallController = class ScheduledCallController {
    constructor(scheduledCallService, usersService) {
        this.scheduledCallService = scheduledCallService;
        this.usersService = usersService;
    }
    async createScheduledCall(payload, user, res) {
        try {
            const hasSufficientCredits = await this.usersService.hasSufficientCredits(user.userId, 1);
            if (!hasSufficientCredits) {
                return res.status(common_1.HttpStatus.PAYMENT_REQUIRED).json({
                    error: "Insufficient credits. Please add funds to your account.",
                });
            }
            if (payload.scheduledByName !== "agent") {
                payload.scheduledByName = user.fullName || "User";
            }
            const scheduledCall = await this.scheduledCallService.createScheduledCall(payload);
            await this.usersService.deductCredits(user.userId, 1);
            return res.status(common_1.HttpStatus.OK).json({
                message: "Scheduled call created successfully",
                scheduledCall,
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
    async getScheduledCalls(res, page, limit, search, filter) {
        try {
            const pageNum = page ? +page : undefined;
            const limitNum = limit ? +limit : undefined;
            const scheduledCalls = await this.scheduledCallService.getScheduledCalls(pageNum, limitNum, search, filter);
            return res.status(common_1.HttpStatus.OK).json(scheduledCalls);
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
    async getScheduledCallById(id, res) {
        try {
            const scheduledCall = await this.scheduledCallService.getScheduledCallById(id);
            if (!scheduledCall) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    error: "Scheduled call not found",
                });
            }
            return res.status(common_1.HttpStatus.OK).json(scheduledCall);
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
    async updateScheduledCall(id, updateData, res) {
        try {
            const updatedCall = await this.scheduledCallService.updateScheduledCall(id, updateData);
            if (!updatedCall) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    error: "Scheduled call not found",
                });
            }
            return res.status(common_1.HttpStatus.OK).json({
                message: "Scheduled call updated successfully",
                scheduledCall: updatedCall,
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
    async deleteScheduledCall(id, res) {
        try {
            const result = await this.scheduledCallService.deleteScheduledCall(id);
            if (!result) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    error: "Scheduled call not found",
                });
            }
            return res.status(common_1.HttpStatus.OK).json({
                message: "Scheduled call deleted successfully",
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
    async removeDuplicateCalls(payload, res) {
        try {
            const { agentId } = payload;
            const result = await this.scheduledCallService.removeDuplicateCalls(agentId);
            return res.status(common_1.HttpStatus.OK).json({
                message: `Successfully removed ${result.duplicatesRemoved} duplicate calls`,
                ...result
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
    async rescheduleCampaignCalls(payload, res) {
        try {
            const { agentId, concurrentCalls, batchIntervalMinutes, callWindow } = payload;
            const result = await this.scheduledCallService.rescheduleCampaignCalls(agentId, concurrentCalls, batchIntervalMinutes, callWindow);
            return res.status(common_1.HttpStatus.OK).json({
                message: `Successfully processed ${result.totalCount} calls (${result.rescheduledCount} rescheduled, ${result.duplicatesRemoved} duplicates removed)`,
                ...result
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
};
exports.ScheduledCallController = ScheduledCallController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.User)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], ScheduledCallController.prototype, "createScheduledCall", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('search')),
    __param(4, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String]),
    __metadata("design:returntype", Promise)
], ScheduledCallController.prototype, "getScheduledCalls", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ScheduledCallController.prototype, "getScheduledCallById", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ScheduledCallController.prototype, "updateScheduledCall", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ScheduledCallController.prototype, "deleteScheduledCall", null);
__decorate([
    (0, common_1.Post)("remove-duplicates"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'superadmin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ScheduledCallController.prototype, "removeDuplicateCalls", null);
__decorate([
    (0, common_1.Post)("reschedule-campaign"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superadmin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ScheduledCallController.prototype, "rescheduleCampaignCalls", null);
exports.ScheduledCallController = ScheduledCallController = __decorate([
    (0, common_1.Controller)("scheduled-call"),
    __metadata("design:paramtypes", [scheduled_call_service_1.ScheduledCallService,
        users_service_1.UsersService])
], ScheduledCallController);
//# sourceMappingURL=scheduled-call.controller.js.map