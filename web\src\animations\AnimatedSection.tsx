"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface AnimatedSectionProps {
  children: ReactNode;
  className?: string;
  animation?: "fade" | "slide" | "scale" | "stagger";
  direction?: "up" | "down" | "left" | "right" | "none";
  delay?: number;
  duration?: number;
  once?: boolean;
  staggerChildren?: number;
}

export default function AnimatedSection({
  children,
  className = "",
  animation = "fade",
  direction = "up",
  delay = 0,
  duration = 0.5,
  once = true,
  staggerChildren = 0.1,
}: AnimatedSectionProps) {
  // Prepare animations based on type and direction
  let initialPosition = {};
  
  if (direction !== "none" && (animation === "fade" || animation === "slide")) {
    const distance = animation === "slide" ? 100 : 30;
    initialPosition = {
      y: direction === "up" ? distance : direction === "down" ? -distance : 0,
      x: direction === "left" ? distance : direction === "right" ? -distance : 0,
    };
  }
  
  // Animation variants
  const variants = {
    hidden: {
      opacity: 0,
      ...(animation === "scale" ? { scale: 0.95 } : {}),
      ...initialPosition,
    },
    visible: {
      opacity: 1,
      ...(animation === "scale" ? { scale: 1 } : {}),
      y: 0,
      x: 0,
      transition: {
        duration: duration,
        delay: delay,
        ease: "easeOut",
        when: "beforeChildren",
        staggerChildren: animation === "stagger" ? staggerChildren : 0,
      },
    },
  };

  // Child variants for stagger animations
  const childVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.section
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once, amount: 0.1 }}
      variants={variants}
    >
      {animation === "stagger" 
        ? Array.isArray(children) 
          ? children.map((child, i) => (
              <motion.div key={i} variants={childVariants}>
                {child}
              </motion.div>
            ))
          : <motion.div variants={childVariants}>{children}</motion.div>
        : children}
    </motion.section>
  );
}