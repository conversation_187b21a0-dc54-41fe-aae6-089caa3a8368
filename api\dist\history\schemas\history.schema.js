"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistorySchema = void 0;
const mongoose_1 = require("mongoose");
exports.HistorySchema = new mongoose_1.Schema({
    fullName: { type: String, required: true },
    mobileNumber: { type: String, required: true },
    interest: { type: String },
    timezone: { type: String, default: null },
    callTranscript: { type: String },
    callSummary: { type: String },
    callStartTime: { type: Date },
    callEndTime: { type: Date },
    callDuration: { type: String },
    callRoute: { type: String, default: null },
    callPurpose: { type: String, default: null },
    callEndReason: { type: String },
    callCost: { type: String },
    bookedStatus: { type: String, default: null },
    confirmedStatus: { type: String, default: null },
    additionalQuestions: { type: String, default: null },
    recordingUrl: { type: String },
    preferredProject: { type: String },
    preferredLocation: { type: String },
    preferredUnitType: { type: String },
    projectType: { type: String },
    investmentType: { type: String },
    budget: { type: String },
    recentContact: { type: Boolean },
    emotions: {
        type: String,
        enum: [
            "Positive",
            "Neutral",
            "Slightly Positive",
            "Slightly Negative",
            "Negative",
        ],
        default: "Neutral",
    },
    agent: { type: String },
    brokenPromise: { type: String },
    callBackLanguage: { type: String },
    callBackRequest: { type: String },
    claimedPaidAwaitingPOP: { type: String },
    doNotCall: { type: String },
    followingPaymentPlan: { type: String },
    fullyPaid: { type: String },
    fullyPaidByPDC: { type: String },
    incorrectContactDetails: { type: String },
    mortgage: { type: String },
    notResponding: { type: String },
    notRespondingSOASent: { type: String },
    notWillingToPay: { type: String },
    popRaised: { type: String },
    promiseToPay: { type: String },
    promiseToPayPartial: { type: String },
    refuseToPay: { type: String },
    thirdParty: { type: String },
    willingToPay: { type: String },
    Response: { type: String },
    Channel: { type: String },
    GuestRequest: { type: String },
    Notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
}, {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    id: false,
});
exports.HistorySchema.pre("save", function (next) {
    this.updatedAt = new Date();
    next();
});
exports.HistorySchema.statics.getTotalCount = async function () {
    return await this.countDocuments();
};
exports.HistorySchema.virtual('totalCalls').get(function () {
    return this._totalCount;
});
exports.HistorySchema.virtual('filteredCalls').get(function () {
    return this._filteredCount;
});
exports.HistorySchema.index({ callStartTime: 1 });
exports.HistorySchema.index({ agent: 1 });
exports.HistorySchema.index({ callStartTime: 1, agent: 1 });
//# sourceMappingURL=history.schema.js.map