/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  ArrowLeft,
  CreditCard,
  Loader2,
  Search,
  FileText,
  Banknote,
  CheckCircle2,
  Trash2,
} from "lucide-react";
import FadeIn from "@/animations/FadeIn";
import {
  getTransactionHistory,
  getPaymentMethods,
  setDefaultPaymentMethod,
  removePaymentMethod,
  Transaction,
  PaymentMethod,
} from "@/app/api/billing";
import { getUserCredits } from "@/app/api/users";
import StripeProvider from "@/components/stripe/StripeProvider";
import CardPaymentForm from "@/components/stripe/CardPaymentForm";
import SavedPaymentMethods from "@/components/stripe/SavedPaymentMethods";
import { AutoRechargeSettings } from "@/components/stripe/AutoRechargeSettings";
import { SuccessDialog } from "@/components/ui/success-dialog";

// Format date to readable format
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Payment method options
const paymentMethodOptions = [
  {
    id: "card",
    name: "Credit Card",
    icon: <CreditCard className="h-5 w-5" />,
  },
  {
    id: "paypal",
    name: "PayPal",
    icon: <Banknote className="h-5 w-5" />,
  },
];

export default function BillingContent() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("add-funds");
  const [paymentMethod, setPaymentMethod] = useState("card");
  const [amount, setAmount] = useState("25");
  const [customAmount, setCustomAmount] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [showNewCardForm, setShowNewCardForm] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState({
    title: "",
    description: "",
  });
  const [userCredits, setUserCredits] = useState<{
    freeMinutesRemaining: number;
    paidMinutes: number;
    totalMinutesAvailable: number;
    usingFreeCredits: boolean;
    callPricePerMinute: number;
    monthlyResetDate: number;
    monthlyAllowance: number;
  }>({
    freeMinutesRemaining: 0,
    paidMinutes: 0,
    totalMinutesAvailable: 0,
    usingFreeCredits: false,
    callPricePerMinute: 0.1,
    monthlyResetDate: 1,
    monthlyAllowance: 0,
  });

  // Memoize the amount in cents to prevent unnecessary re-renders
  const [amountInCents, setAmountInCents] = useState<number>(
    parseInt(amount) * 100
  );

  // Memoize the validation result for custom amount to prevent layout shifts
  const [isCustomAmountValid, setIsCustomAmountValid] = useState(true);

  // Update validation and amount in cents when amount or customAmount changes
  useEffect(() => {
    if (amount === "custom") {
      const amountValue = parseFloat(customAmount || "0");
      const isValid = amountValue >= 10 && !isNaN(amountValue);
      setIsCustomAmountValid(isValid);
      setAmountInCents(Math.round(amountValue * 100));
    } else {
      setAmountInCents(parseInt(amount) * 100);
    }
  }, [amount, customAmount]);

  // Function to refresh user credits
  const refreshUserCredits = async () => {
    try {
      const response = await getUserCredits();
      console.log("Refreshed user credits:", response);
      setUserCredits({
        freeMinutesRemaining: response.freeMinutesRemaining || 0,
        paidMinutes: response.paidMinutes || 0,
        totalMinutesAvailable: response.totalMinutesAvailable || 0,
        usingFreeCredits: response.usingFreeCredits || false,
        callPricePerMinute: response.callPricePerMinute || 0.1,
        monthlyResetDate: response.monthlyResetDate || 1,
        monthlyAllowance: response.monthlyAllowance || 0,
      });
      return response;
    } catch (err) {
      console.error("Error fetching user credits:", err);
      return null;
    }
  };

  // Function to get next reset date based on organization's reset date
  const getNextResetDate = () => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Use organization's reset date (default to 1st if not available)
    const resetDay = userCredits.monthlyResetDate || 1;

    let nextResetDate = new Date(currentYear, currentMonth, resetDay);

    // If we've passed this month's reset date, move to next month
    if (now.getDate() >= resetDay) {
      nextResetDate = new Date(currentYear, currentMonth + 1, resetDay);
    }

    return nextResetDate.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Fetch transaction history and payment methods
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        if (activeTab === "payment-history") {
          const response = await getTransactionHistory();
          setTransactions(response.transactions);
        } else if (activeTab === "add-funds") {
          const methods = await getPaymentMethods();
          setPaymentMethods(methods);
          // Show new card form only if no payment methods exist
          setShowNewCardForm(methods.length === 0);
        }

        // Fetch user credits regardless of active tab
        await refreshUserCredits();
      } catch (err) {
        console.error("Error fetching data:", err);
        setError(
          activeTab === "payment-history"
            ? "Failed to load transaction history"
            : "Failed to load payment methods"
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [activeTab]);

  // Handle PayPal and Apple Pay payments
  const handleAlternativePayment = () => {
    setIsProcessing(true);
    setError("");

    try {
      // Use the memoized validation state instead of recalculating
      if (amountInCents < 1000) {
        throw new Error("Minimum amount is $10");
      }

      if (paymentMethod === "paypal") {
        // For PayPal, we would redirect to PayPal's checkout page
        alert("PayPal integration would be implemented here");
      } else if (paymentMethod === "apple") {
        // For Apple Pay, we would use Apple Pay JS
        alert("Apple Pay integration would be implemented here");
      }
    } catch (err: any) {
      console.error("Payment error:", err);
      setError(err.message || "Payment processing failed");
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle payment success
  const handlePaymentSuccess = async () => {
    // Refresh credits to show updated balance
    await refreshUserCredits();

    // Show success dialog
    setSuccessMessage({
      title: "Payment Successful",
      description: `Your payment of ${new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amountInCents / 100)} has been processed successfully.`,
    });
    setSuccessDialogOpen(true);

    // Refresh transaction history if needed
    if (activeTab === "payment-history") {
      const response = await getTransactionHistory();
      setTransactions(response.transactions);
    }
  };

  return (
    <FadeIn>
      {/* Success Dialog */}
      <SuccessDialog
        open={successDialogOpen}
        onOpenChange={setSuccessDialogOpen}
        title={successMessage.title}
        description={successMessage.description}
      />
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
            className="rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">Back</span>
          </Button>

          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Billing & Payments
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage your payment methods and view transaction history
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left column - Plan details & Payment options */}
          <div className="lg:col-span-1 space-y-4">
            {/* Current Plan */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Current Plan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Header Section */}
                <div className="space-y-4">
                  {userCredits.monthlyAllowance > 0 ? (
                    // Original layout when monthly allowance exists
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Monthly Allowance
                        </p>
                        <p className="text-2xl font-bold text-foreground">
                          {userCredits.monthlyAllowance.toFixed(0)}{" "}
                          <span className="text-lg font-normal text-muted-foreground">
                            minutes
                          </span>
                        </p>
                      </div>
                      {userCredits.paidMinutes > 0 && (
                        <div className="text-right">
                          <p className="text-xs font-medium text-muted-foreground mb-1">
                            Balance
                          </p>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                            <span className="text-lg font-semibold text-emerald-600 dark:text-emerald-400">
                              $
                              {(
                                userCredits.paidMinutes *
                                userCredits.callPricePerMinute
                              ).toFixed(2)}
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            ({userCredits.paidMinutes.toFixed(0)} min)
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    // Improved layout when no monthly allowance (pay-per-use)
                    <div className="text-center space-y-3">
                      {userCredits.paidMinutes > 0 ? (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground mb-2">
                            Current Balance
                          </p>
                          <div className="flex items-center justify-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
                            <span className="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
                              $
                              {(
                                userCredits.paidMinutes *
                                userCredits.callPricePerMinute
                              ).toFixed(2)}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {userCredits.paidMinutes.toFixed(0)} minutes available
                          </p>
                        </div>
                      ) : (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground mb-2">
                            Current Balance
                          </p>
                          <div className="flex items-center justify-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                            <span className="text-3xl font-bold text-gray-500 dark:text-gray-400">
                              $0.00
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            Add funds to start making calls
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Progress Section */}
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-emerald-700 dark:text-emerald-400">
                      Minutes Remaining
                    </span>
                    <span className="font-medium text-emerald-600 dark:text-emerald-400">
                      {userCredits.totalMinutesAvailable.toFixed(0)} min
                    </span>
                  </div>
                  <div className="h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-emerald-500 rounded-full"
                      style={{
                        width: `${Math.min(
                          100,
                          (userCredits.totalMinutesAvailable /
                            Math.max(userCredits.monthlyAllowance, 1)) *
                            100
                        )}%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* Reset Date */}
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Next Reset Date
                      </p>
                      <p className="text-sm font-semibold">
                        {getNextResetDate()}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment info card */}
            <Card>
              <CardHeader className="pb-2 flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Payment Methods</CardTitle>
                  <CardDescription>
                    Manage your saved payment methods
                  </CardDescription>
                </div>
                {activeTab === "add-funds" && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowNewCardForm(true)}
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    Add New Card
                  </Button>
                )}
              </CardHeader>
              <CardContent className="pt-2">
                {isLoading ? (
                  <div className="flex justify-center py-6">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : paymentMethods.length === 0 ? (
                  <div className="text-center py-6">
                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md inline-flex mb-3">
                      <CreditCard className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <p className="text-muted-foreground">
                      No payment methods saved
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => {
                        setActiveTab("add-funds");
                        setShowNewCardForm(true);
                      }}
                    >
                      Add Payment Method
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {paymentMethods.map((method) => (
                      <div
                        key={method._id}
                        className={`flex items-center justify-between p-3 rounded-lg border ${
                          method.isDefault
                            ? "border-primary/50 bg-primary/5"
                            : "border-border"
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`p-2 rounded-md ${
                              method.isDefault
                                ? "bg-primary/10"
                                : "bg-gray-100 dark:bg-gray-800"
                            }`}
                          >
                            <CreditCard
                              className={`h-5 w-5 ${
                                method.isDefault ? "text-primary" : ""
                              }`}
                            />
                          </div>
                          <div>
                            <div className="flex items-center">
                              <p className="font-medium">•••• {method.last4}</p>
                              {method.isDefault && (
                                <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                                  Default
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {method.brand &&
                                method.brand.charAt(0).toUpperCase() +
                                  method.brand.slice(1)}{" "}
                              • Expires {method.expMonth}/{method.expYear}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-1">
                          {!method.isDefault && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={async () => {
                                try {
                                  await setDefaultPaymentMethod(method._id);
                                  // Refresh payment methods
                                  const methods = await getPaymentMethods();
                                  setPaymentMethods(methods);
                                } catch (err) {
                                  console.error(
                                    "Error setting default payment method:",
                                    err
                                  );
                                  setError(
                                    "Failed to set default payment method"
                                  );
                                }
                              }}
                              title="Set as default"
                            >
                              <CheckCircle2 className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={async () => {
                              try {
                                await removePaymentMethod(method._id);
                                // Refresh payment methods
                                const methods = await getPaymentMethods();
                                setPaymentMethods(methods);
                              } catch (err) {
                                console.error(
                                  "Error removing payment method:",
                                  err
                                );
                                setError("Failed to remove payment method");
                              }
                            }}
                            title="Remove payment method"
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right column - Add funds & Payment history */}
          <div className="lg:col-span-2">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="space-y-6"
            >
              <TabsList className="grid grid-cols-3">
                <TabsTrigger value="add-funds">Add Funds</TabsTrigger>
                <TabsTrigger value="payment-history">
                  Payment History
                </TabsTrigger>
                <TabsTrigger value="auto-recharge">Auto-Recharge</TabsTrigger>
              </TabsList>

              <TabsContent value="add-funds" className="space-y-6">
                {/* Amount selection */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Select Amount</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {/* Show error message if preset amount is below minimum */}
                    {amount !== "custom" && parseInt(amount) < 10 && (
                      <p className="text-red-500 text-sm mb-3">
                        Minimum amount is $10
                      </p>
                    )}

                    <RadioGroup
                      value={amount}
                      onValueChange={setAmount}
                      className="grid grid-cols-3 gap-4"
                    >
                      <div>
                        <RadioGroupItem
                          value="25"
                          id="amount-25"
                          className="peer sr-only"
                        />
                        <Label
                          htmlFor="amount-25"
                          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                        >
                          <span className="text-xs text-muted-foreground mb-1">
                            Basic
                          </span>
                          <span className="text-2xl font-bold">$25</span>
                        </Label>
                      </div>

                      <div>
                        <RadioGroupItem
                          value="50"
                          id="amount-50"
                          className="peer sr-only"
                        />
                        <Label
                          htmlFor="amount-50"
                          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                        >
                          <span className="text-xs text-muted-foreground mb-1">
                            Standard
                          </span>
                          <span className="text-2xl font-bold">$50</span>
                        </Label>
                      </div>

                      <div>
                        <RadioGroupItem
                          value="100"
                          id="amount-100"
                          className="peer sr-only"
                        />
                        <Label
                          htmlFor="amount-100"
                          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                        >
                          <span className="text-xs text-muted-foreground mb-1">
                            Plus
                          </span>
                          <span className="text-2xl font-bold">$100</span>
                        </Label>
                      </div>
                    </RadioGroup>

                    <div className="mt-4 relative">
                      <Label htmlFor="custom-amount" className="text-sm">
                        Custom Amount
                      </Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                          $
                        </span>
                        <Input
                          id="custom-amount"
                          type="number"
                          min="10"
                          placeholder="Enter amount (min $10)"
                          className={`pl-7 ${
                            amount === "custom" && !isCustomAmountValid
                              ? "border-red-500"
                              : ""
                          }`}
                          value={customAmount}
                          onChange={(e) => setCustomAmount(e.target.value)}
                          onClick={() => setAmount("custom")}
                        />
                      </div>
                      {amount === "custom" && !isCustomAmountValid && (
                        <p className="text-red-500 text-sm mt-1">
                          Minimum amount is $10
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Payment methods */}
                {/* <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Payment Method</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RadioGroup
                      value={paymentMethod}
                      onValueChange={setPaymentMethod}
                      className="grid grid-cols-3 gap-4"
                    >
                      {paymentMethodOptions.map((method) => (
                        <div key={method.id}>
                          <RadioGroupItem
                            value={method.id}
                            id={`method-${method.id}`}
                            className="peer sr-only"
                          />
                          <Label
                            htmlFor={`method-${method.id}`}
                            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                          >
                            <div className="mb-2">{method.icon}</div>
                            <span className="text-sm">{method.name}</span>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </CardContent>
                </Card> */}

                {/* Credit Card Form - only show if card selected */}
                {paymentMethod === "card" && (
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle className="text-lg">Card Details</CardTitle>
                      {paymentMethods.length > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowNewCardForm(!showNewCardForm)}
                        >
                          {showNewCardForm ? "Use Saved Card" : "Use New Card"}
                        </Button>
                      )}
                    </CardHeader>
                    <CardContent>
                      <StripeProvider>
                        {error && (
                          <div className="text-red-500 text-sm mb-4">
                            {error}
                          </div>
                        )}

                        {/* No large warning message as requested */}

                        {showNewCardForm ? (
                          <CardPaymentForm
                            amount={amountInCents}
                            currency="usd"
                            description={`Adding funds: $${
                              amount === "custom" ? customAmount : amount
                            }`}
                            onSuccess={async () => {
                              // Payment will be blocked on the server side if below minimum
                              // but we'll also check here for better UX
                              if (
                                amount === "custom"
                                  ? !isCustomAmountValid
                                  : parseInt(amount) < 10
                              ) {
                                setError("Minimum amount is $10");
                                return;
                              }

                              // Use our centralized payment success handler
                              await handlePaymentSuccess();
                            }}
                            onError={(message) => {
                              setError(message);
                            }}
                          />
                        ) : (
                          <SavedPaymentMethods
                            amount={amountInCents}
                            currency="usd"
                            description={`Adding funds: $${
                              amount === "custom" ? customAmount : amount
                            }`}
                            onSuccess={async () => {
                              // Payment will be blocked on the server side if below minimum
                              // but we'll also check here for better UX
                              if (
                                amount === "custom"
                                  ? !isCustomAmountValid
                                  : parseInt(amount) < 10
                              ) {
                                setError("Minimum amount is $10");
                                return;
                              }

                              // Use our centralized payment success handler
                              await handlePaymentSuccess();
                            }}
                            onError={(message) => {
                              setError(message);
                            }}
                          />
                        )}
                      </StripeProvider>
                    </CardContent>
                  </Card>
                )}

                {/* PayPal Message */}
                {paymentMethod === "paypal" && (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <p className="text-lg font-medium mb-4">
                        Continue to PayPal to complete your payment
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 mb-6">
                        You&apos;ll be redirected to PayPal to complete your
                        payment securely.
                      </p>
                      <Button
                        onClick={handleAlternativePayment}
                        disabled={
                          isProcessing ||
                          (amount === "custom"
                            ? !isCustomAmountValid
                            : parseInt(amount) < 10)
                        }
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          `Pay with PayPal`
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                )}

                {/* Apple Pay Message */}
                {paymentMethod === "apple" && (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <p className="text-lg font-medium mb-4">
                        Continue with Apple Pay
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 mb-6">
                        You&apos;ll be prompted to authenticate your payment
                        with Apple Pay.
                      </p>
                      <Button
                        onClick={handleAlternativePayment}
                        disabled={
                          isProcessing ||
                          (amount === "custom"
                            ? !isCustomAmountValid
                            : parseInt(amount) < 10)
                        }
                        className="bg-black hover:bg-gray-800"
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          `Pay with Apple Pay`
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="auto-recharge">
                <AutoRechargeSettings />
              </TabsContent>

              <TabsContent value="payment-history">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Payment History</CardTitle>
                    <CardDescription>
                      View all your past transactions
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center mb-6">
                      <div className="relative w-64">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                        <Input
                          placeholder="Search transactions..."
                          className="pl-8"
                        />
                      </div>
                    </div>

                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>ID</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead className="text-right">Amount</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">
                              Actions
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {isLoading ? (
                            <TableRow>
                              <TableCell
                                colSpan={6}
                                className="text-center py-8"
                              >
                                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                                <p className="mt-2 text-sm text-muted-foreground">
                                  Loading transaction history...
                                </p>
                              </TableCell>
                            </TableRow>
                          ) : error ? (
                            <TableRow>
                              <TableCell
                                colSpan={6}
                                className="text-center py-8 text-red-500"
                              >
                                {error}
                              </TableCell>
                            </TableRow>
                          ) : transactions.length === 0 ? (
                            <TableRow>
                              <TableCell
                                colSpan={6}
                                className="text-center py-8"
                              >
                                <p className="text-muted-foreground">
                                  No transactions found
                                </p>
                              </TableCell>
                            </TableRow>
                          ) : (
                            transactions.map((transaction) => (
                              <TableRow key={transaction._id}>
                                <TableCell>
                                  {formatDate(transaction.createdAt)}
                                </TableCell>
                                <TableCell>
                                  {transaction.stripePaymentIntentId.substring(
                                    0,
                                    8
                                  )}
                                </TableCell>
                                <TableCell className="max-w-[200px] truncate">
                                  {transaction.email}
                                </TableCell>
                                <TableCell className="text-right font-medium">
                                  ${transaction.amount.toFixed(2)}
                                </TableCell>
                                <TableCell>
                                  <span
                                    className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                      transaction.status === "completed" ||
                                      transaction.status === "succeeded"
                                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                        : transaction.status === "failed"
                                        ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                                    }`}
                                  >
                                    {transaction.status === "completed" ||
                                    transaction.status === "succeeded"
                                      ? "Completed"
                                      : transaction.status === "failed"
                                      ? "Failed"
                                      : "Pending"}
                                  </span>
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <FileText className="h-4 w-4" />
                                    <span className="sr-only">
                                      View receipt
                                    </span>
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </FadeIn>
  );
}
