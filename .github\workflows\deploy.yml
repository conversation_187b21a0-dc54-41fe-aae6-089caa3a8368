name: Deployment

on:
  push:
    branches:
      - app
      - demo
      - binghatti-test

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      DOMAIN: ${{ github.ref == 'refs/heads/app' && 'app.orova.ai' || github.ref == 'refs/heads/demo' && 'demo.orova.ai' || 'binghatti-test.orova.ai' }}
      APP_PREFIX: ${{ github.ref == 'refs/heads/app' && 'app' || github.ref == 'refs/heads/demo' && 'demo' || 'binghatti' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set deployment directory and server
        id: set-dir
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/app" ]]; then
            echo "DEPLOY_DIR=orova-v/app.orova.ai" >> $GITHUB_ENV
            echo "USE_DEV_SERVER=false" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == "refs/heads/demo" ]]; then
            echo "DEPLOY_DIR=orova-v/demo.orova.ai" >> $GITHUB_ENV
            echo "USE_DEV_SERVER=false" >> $GITHUB_ENV
          else
            echo "DEPLOY_DIR=orova-v" >> $GITHUB_ENV
            echo "USE_DEV_SERVER=true" >> $GITHUB_ENV
          fi

      - name: Remote deployment (Production/Demo server)
        if: env.USE_DEV_SERVER == 'false'
        uses: appleboy/ssh-action@v0.1.8
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          envs: DEPLOY_DIR,DOMAIN,APP_PREFIX
          script: |
            cd $DEPLOY_DIR
            git pull

            # Ensure required environment variables are set
            if [ -z "$DOMAIN" ] || [ -z "$APP_PREFIX" ]; then
              echo "Error: Required environment variables DOMAIN and APP_PREFIX must be set."
              exit 1
            fi

            # Create or update .env file with environment-specific variables
            cat > .env << EOL
            # Domain configuration
            DOMAIN=$DOMAIN

            # Application prefix for unique Traefik router names
            APP_PREFIX=$APP_PREFIX

            # This file was automatically generated by GitHub Actions
            # Last updated: $(date)
            EOL

            docker compose build
            docker compose up -d

      - name: Remote deployment (Binghatti Test server)
        if: env.USE_DEV_SERVER == 'true'
        uses: appleboy/ssh-action@v0.1.8
        with:
          host: ${{ secrets.DEVHOST }}
          username: ${{ secrets.DEVUSERNAME }}
          password: ${{ secrets.DEVPASSWORD }}
          envs: DEPLOY_DIR,DOMAIN,APP_PREFIX
          script: |
            cd $DEPLOY_DIR
            git pull

            # Ensure required environment variables are set
            if [ -z "$DOMAIN" ] || [ -z "$APP_PREFIX" ]; then
              echo "Error: Required environment variables DOMAIN and APP_PREFIX must be set."
              exit 1
            fi

            # Create or update .env file with environment-specific variables
            cat > .env << EOL
            # Domain configuration
            DOMAIN=$DOMAIN

            # Application prefix for unique Traefik router names
            APP_PREFIX=$APP_PREFIX

            # This file was automatically generated by GitHub Actions
            # Last updated: $(date)
            EOL

            docker compose build
            docker compose up -d
