import { Schema } from 'mongoose';
export declare const LogSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    message: string;
    level: "INFO" | "WARN" | "ERROR";
    timestamp: NativeDate;
    trace: string;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    message: string;
    level: "INFO" | "WARN" | "ERROR";
    timestamp: NativeDate;
    trace: string;
}>> & import("mongoose").FlatRecord<{
    message: string;
    level: "INFO" | "WARN" | "ERROR";
    timestamp: NativeDate;
    trace: string;
}> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
