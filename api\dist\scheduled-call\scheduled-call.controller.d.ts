import { Response } from "express";
import { ScheduledCallService } from "./scheduled-call.service";
import { FullUserType } from "src/auth/interceptors/user-redaction.interceptor";
import { UsersService } from "src/users/users.service";
export declare class ScheduledCallController {
    private readonly scheduledCallService;
    private readonly usersService;
    constructor(scheduledCallService: ScheduledCallService, usersService: UsersService);
    createScheduledCall(payload: {
        agentId: string;
        contacts: any[];
        scheduledTime: string;
        region: string;
        scheduledByName: string;
    }, user: FullUserType, res: Response): Promise<Response<any, Record<string, any>>>;
    getScheduledCalls(res: Response, page?: string, limit?: string, search?: string, filter?: 'all' | 'today' | 'thisWeek' | 'weekend' | 'nextWeek' | 'upcoming' | 'past'): Promise<Response<any, Record<string, any>>>;
    getScheduledCallById(id: string, res: Response): Promise<Response<any, Record<string, any>>>;
    updateScheduledCall(id: string, updateData: {
        agentId?: string;
        contacts?: {
            Name: string;
            MobileNumber: string;
        }[];
        scheduledTime?: Date;
        region?: string;
        status?: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    deleteScheduledCall(id: string, res: Response): Promise<Response<any, Record<string, any>>>;
    removeDuplicateCalls(payload: {
        agentId: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    rescheduleCampaignCalls(payload: {
        agentId: string;
        concurrentCalls: number;
        batchIntervalMinutes: number;
        callWindow: {
            startTime: string;
            endTime: string;
            daysOfWeek: string[];
        };
    }, res: Response): Promise<Response<any, Record<string, any>>>;
}
