services:
  mongodb:
    image: mongo:latest
    restart: always
    env_file:
      - ./mongodb/.env
    volumes:
      - ./mongodb/data:/data/db
    networks:
      - internal

  nestjs:
    build:
      context: ./api
      dockerfile: Dockerfile
    restart: always
    env_file:
      - ./api/.env
    depends_on:
      - mongodb
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_PREFIX}_api.rule=Host(`${DOMAIN}`) && PathPrefix(`/api`)"
      - "traefik.http.routers.${APP_PREFIX}_api.entrypoints=websecure"
      - "traefik.http.routers.${APP_PREFIX}_api.tls=true"
      - "traefik.http.routers.${APP_PREFIX}_api.tls.certresolver=orovaairesolver"
      - "traefik.http.services.${APP_PREFIX}_api.loadbalancer.server.port=4000"
    networks:
      - internal
      - traefik

  nextjs:
    build:
      context: ./web
      dockerfile: Dockerfile
    restart: always
    env_file:
      - ./web/.env
    depends_on:
      - nestjs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_PREFIX}_frontend.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.${APP_PREFIX}_frontend.entrypoints=websecure"
      - "traefik.http.routers.${APP_PREFIX}_frontend.tls=true"
      - "traefik.http.routers.${APP_PREFIX}_frontend.tls.certresolver=orovaairesolver"
      - "traefik.http.services.${APP_PREFIX}_frontend.loadbalancer.server.port=3000"
      - "traefik.http.routers.${APP_PREFIX}_frontend.priority=1"
    networks:
      - internal
      - traefik

  webhook:
    build:
      context: ./webhook
      dockerfile: Dockerfile
    restart: always
    depends_on:
      - nestjs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_PREFIX}_webhook.rule=Host(`${DOMAIN}`) && PathPrefix(`/webhook`)"
      - "traefik.http.routers.${APP_PREFIX}_webhook.entrypoints=websecure"
      - "traefik.http.routers.${APP_PREFIX}_webhook.tls=true"
      - "traefik.http.routers.${APP_PREFIX}_webhook.tls.certresolver=orovaairesolver"
      - "traefik.http.services.${APP_PREFIX}_webhook.loadbalancer.server.port=5000"
    networks:
      - internal
      - traefik

  call:
    build:
      context: ./call
      dockerfile: Dockerfile
    restart: always
    depends_on:
      - nestjs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_PREFIX}_call.rule=Host(`${DOMAIN}`) && PathPrefix(`/call`)"
      - "traefik.http.routers.${APP_PREFIX}_call.entrypoints=websecure"
      - "traefik.http.routers.${APP_PREFIX}_call.tls=true"
      - "traefik.http.routers.${APP_PREFIX}_call.tls.certresolver=orovaairesolver"
      - "traefik.http.services.${APP_PREFIX}_call.loadbalancer.server.port=3000"
    networks:
      - internal
      - traefik

  mongo-express:
    image: mongo-express:latest
    restart: unless-stopped
    env_file:
      - ./mongo-express/.env
    depends_on:
      - mongodb
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${APP_PREFIX}-mongo-express.rule=Host(`db-${DOMAIN}`)"
      - "traefik.http.routers.${APP_PREFIX}-mongo-express.entrypoints=websecure"
      - "traefik.http.routers.${APP_PREFIX}-mongo-express.tls=true"
      - "traefik.http.routers.${APP_PREFIX}-mongo-express.tls.certresolver=orovaairesolver"
      - "traefik.http.services.${APP_PREFIX}-mongo-express.loadbalancer.server.port=8081"
    networks:
      - internal
      - traefik

networks:
  internal:
    driver: bridge
  traefik:
    external: true

