/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { Plus, Send, Mail, PhoneForwarded, PhoneOff, FileText, Calendar } from "lucide-react";
import { useState } from "react";
import { Agent, AgentTabProps } from "@/types/agent.types";


interface AgentActionsTabProps {
  agent: Agent;
}

type ActionType = "sms" | "email" | "transfer" | "end" | "extract" | "calendar";

interface ActionConfig {
  type: ActionType;
  icon: React.ReactNode;
  label: string;
  description: string;
  color: string;
  enabled?: boolean;
}

export default function AgentActionsTab({ agent, setAgent, phoneNumbers }: AgentTabProps) {
  const [selectedAction, setSelectedAction] = useState<ActionType | null>(null);
  const [dialogTab, setDialogTab] = useState("config");
  
  // Track enabled actions
  const [actionConfigs, setActionConfigs] = useState<Record<ActionType, ActionConfig>>({
    sms: {
      type: "sms",
      icon: <Send className="h-4 w-4" />,
      label: "Send SMS",
      description: "Send SMS messages to customers",
      color: "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300",
      enabled: true,
    },
    email: {
      type: "email",
      icon: <Mail className="h-4 w-4" />,
      label: "Send Email",
      description: "Send email communications",
      color: "bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300",
      enabled: true,
    },
    transfer: {
      type: "transfer",
      icon: <PhoneForwarded className="h-4 w-4" />,
      label: "Transfer Call",
      description: "Transfer calls to other agents",
      color: "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300",
      enabled: true,
    },
    end: {
      type: "end",
      icon: <PhoneOff className="h-4 w-4" />,
      label: "End Call",
      description: "End the current call",
      color: "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300",
      enabled: true,
    },
    extract: {
      type: "extract",
      icon: <FileText className="h-4 w-4" />,
      label: "Extract Info",
      description: "Extract information from conversations",
      color: "bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300",
      enabled: true,
    },
    calendar: {
      type: "calendar",
      icon: <Calendar className="h-4 w-4" />,
      label: "Calendar",
      description: "Schedule appointments and meetings",
      color: "bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300",
      enabled: true,
    },
  });
  
  // Form state for action configuration
  const [actionName, setActionName] = useState("");
  const [callType, setCallType] = useState("both");
  const [trigger, setTrigger] = useState("end");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [content, setContent] = useState("");
  const [emailTemplate, setEmailTemplate] = useState("followup");

  const handleActionClick = (type: ActionType) => {
    setSelectedAction(type);
    setDialogTab("config");
    
    // Reset form fields
    setActionName(`${actionConfigs[type].label} Action`);
    setCallType("both");
    setTrigger("end");
    setPhoneNumber("");
    setContent("");
    setEmailTemplate("followup");
  };

  const handleAddAction = () => {
    // For demo purposes, we'll just show the dialog with a default action
    setSelectedAction("sms");
    setActionName("New SMS Action");
    setDialogTab("config");
  };

  const handleCloseDialog = () => {
    setSelectedAction(null);
  };

  const handleSaveAction = () => {
    // In a real app, we would save the action configuration
    handleCloseDialog();
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
        <h3 className="text-lg font-semibold">Actions Configuration</h3>
       
      </div>

    </div>
  );
}