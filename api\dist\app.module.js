"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const jwt_1 = require("@nestjs/jwt");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const users_module_1 = require("./users/users.module");
const auth_module_1 = require("./auth/auth.module");
const agent_module_1 = require("./agent/agent.module");
const ai_module_1 = require("./ai/ai.module");
const conversations_module_1 = require("./conversations/conversations.module");
const vapi_module_1 = require("./vapi/vapi.module");
const contacts_module_1 = require("./contacts/contacts.module");
const history_module_1 = require("./history/history.module");
const campaign_module_1 = require("./campaign/campaign.module");
const scheduled_call_module_1 = require("./scheduled-call/scheduled-call.module");
const schedule_1 = require("@nestjs/schedule");
const serve_static_1 = require("@nestjs/serve-static");
const token_module_1 = require("./token/token.module");
const dahboard_module_1 = require("./dashboard/dahboard.module");
const billing_module_1 = require("./billing/billing.module");
const credit_module_1 = require("./credit/credit.module");
const global_settings_module_1 = require("./global-settings/global-settings.module");
const organizations_module_1 = require("./organizations/organizations.module");
const websocket_module_1 = require("./websocket/websocket.module");
const phone_number_module_1 = require("./phone-number/phone-number.module");
const email_module_1 = require("./email/email.module");
const notifications_module_1 = require("./notifications/notifications.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            schedule_1.ScheduleModule.forRoot(),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    uri: configService.get("MONGODB_URI"),
                }),
                inject: [config_1.ConfigService],
            }),
            serve_static_1.ServeStaticModule.forRoot({
                rootPath: '/usr/src/app/uploads',
                serveRoot: '/api/uploads',
                serveStaticOptions: {
                    index: false,
                    fallthrough: true,
                }
            }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    secret: configService.get("JWT_SECRET"),
                    signOptions: { expiresIn: "60m" },
                }),
                inject: [config_1.ConfigService],
            }),
            users_module_1.UsersModule,
            auth_module_1.AuthModule,
            agent_module_1.AgentModule,
            ai_module_1.AiModule,
            conversations_module_1.ConversationsModule,
            vapi_module_1.VapiModule,
            contacts_module_1.ContactsModule,
            history_module_1.HistoryModule,
            campaign_module_1.CampaignModule,
            scheduled_call_module_1.ScheduledCallModule,
            token_module_1.TokenModule,
            dahboard_module_1.DashboardModule,
            billing_module_1.BillingModule,
            credit_module_1.CreditModule,
            global_settings_module_1.GlobalSettingsModule,
            organizations_module_1.OrganizationsModule,
            websocket_module_1.WebsocketModule,
            phone_number_module_1.PhoneNumberModule,
            email_module_1.EmailModule,
            notifications_module_1.NotificationsModule
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map