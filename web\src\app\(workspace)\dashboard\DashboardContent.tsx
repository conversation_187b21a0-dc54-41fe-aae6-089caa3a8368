/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable @next/next/no-img-element */
"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {Select,SelectContent,SelectItem,SelectTrigger,SelectValue} from "@/components/ui/select";
import {Phone,Clock,Info,Calendar,PhoneCall,PhoneOutgoing,BarChart,Users,PhoneOff, UserX, VoicemailIcon, HelpCircle, PhoneIncoming, RefreshCw} from "lucide-react";
import { ResponsiveContainer, AreaChart, Area } from "recharts";
import { useEffect, useState } from "react";
import agentLisa from "@/assets/img/Binghatti-Lisa.jpeg";
import FadeIn from "@/animations/FadeIn";
import { format, formatDistanceToNow } from "date-fns";
import Link from "next/link";
import Image from "next/image";
import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import { DashboardData } from "@/types/dashboard.types";




function DashboardContent() {

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>("all");
  const [selectedAgentType, setSelectedAgentType] = useState<string>("all");
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  

  // Fetch all data
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const token = localStorage.getItem('access_token');
        if (!token) {
          console.error("No access token available");
          return;
        }
        
        const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch user profile: ${response.status}`);
        }
        
        const userData = await response.json();
        setUserRole(userData.role);
      } catch (err) {
        console.error("Failed to load user profile:", err);
      }
    };
    
    fetchUserProfile();
  }, []);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        const token = localStorage.getItem('access_token');
        if (!token) {
          console.error("No access token available");
          setError("Authentication required. Please log in again.");
          setLoading(false);
          return;
        }
        
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/dashboard/metrics?timeRange=${selectedTimeRange}&agentType=${selectedAgentType}`, 
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          }
        );
        
        if (!response.ok) {
          throw new Error(`Failed to fetch dashboard data: ${response.status}`);
        }
        
        const data = await response.json();
        setDashboardData(data);
        
      } catch (err) {
        console.error("Failed to load dashboard data:", err);
        setError("Error loading dashboard data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [selectedTimeRange, selectedAgentType]);

//   const refreshDashboardStats = async () => {
//   setLoading(true);
//   try {
//     const token = localStorage.getItem('access_token');
//     if (!token) {
//       console.error("No access token available");
//       setLoading(false);
//       return;
//     }
    
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/dashboard/refresh-stats`, 
//       {
//         method: 'POST',
//         headers: {
//           'Authorization': `Bearer ${token}`,
//           'Content-Type': 'application/json',
//         },
//       }
//     );
    
//     if (!response.ok) {
//       throw new Error(`Failed to refresh dashboard stats: ${response.status}`);
//     }
    
//     // Fetch fresh data after clearing cache
//     const dataResponse = await fetch(
//       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/dashboard/metrics?timeRange=${selectedTimeRange}&agentType=${selectedAgentType}`, 
//       {
//         headers: {
//           'Authorization': `Bearer ${token}`,
//           'Content-Type': 'application/json',
//         },
//       }
//     );
    
//     if (!dataResponse.ok) {
//       throw new Error(`Failed to fetch dashboard data: ${dataResponse.status}`);
//     }
    
//     const data = await dataResponse.json();
//     setDashboardData(data);
    
//   } catch (err) {
//     console.error("Failed to refresh dashboard stats:", err);
    
//   } finally {
//     setLoading(false);
//   }
// };

  const formatCallDuration = (durationMs: number): string => {
    const totalSeconds = Math.round(durationMs / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}m ${seconds.toString().padStart(2, '0')}s`;
  };


  const formatDuration = (durationMs: string | number) => {
    const ms = typeof durationMs === 'string' ? parseInt(durationMs) : durationMs;
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };
  

  const connectionRateSparkline = Array.from({ length: 10 }, (_, i) => {
    const baseValue = dashboardData?.callMetrics.connectionRate || 0;
    let value = baseValue;
    
    if (i < 4) {
      value = baseValue - (13 - i);
    } else if (i > 8) {
      value = baseValue + (i - 2);
    } else if (i === 6) {
      value = baseValue + 2;
    }
    
    return {
      day: i + 1,
      value: Math.max(0, Math.min(100, value))
    };
  });


  const getReasonDetails = (reason: string) => {
    switch (reason) {
      case 'customer-busy':
        return { icon: <Clock className="h-4 w-4" />, label: 'Customer Busy' };
      case 'customer-ended-call':
        return { icon: <PhoneOff className="h-4 w-4" />, label: 'Customer Ended The call' };
      case 'assistant-ended-call':
        return { icon: <UserX className="h-4 w-4" />, label: 'Agent Ended The call' };
      case 'customer-did-not-answer':
        return { icon: <PhoneOff className="h-4 w-4" />, label: 'Customer Did not Answer' };
         case 'customer-out-of-reach':
        return { icon: <PhoneOff className="h-4 w-4" />, label: 'Customer Out Of Reach' };
      case 'voicemail':
        return { icon: <VoicemailIcon className="h-4 w-4" />, label: 'Voicemail' };
      case 'silence-timed-out':
        return { icon: <Clock className="h-4 w-4" />, label: 'Silence Timed Out' };
      default:
        return { icon: <HelpCircle className="h-4 w-4" />, label: "Others (Timed out or failed to reach)" };
    }
  };
  
 
  const answerRateSparklineData = Array.from({ length: 10 }, (_, i) => {
    const baseValue = dashboardData?.callMetrics.answerRate || 0;
    let value = baseValue;
    
    if (i < 3) {
      value = baseValue - (10 - i);
    } else if (i > 7) {
      value = baseValue + (i - 5);
    } else if (i === 5) {
      value = baseValue + 3;
    }
    
    return {
      day: i + 1,
      value: Math.max(0, Math.min(100, value))
    };
  });


  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
        <div className="w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
          <p className="text-lg font-medium">Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
        <div className="p-4 bg-red-50 text-red-500 rounded-lg">
          <p>Error loading dashboard: {error}</p>
          <button 
            className="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  

  return (
    <>
    <FadeIn direction="up">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
      <h1 className="text-2xl font-bold">
      Dashboard {selectedAgentType !== "all" && `(${selectedAgentType} Agents)`} 
          {selectedTimeRange !== "all" && ` | Last ${selectedTimeRange} days`}
      </h1>
        
        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full">

             {/* <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    onClick={refreshDashboardStats}
                    disabled={loading}
                    className="h-9 w-9"
                  >
                    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                    <span className="sr-only">Refresh dashboard statistics</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Refresh dashboard statistics</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider> */}

          <Select defaultValue="30" 
            value={selectedTimeRange}
            onValueChange={setSelectedTimeRange}
            >
            <SelectTrigger className="h-9 w-full sm:w-[180px]">
              <SelectValue placeholder="Last 30 days" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="14">Last 14 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              
            </SelectContent>
          </Select>
{/* 
            <Select defaultValue="all" >
              <SelectTrigger className="h-9 w-full sm:w-[180px]">
                <SelectValue placeholder="All Workflows" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Campaigns</SelectItem>
                <SelectItem value="sales">Sales</SelectItem>
                <SelectItem value="support">Support</SelectItem>
              </SelectContent>
            </Select> */}

            <Select 
                defaultValue="all" 
                value={selectedAgentType}
                onValueChange={setSelectedAgentType}
              >
                <SelectTrigger className="h-9 w-full sm:w-[180px]">
              <SelectValue placeholder="All Agents">
                {selectedAgentType === "all" ? "All Agents" : `${selectedAgentType} Agents`}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Agents</SelectItem>
              {dashboardData?.agentRoles && dashboardData.agentRoles.map((role) => (
                <SelectItem key={role} value={role}>
                  {role} Agents
                </SelectItem>
              ))} 
            </SelectContent>
              </Select>
          </div>

         
        </div>
      </div>

      {/* Top Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6 mb-6">
      <Card className="border rounded-lg ">
      <CardContent className="pt-6">
        <div className="flex justify-between items-start mb-2">
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Total Calls
            </h3>
            <div className="flex items-baseline">
            <span className="text-2xl font-bold mr-2">{dashboardData?.callMetrics.totalCalls || 0}</span>
            </div>
          </div>
          <Phone className="h-5 w-5 text-gray-400" />
        </div>
      </CardContent>
    </Card>

    

        <Card className="border rounded-lg ">
          <CardContent className="pt-6">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Call in Minutes
                </h3>
                <div className="flex items-baseline">
                <span className="text-2xl font-bold mr-1">
                {dashboardData?.callMetrics.totalMinutes.toFixed(1) || 0}
              </span>
                  <span className="text-sm text-gray-500">min</span>
                </div>
              </div>
              <Clock className="h-5 w-5 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="border rounded-lg ">
          <CardContent className="pt-6">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Av. Call Length
                </h3>
                <div className="flex items-baseline">
                  <span className="text-2xl font-bold mr-1">{formatCallDuration(dashboardData?.callMetrics.averageLength || 0)}</span>
                  <span className="text-sm text-gray-500">sec</span>
                </div>
              </div>
              <Clock className="h-5 w-5 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="border rounded-lg ">
        <CardContent className="pt-6">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Call Engagement Rate
              </h3>
              <div className="flex items-baseline">
                <span className="text-2xl font-bold mr-1">
                {dashboardData?.callMetrics.connectionRate || 0}%
                </span>
              </div>
            </div>
            <PhoneOutgoing className="h-5 w-5 text-gray-400" />
          </div>
          <FadeIn delay={0.3} direction="right">    
          <div className="h-12 mt-2">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={connectionRateSparkline}
                margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
              >
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke="#4157ea"
                  strokeWidth={2}
                  fill="#4157ea20"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
          </FadeIn>
        </CardContent>
      </Card>
      </div>

      {/* Call Answer Rate, Conversion Gain, New Calls Today, and Follow-up Calls */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
      <Card className="border rounded-lg">
        <CardContent className="pt-6">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Total Campaigns 
            </h3>
            <BarChart className="h-5 w-5 text-gray-400" />
          </div>
          <div className="flex items-baseline">
          <span className="text-2xl font-bold mr-2">{dashboardData?.totalCounts.totalCampaigns || 0}</span>
          <span className="text-lg text-gray-500">campaigns</span>
        </div>
        </CardContent>
      </Card>

    <Card className="border rounded-lg">
      <CardContent className="pt-6">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Total Scheduled Calls
          </h3>
          <Calendar className="h-5 w-5 text-gray-400" />
        </div>
        <div className="flex items-baseline">
        <span className="text-2xl font-bold mr-2">{dashboardData?.totalCounts.totalScheduledCalls || 0}</span>
          <span className="text-lg text-gray-500">scheduled</span>
        </div>
      </CardContent>
    </Card>

        <Card className="border rounded-lg">
          <CardContent className="pt-6">
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Contacts
              </h3>
              <Info className="h-5 w-5 text-gray-400" />
            </div>
            <div className="flex items-baseline">
            <div className="text-2xl font-bold mr-2">{dashboardData?.totalCounts.totalContacts || 0}</div>     
          <span className="text-lg text-gray-500">contacts</span>
        </div>
            
            {/* <div className="h-10">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={sparklineData}
                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                >
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#4157ea"
                    strokeWidth={2}
                    fill="#4157ea20"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div> */}
          </CardContent>
        </Card>

        <Card className="border rounded-lg">
  <CardContent className="pt-6">
    <div className="flex justify-between items-start mb-2">
      <div>
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
          Answer Call Rate
        </h3>
        <div className="flex items-baseline">
          <span className="text-2xl font-bold mr-1">
          {dashboardData?.callMetrics.answerRate || 0}%
          </span>
        </div>
      </div>
      <PhoneIncoming className="h-5 w-5 text-gray-400" />
    </div>
    <FadeIn delay={0.3} direction="right">         
    <div className="h-10">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={answerRateSparklineData}
          margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
        >
          <Area
            type="monotone"
            dataKey="value"
            stroke="#4157ea"
            strokeWidth={2}
            fill="#4157ea20"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
    </FadeIn>
  </CardContent>
</Card>
      </div>

      {/* Most Common Lost Reasons and Topics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6">
      <Card className="border rounded-lg">
  <CardContent className="pt-6">
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-sm font-medium">Recent Calls {selectedAgentType !== "all" && `(${selectedAgentType})`}</h3>
      <Badge
        variant="outline"
        className="bg-blue-50 text-blue-800 hover:bg-blue-50 border-none"
      >
        {dashboardData?.callMetrics.totalCalls || 0} calls 
      </Badge>
    </div>
    <div className="space-y-4">
      {dashboardData?.recentCalls.map((call) => (
        <div
          key={call._id}
          className="flex items-center justify-between border-b pb-3"
        >
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              <PhoneCall className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium"> {call.fullName} | {call.mobileNumber}</p>
              <p className="text-xs text-gray-400">
                {formatDistanceToNow(new Date(call.callStartTime), { addSuffix: true })}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            <PhoneOutgoing className="h-3.5 w-3.5 text-blue-600 mr-2" />
          <Badge
            variant="outline"
            className="bg-blue-100 text-blue-800 hover:bg-blue-100 border-none mr-1"
          >
            {formatDuration(call.callDuration)}
          </Badge>
        </div>
        </div>
      ))}
      <Link href="/history">
      <Button
        variant="ghost"
        size="sm"
        className="w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50"
        >
        View all calls
      </Button>
        </Link>
    </div>
  </CardContent>
</Card>

<Card className="border rounded-lg">
  <CardContent className="pt-6">
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-sm font-medium">
        Last Scheduled Cost {selectedAgentType !== "all" && `(${selectedAgentType})`}
      </h3>
      <Badge
        variant="outline"
        className="bg-amber-50 text-amber-800 hover:bg-amber-50 border-none"
      >
        {dashboardData?.recentSchedules.length || 0} scheduled
      </Badge>
    </div>
    <div className="space-y-4">
      {dashboardData?.recentSchedules.slice(0, 3).slice(0, 3).map((schedule) => (
        <div
          key={schedule._id}
          className="flex items-center justify-between border-b pb-3"
        >
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3">
              <Calendar className="h-4 w-4 text-amber-600" />
            </div>
            <div>
              <p className="text-sm font-medium">{schedule.contacts[0]?.Name || "N/A"} | {schedule.contacts[0]?.MobileNumber || "N/A"}</p>
              <p className="text-xs text-gray-400">
                {format(new Date(schedule.scheduledTime), "MMM d, yyyy h:mm a")} • {schedule.region || "N/A"}
              </p>
            </div>
          </div>
          <Badge
            variant="outline"
            className={`
              ${
                schedule.status === "executed"
                  ? "bg-green-100 text-green-800 hover:bg-green-100"
                  : schedule.status === "pending"
                  ? "bg-amber-100 text-yellow-800 hover:bg-yellow-100"
                  : "bg-red-100 text-red-800 hover:bg-red-100"
              } border-none
            `}
          >
            {schedule.status || "pending"}
          </Badge>
        </div>
      ))}
      <Link href="/schedule">
      <Button
        variant="ghost"
        size="sm"
        className="w-full text-amber-600 hover:text-amber-700 hover:bg-amber-50"    
        >
        View all scheduled calls
      </Button>
        </Link>
    </div>
  </CardContent>
</Card>
          </div>

      {/* Sentiment Overview and Call Logs */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
      <Card className="border rounded-lg ">
        <CardContent className="pt-3">
          <h3 className="text-sm font-medium mb-6"> Sentiment Overview {selectedAgentType !== "all" && `(${selectedAgentType})`} </h3>
          <div className="grid grid-cols-3 gap-4 text-center mt-9">
            <div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">😊</span>
              </div>
              <h4 className="text-sm">Positive</h4>
              <p className="text-xl font-bold">{dashboardData?.sentiments.positive || 0}%</p>
            </div>

            <div>
              <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">😐</span>
              </div>
              <h4 className="text-sm">Neutral</h4>
              <p className="text-xl font-bold">{dashboardData?.sentiments.neutral || 0}%</p>
            </div>

            <div>
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">😞</span>
              </div>
              <h4 className="text-sm">Negative</h4>
              <p className="text-xl font-bold">{dashboardData?.sentiments.negative || 0}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border rounded-lg">
      <CardContent className="pt-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-sm font-medium">  Recent Campaigns {selectedAgentType !== "all" && `(${selectedAgentType})`} </h3>
          <Badge
            variant="outline"
            className="bg-green-50 text-green-800 hover:bg-green-50 border-none"
          >
            {dashboardData?.recentCampaigns.length || 0} campaigns
          </Badge>
        </div>
        <div className="space-y-4">
          {dashboardData?.recentCampaigns.slice(0, 3).map((campaign, index) => (
            <div
              key={campaign._id || index}
              className="flex items-center justify-between border-b pb-3"
            >
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                  <Users className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">{campaign.name}</p>
                  <p className="text-xs text-gray-400">
                    {format(new Date(campaign.startDate), "MMM d, yyyy")} - {campaign.endDate ? format(new Date(campaign.endDate), "MMM d, yyyy") : "No End Date"}
                  </p>
                </div>
              </div>
              <Badge
                variant="outline"
                className={`
                  ${
                    campaign.status === "active"
                      ? "bg-green-100 text-green-800 hover:bg-green-100"
                      : campaign.status === "paused"
                      ? "bg-amber-100 text-amber-800 hover:bg-amber-100"
                      : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                  } border-none
                `}
              >
                {campaign.status === "active" ? "Active" : 
                campaign.status === "paused" ? "Paused" : 
                campaign.status === "completed" ? "Completed" : "Inactive"}
              </Badge>
            </div>
          ))}
          <Link href="/campaign">
          <Button
            variant="ghost"
            size="sm"
            className="w-full text-green-600 hover:text-green-700 hover:bg-green-50"
            >
            View all campaigns
          </Button>
            </Link>
        </div>
      </CardContent>
    </Card>

    <Card className="border rounded-lg">
  <CardContent className="pt-6">
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-sm font-medium">Most Used Agents {selectedAgentType !== "all" && `(${selectedAgentType})`}</h3>
      <Badge
        variant="outline"
        className="bg-purple-50 text-purple-800 hover:bg-purple-50 border-none"
      >
       {userRole === "superadmin" 
          ? selectedAgentType === "all" 
            ? `${dashboardData?.topAgents.length} agents` 
            : `${dashboardData?.topAgents.filter(agent => agent.role === selectedAgentType).length} agents`
          : selectedAgentType === "all"
            ? `${dashboardData?.topAgents.filter(agent => agent.status === "active").length} agents`
            : `${dashboardData?.topAgents.filter(agent => agent.status === "active" && agent.role === selectedAgentType).length} agents`
        }
      </Badge>
    </div>
    <div className="space-y-4">
    {dashboardData?.topAgents.slice(0, 5).map((agent, index) => (
      <div
        key={agent.id || index}
        className="flex items-center justify-between border-b pb-3"
      >
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
            {agent.avatar ? (
              <img 
                src={agent.avatar}
                alt={`${agent.name} avatar`}
                className="h-full w-full rounded-full object-cover"
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = agentLisa.src;                  
                }}
              />
            ) : (
              <Image 
                src={agentLisa}
                alt={`${agent?.name} avatar`}
                width={64}
                height={64}
                className="object-cover h-full w-full"
              />
            )}
          </div>
          <div>
            <p className="text-sm font-medium">{agent?.name || "Agent"}</p>
            <p className="text-xs text-gray-400">
              {agent?.role || "Assistant"}
            </p>
          </div>
        </div>
        <Badge
          variant="outline"
          className="bg-purple-100 text-purple-800 hover:bg-purple-100 border-none"
        >
          {agent?.callCount || 0} calls
        </Badge>
      </div>
    ))}
  <Link href="/agents">
    <Button
      variant="ghost"
      size="sm"
      className="w-full text-purple-600 hover:text-purple-700 hover:bg-purple-50"
    >
      View all agents
    </Button>
  </Link>
</div>
  </CardContent>
</Card>

<Card className="border rounded-lg">
  <CardContent className="pt-6">
    <h3 className="text-sm font-medium mb-4">Call End Reasons {selectedAgentType !== "all" && `(${selectedAgentType})`}</h3>
    <div className="space-y-4">
      {dashboardData?.callEndReasons.slice(0, 6).map((item, index) => (
        <div
          key={index}
          className="flex items-center justify-between border-b pb-3"
        >
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              {getReasonDetails(item.reason).icon}
            </div>
            <div>
              <p className="text-sm font-medium">{getReasonDetails(item.reason).label}</p>
              <p className="text-xs text-gray-400">
              {String(item.count)} calls
              </p>
            </div>
          </div>
          <Badge
            variant="outline"
            className="bg-blue-100 text-blue-800 hover:bg-blue-100 border-none"
          >
            {item.percentage}%
          </Badge>
        </div>
      ))}
    </div>
  </CardContent>
</Card>
      </div>

       </FadeIn>
    </>
  );
}

export default DashboardContent;
