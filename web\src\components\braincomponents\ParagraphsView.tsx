"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Trash2 } from "lucide-react";
import { EmptyState } from "./EmptyState";

type Paragraph = {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
};

type ParagraphsViewProps = {
  paragraphs: Paragraph[];
  isEmpty: boolean;
  onAddClick: () => void;
  onDelete: (id: string) => void;
};

export function ParagraphsView({ paragraphs, isEmpty, onAddClick, onDelete }: ParagraphsViewProps) {
  if (isEmpty) {
    return <EmptyState type="paragraph" onAdd={onAddClick} />;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {paragraphs.map((paragraph) => (
        <Card key={paragraph.id} className="overflow-hidden">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="font-semibold text-lg">{paragraph.title}</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  {paragraph.tags.map((tag) => (
                    <Badge key={tag} variant="secondary">{tag}</Badge>
                  ))}
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="icon" 
                className="text-red-500 hover:text-red-700 dark:text-red-400 hover:dark:text-red-300"
                onClick={() => onDelete(paragraph.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-gray-600 dark:text-gray-300 line-clamp-3">{paragraph.content}</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-4">
              Added {paragraph.createdAt.toLocaleDateString()}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}