import { Schema, Types } from "mongoose";

export const ContactSchema = new Schema({
  contactName: { type: String, required: true },
  phoneNumber: { type: String, required: true },
  customerId: { type: String },
  lastCall: { type: Date },
  campaigns: [{ type: Types.ObjectId, ref: "Campaign", }],
  campaignNames: [{ type: String }], // For storing campaign names directly
  region: { type: String },
  updatedAt: { type: Date, default: Date.now },

  projectName: { type: String },
  unitNumber: { type: String},
  totalPayableAmount: { type: Number},
  pendingPayableAmount: { type: Number },
  dueDate: { type: Date},
  totalInstallments: { type: Number},
  paymentType: { type: String },
  pendingInstallments: { type: Number},
  lastPaymentDate: { type: Date },
  lastPaymentAmount: { type: Number },
  lastPaymentType: { type: String },
  collectionBucket: { type: String },
  unitPrice: { type: Number },
  paidAmtIncluding: { type: Number },
  unansweredCalls: {
    type: Number,
    default: 0,
    min: [0, "Unanswered calls cannot be negative"]
  },

  // Event related fields
  eventDate: { type: String },
  eventLocation: { type: String },
  eventTime: { type: String },
  nameOfRegistrant: { type: String },
  addedBy: { type: String},
  createdAt: { 
    type: Date, 
    default: Date.now,
    immutable: true // This ensures the creation date can't be modified
  },
  source: {
    type: String,
    enum: ['manual', 'file Upload', 'CRM', '-'],
    default: '-'
  },
},{
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
  id: false,
});

ContactSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// static method to the schema to get the total count
ContactSchema.statics.getTotalCount = async function() {
  return await this.countDocuments();
};

ContactSchema.virtual('totalContacts').get(function(this: any) {
  return this._totalCount;
});

ContactSchema.virtual('filteredContacts').get(function(this: any) {
  return this._filteredCount;
});
// Create a compound index on contactName and phoneNumber
// This allows contacts with the same name but different phone numbers
// or same phone number but different names
ContactSchema.index({ contactName: 1, phoneNumber: 1 }, { unique: true });
