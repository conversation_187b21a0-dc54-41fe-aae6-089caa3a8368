/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { Card } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>Chart, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { useState } from "react";
import { AgentTabProps } from "@/types/agent.types";

// Mock data for the charts
const mockData = [
  { date: "Feb 15", calls: 24, duration: 45, satisfaction: 4.5 },
  { date: "Feb 16", calls: 32, duration: 38, satisfaction: 4.7 },
  { date: "Feb 17", calls: 28, duration: 42, satisfaction: 4.3 },
  { date: "Feb 18", calls: 35, duration: 40, satisfaction: 4.6 },
  { date: "Feb 19", calls: 30, duration: 43, satisfaction: 4.8 },
  { date: "Feb 20", calls: 26, duration: 41, satisfaction: 4.4 },
  { date: "Feb 21", calls: 33, duration: 39, satisfaction: 4.9 },
];

export default function AgentPerformanceTab( { agent, setAgent }: AgentTabProps) {
  const [timeRange, setTimeRange] = useState("7days");
  
  return (
    <>
    <div className="space-y-6 p-6">
      {/* Time Range Selector */}
      <div className="flex flex-col sm:flex-row justify-between sm:items-center space-y-2 sm:space-y-0">
        <h3 className="text-lg font-semibold">Performance Analytics</h3>
        <Select 
          value={timeRange} 
          onValueChange={setTimeRange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="24h">Last 24 hours</SelectItem>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="custom">Custom range</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-sm text-muted-foreground">Total Calls</div>
          <div className="text-2xl font-bold mt-1">208</div>
          <div className="text-xs text-green-500 mt-1">+12% vs last week</div>
        </Card>
        <Card className="p-4">
          <div className="text-sm text-muted-foreground">Avg. Call Duration</div>
          <div className="text-2xl font-bold mt-1">41m</div>
          <div className="text-xs text-red-500 mt-1">-3% vs last week</div>
        </Card>
        <Card className="p-4">
          <div className="text-sm text-muted-foreground">Satisfaction Score</div>
          <div className="text-2xl font-bold mt-1">4.6</div>
          <div className="text-xs text-green-500 mt-1">+0.2 vs last week</div>
        </Card>
        <Card className="p-4">
          <div className="text-sm text-muted-foreground">Response Rate</div>
          <div className="text-2xl font-bold mt-1">98%</div>
          <div className="text-xs text-muted-foreground mt-1">Same as last week</div>
        </Card>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Calls Volume Chart */}
        <Card className="p-6">
          <h4 className="text-lg font-semibold mb-4">Calls Volume</h4>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={mockData}>
                <CartesianGrid strokeDasharray="3 3" opacity={0.5} />
                <XAxis 
                  dataKey="date" 
                  tickLine={false}
                  axisLine={false}
                  dy={10}
                />
                <YAxis 
                  tickLine={false}
                  axisLine={false}
                  dx={-10}
                />
                <Tooltip 
                  contentStyle={{ 
                    borderRadius: '8px', 
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Legend wrapperStyle={{ paddingTop: '10px' }} />
                <Line
                  type="monotone"
                  dataKey="calls"
                  stroke="#8884d8"
                  strokeWidth={2}
                  activeDot={{ r: 6 }}
                  dot={{ r: 3 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Average Duration Chart */}
        <Card className="p-6">
          <h4 className="text-lg font-semibold mb-4">Average Call Duration</h4>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={mockData}>
                <CartesianGrid strokeDasharray="3 3" opacity={0.5} />
                <XAxis 
                  dataKey="date" 
                  tickLine={false}
                  axisLine={false}
                  dy={10}
                />
                <YAxis 
                  tickLine={false}
                  axisLine={false}
                  dx={-10}
                />
                <Tooltip 
                  contentStyle={{ 
                    borderRadius: '8px', 
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Legend wrapperStyle={{ paddingTop: '10px' }} />
                <Line
                  type="monotone"
                  dataKey="duration"
                  stroke="#82ca9d"
                  strokeWidth={2}
                  activeDot={{ r: 6 }}
                  dot={{ r: 3 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Satisfaction Score Chart */}
        <Card className="p-6">
          <h4 className="text-lg font-semibold mb-4">Satisfaction Score</h4>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={mockData}>
                <CartesianGrid strokeDasharray="3 3" opacity={0.5} />
                <XAxis 
                  dataKey="date" 
                  tickLine={false}
                  axisLine={false}
                  dy={10}
                />
                <YAxis 
                  domain={[0, 5]} 
                  tickLine={false}
                  axisLine={false}
                  dx={-10}
                />
                <Tooltip 
                  contentStyle={{ 
                    borderRadius: '8px', 
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Legend wrapperStyle={{ paddingTop: '10px' }} />
                <Line
                  type="monotone"
                  dataKey="satisfaction"
                  stroke="#ffc658"
                  strokeWidth={2}
                  activeDot={{ r: 6 }}
                  dot={{ r: 3 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>
    </div>
    </>
  );
}