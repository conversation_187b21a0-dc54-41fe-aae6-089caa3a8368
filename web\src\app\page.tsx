/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import AnimatedSection from "../animations/AnimatedSection";
import FadeIn from "@/animations/FadeIn";
import ScaleIn from "@/animations/ScaleIn";
import SlideIn from "@/animations/SlideIn";
import StaggerContainer from "@/animations/StaggerContainer";
import CTASection from "@/components/CTASection";
import FAQSection from "@/components/FAQSection";
import FeaturesGrid from "@/components/FeaturesGrid";
import Footer from "@/components/Footer";
import HeroSection from "@/components/HeroSection";
import Navigation from "@/components/Navigation";
import PricingSection from "@/components/PricingSection";
import Testimonials from "@/components/Testimonials";
import Login from "./(auth)/login/page";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { authFetch } from "@/lib/authFetch";

const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

export default function Home() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is already logged in
    async function checkAuthStatus() {
      try {
        // Check for token in localStorage
        const token = localStorage.getItem("access_token");
        
        if (!token) {
          // No token found, stay on login page
          setLoading(false);
          return;
        }

        // Verify token validity by making a request to the API
        const response = await authFetch(`${API_BASE_URL}/api/auth/me`);
        
        if (response.ok) {
          // User is authenticated, redirect to dashboard
          setIsAuthenticated(true);
          router.push("/dashboard");
        } else {
          // Invalid token, stay on login page
          setLoading(false);
        }
      } catch (error) {
        console.error("Error checking authentication status:", error);
        setLoading(false);
      }
    }

    checkAuthStatus();
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-12 w-12 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin"></div>
          <p className="text-lg font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
    {/* <div className="min-h-screen bg-white dark:bg-gray-950 text-gray-900 dark:text-white ">
     
      <Navigation />
    
      <FadeIn>
        <HeroSection />
      </FadeIn>
  
      <AnimatedSection delay={0.5} animation="stagger">
        <FeaturesGrid />
      </AnimatedSection>
   
      <SlideIn delay={0.5} direction="up">
        <Testimonials />
      </SlideIn>
   
      <ScaleIn delay={0.5}>
        <PricingSection />
      </ScaleIn>
  
      <StaggerContainer delay={0.5}>
        <FAQSection />
      </StaggerContainer>
    
      <FadeIn delay={0.5} direction="up" distance={50}>
        <CTASection />
      </FadeIn>
    
      <Footer />
    </div> */}
   <Login/>
    </>
  );
}
