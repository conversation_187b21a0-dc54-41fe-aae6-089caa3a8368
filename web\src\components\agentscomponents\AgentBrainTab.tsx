/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { AgentTabProps } from "@/types/agent.types";


export default function AgentBrainTab( { agent, setAgent, phoneNumbers }: AgentTabProps) {
 

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
        <h3 className="text-lg font-semibold">Brain Configuration</h3>
       
      </div>

    </div>
  );
}