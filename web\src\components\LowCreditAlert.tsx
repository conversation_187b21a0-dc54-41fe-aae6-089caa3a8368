"use client";

import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useCredits } from "@/contexts/CreditContext";

interface LowCreditAlertProps {
  credits?: number;
  threshold?: number;
  minutes?: number;
}

export function LowCreditAlert({ credits: propCredits, threshold: propThreshold }: LowCreditAlertProps) {
  const router = useRouter();
  const {
    totalAvailable,
    organizationCreditThreshold,
    isLoading,
    totalMinutesAvailable,
    callPricePerMinute,
    monthlyAllowance,
    isConnected,
    hasValidData,
    lastSuccessfulFetch
  } = useCredits();

  // Use props if provided, otherwise use context values
  const credits = propCredits !== undefined ? propCredits : totalAvailable;
  // Use threshold prop if provided, otherwise use organization threshold from context
  const threshold = propThreshold !== undefined ? propThreshold : organizationCreditThreshold;

  // Don't show anything while loading
  if (isLoading) {
    return null;
  }

  // Don't show alert if we don't have valid data or connection issues
  if (!hasValidData) {
    return null;
  }

  // If connection is lost and we have recent data, don't show scary alerts
  const isRecentData = lastSuccessfulFetch && (Date.now() - lastSuccessfulFetch < 2 * 60 * 1000); // 2 minutes
  if (!isConnected && !isRecentData) {
    return null;
  }

  // Only show the alert if credits are below the threshold
  if (credits >= threshold) {
    return null;
  }

  // Convert credits to minutes for display
  const minutesRemaining = callPricePerMinute > 0 ? credits / callPricePerMinute : 0;
  const hasMonthlyAllowance = monthlyAllowance > 0;

  return (
    <div className="flex justify-center mb-5">
      <Alert className="border-red-200 bg-red-50 dark:bg-red-900/10 dark:border-red-800 p-1.5 w-auto">
        <AlertDescription className="text-red-700 dark:text-red-200">
          <div className="flex items-center gap-3">
            <AlertCircle className="h-4 w-4 text-orange-600 dark:text-red-400 flex-shrink-0" />
            <div>
              <span className="text-sm font-medium">
                {hasMonthlyAllowance
                  ? `${Math.floor(totalMinutesAvailable)} minutes remaining`
                  : `$${credits.toFixed(2)} balance (${Math.floor(minutesRemaining)} min) it should be atleast $${threshold.toFixed(0)}`
                }
              </span>
              <span className="text-xs text-red-600 dark:text-red-400 ml-2">
                Add funds to continue making calls
              </span>
            </div>
            <Button
              size="sm"
              variant="outline"
              className="h-7 px-3 text-xs border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-900/20 flex-shrink-0"
              onClick={() => router.push('/billing')}
            >
              Add Funds
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
}
