import {
    Call<PERSON><PERSON><PERSON>,
    ExecutionContext,
    Injectable,
    NestInterceptor,
  } from '@nestjs/common';
  import { Observable } from 'rxjs';
  import { map } from 'rxjs/operators';
  
  export interface FullUserType {
    userId?: string;
    email?: string;
    role?: string;
    fullName?: string;
    password?: string;
    isApproved?: boolean;
    refreshToken?: string;
  }
  
  function flattenUserObject(user: FullUserType): { [key: string]: any } {
    const flatUser: { [key: string]: any } = {};
  
    function flatten(obj: { [key: string]: any }) {
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          if (
            typeof obj[key] === 'object' &&
            !(obj[key] instanceof Date) &&
            obj[key] !== null &&
            !Array.isArray(obj[key])
          ) {
            flatten(obj[key]);
          } else {
            flatUser[key] = obj[key];
          }
        }
      }
    }
  
    flatten(user);
    return flatUser;
  }
  
  const excludeFields = (user: FullUserType) => {
    const flatUser = flattenUserObject(user);
    const {
      password,
      isApproved,
      refreshToken,
      updatedAt,
      ...rest
    } = flatUser;
    return {
      ...rest,
      fullName: flatUser.fullName 
    };
  };
  
  @Injectable()
  export class UserRedactionInterceptor implements NestInterceptor {
    intercept(_context: ExecutionContext, next: CallHandler): Observable<any> {
      return next.handle().pipe(
        map((payload) => {
          const data = payload.data ? payload.data : payload;
          if (payload.data) {
            return {
              ...payload,
              data: Array.isArray(data)
                ? data.map((user: FullUserType) => excludeFields(user))
                : excludeFields(data),
            };
          } else {
            return Array.isArray(data)
              ? data.map((user: FullUserType) => excludeFields(user))
              : excludeFields(data);
          }
        }),
      );
    }
  }
  