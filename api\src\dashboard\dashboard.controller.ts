import { Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Dashboard')
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('metrics')
  @ApiOperation({ summary: 'Get dashboard metrics and statistics' })
  @ApiResponse({ status: 200, description: 'Dashboard metrics retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range in days (e.g., 7, 30, all)' })
  @ApiQuery({ name: 'agentType', required: false, description: 'Filter by agent type/role' })
  async getDashboardMetrics(
    @Query('timeRange') timeRange: string = 'all',
    @Query('agentType') agentType: string = 'all',
  ) {
    return this.dashboardService.getDashboardMetrics(timeRange, agentType);
  }

  @Post('refresh-stats')
  @ApiOperation({ summary: 'Clear cached stats and force refresh' })
  @ApiResponse({ status: 200, description: 'Stats cache cleared successfully' })
  async refreshStats() {
    return this.dashboardService.clearCachedStats();
  }

}