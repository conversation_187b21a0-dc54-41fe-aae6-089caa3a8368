"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduledCallSchema = exports.ScheduledCall = void 0;
const mongoose_1 = require("@nestjs/mongoose");
let ScheduledCall = class ScheduledCall {
};
exports.ScheduledCall = ScheduledCall;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ScheduledCall.prototype, "agentId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: [{ Name: String, MobileNumber: String }] }),
    __metadata("design:type", Array)
], ScheduledCall.prototype, "contacts", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], ScheduledCall.prototype, "scheduledTime", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ScheduledCall.prototype, "region", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 'pending' }),
    __metadata("design:type", String)
], ScheduledCall.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], ScheduledCall.prototype, "scheduledByName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], ScheduledCall.prototype, "scheduledByTimestamp", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], ScheduledCall.prototype, "retryCount", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], ScheduledCall.prototype, "lastProcessedAt", void 0);
exports.ScheduledCall = ScheduledCall = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], ScheduledCall);
exports.ScheduledCallSchema = mongoose_1.SchemaFactory.createForClass(ScheduledCall);
exports.ScheduledCallSchema.set('toJSON', {
    virtuals: true,
    transform: (doc, ret) => {
        delete ret.id;
        return ret;
    }
});
exports.ScheduledCallSchema.set('toObject', {
    virtuals: true,
    transform: (doc, ret) => {
        delete ret.id;
        return ret;
    }
});
exports.ScheduledCallSchema.statics.getTotalCount = async function () {
    return await this.countDocuments();
};
exports.ScheduledCallSchema.virtual('totalSchedules').get(function () {
    return this._totalCount;
});
//# sourceMappingURL=scheduled-call.schema.js.map