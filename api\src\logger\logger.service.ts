import { Injectable, Logger, Scope } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LogDocument } from './interfaces/log.interface';
import { Cron } from '@nestjs/schedule';

@Injectable({ scope: Scope.DEFAULT })
export class LoggerService {
  private logger = new Logger(LoggerService.name);
  private logFilePath = path.join(__dirname, '../../logs/api.log');

  constructor(
    @InjectModel('Log') private readonly logModel: Model<LogDocument>,
  ) {
    if (!fs.existsSync(path.dirname(this.logFilePath))) {
      fs.mkdirSync(path.dirname(this.logFilePath), { recursive: true });
    }
  }

  private writeToFile(message: string) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    fs.appendFileSync(this.logFilePath, logMessage, 'utf8');
  }

  private async saveToDatabase(level: string, message: string, trace?: any) {
    // Ensure trace is always a string to prevent validation errors
    const traceString = trace ? (typeof trace === 'string' ? trace : JSON.stringify(trace)) : null;
    const logEntry = new this.logModel({ level, message, trace: traceString });
    await logEntry.save();
  }

  async log(message: string) {
    this.logger.log(message);
    this.writeToFile(`[INFO] ${message}`);
    await this.saveToDatabase('INFO', message);
  }

  async warn(message: string) {
    this.logger.warn(message);
    this.writeToFile(`[WARN] ${message}`);
    await this.saveToDatabase('WARN', message);
  }

  async error(message: string, trace?: any) {
    this.logger.error(message, trace);
    // Format trace for file logging
    const traceStr = trace ? (typeof trace === 'string' ? trace : JSON.stringify(trace)) : 'No stack trace';
    this.writeToFile(`[ERROR] ${message} - ${traceStr}`);
    await this.saveToDatabase('ERROR', message, trace);
  }

  /**
   * Delete logs older than the specified number of days
   * @param days Number of days to keep logs for (default: 4)
   */
  async deleteOldLogs(days: number = 4): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const result = await this.logModel.deleteMany({
      timestamp: { $lt: cutoffDate }
    }).exec();

    this.logger.log(`Deleted ${result.deletedCount} logs older than ${days} days`);
    return result.deletedCount;
  }

  /**
   * Scheduled task to clean up old logs daily at midnight
   */
  @Cron('0 0 * * *') // Run at midnight every day
  async scheduledLogCleanup() {
    try {
      const deletedCount = await this.deleteOldLogs();
      this.logger.log(`Scheduled cleanup: Removed ${deletedCount} logs older than 4 days`);
    } catch (error) {
      this.logger.error(`Error during scheduled log cleanup: ${error.message}`, error.stack);
    }
  }
}
