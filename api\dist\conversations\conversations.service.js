"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const ai_service_1 = require("../ai/ai.service");
let ConversationsService = class ConversationsService {
    constructor(conversationModel, aiService) {
        this.conversationModel = conversationModel;
        this.aiService = aiService;
    }
    async startConversation(agentId, userId, type) {
        const conversation = new this.conversationModel({
            agentId,
            userId,
            type,
            status: 'active',
        });
        return conversation.save();
    }
    async getConversationById(id) {
        const conversation = await this.conversationModel.findById(id).exec();
        if (!conversation)
            throw new common_1.NotFoundException('Conversation not found');
        return conversation;
    }
    async addUserMessage(conversationId, message) {
        const conversation = await this.getConversationById(conversationId);
        if (conversation.status === 'ended') {
            throw new Error('Cannot add messages to an ended conversation');
        }
        conversation.messages.push({
            role: 'user',
            content: message,
            timestamp: new Date(),
        });
        const userSavePromise = conversation.save();
        const historyExcludingCurrent = conversation.messages.slice(0, -1).map(m => ({
            role: m.role,
            content: m.content,
        }));
        const limitedHistory = historyExcludingCurrent.slice(-3);
        const agentReply = await this.aiService.getChatResponse(conversation.agentId.toString(), message, limitedHistory);
        conversation.messages.push({
            role: 'assistant',
            content: agentReply,
            timestamp: new Date(),
        });
        await Promise.all([userSavePromise, conversation.save()]);
        return agentReply;
    }
    async endConversation(conversationId) {
        const conversation = await this.getConversationById(conversationId);
        conversation.status = 'ended';
        conversation.endedAt = new Date();
        return conversation.save();
    }
};
exports.ConversationsService = ConversationsService;
exports.ConversationsService = ConversationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Conversation')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        ai_service_1.AiService])
], ConversationsService);
//# sourceMappingURL=conversations.service.js.map