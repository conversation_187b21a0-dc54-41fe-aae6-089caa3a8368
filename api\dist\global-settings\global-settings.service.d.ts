import { Model } from 'mongoose';
import { GlobalSettingsDocument } from './interfaces/global-settings.interface';
import { UpdateGlobalSettingsDto } from './dto/update-global-settings.dto';
import { OrganizationDocument } from '../organizations/interfaces/organization.interface';
export declare class GlobalSettingsService {
    private globalSettingsModel;
    private organizationModel;
    constructor(globalSettingsModel: Model<GlobalSettingsDocument>, organizationModel: Model<OrganizationDocument>);
    getGlobalSettings(): Promise<GlobalSettingsDocument>;
    updateGlobalSettings(updateGlobalSettingsDto: UpdateGlobalSettingsDto, userId: string): Promise<GlobalSettingsDocument>;
}
