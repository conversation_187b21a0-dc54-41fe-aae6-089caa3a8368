"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const contacts_service_1 = require("./contacts.service");
const contacts_controller_1 = require("./contacts.controller");
const contact_schema_1 = require("./schemas/contact.schema");
const campaign_module_1 = require("../campaign/campaign.module");
const logger_module_1 = require("../logger/logger.module");
const scheduled_call_module_1 = require("../scheduled-call/scheduled-call.module");
let ContactsModule = class ContactsModule {
};
exports.ContactsModule = ContactsModule;
exports.ContactsModule = ContactsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([{ name: 'Contact', schema: contact_schema_1.ContactSchema }]),
            (0, common_1.forwardRef)(() => campaign_module_1.CampaignModule),
            logger_module_1.LoggerModule,
            (0, common_1.forwardRef)(() => scheduled_call_module_1.ScheduledCallModule)
        ],
        providers: [contacts_service_1.ContactsService],
        controllers: [contacts_controller_1.ContactsController],
        exports: [contacts_service_1.ContactsService, mongoose_1.MongooseModule],
    })
], ContactsModule);
//# sourceMappingURL=contacts.module.js.map