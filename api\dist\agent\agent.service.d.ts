import { Model } from 'mongoose';
export declare class AgentService {
    private readonly agentModel;
    private readonly VAPI_API_TOKEN;
    constructor(agentModel: Model<any>);
    create(createAgentDto: any): Promise<any>;
    findAll(): Promise<any[]>;
    findById(id: string): Promise<any>;
    update(id: string, updateAgentDto: any): Promise<any>;
    remove(id: string): Promise<void>;
    getUploadedFiles(): Promise<{
        files: string[];
        count: number;
    }>;
    getVolumeFiles(): Promise<{
        files: string[];
        count: number;
        error?: string;
        volumePath: string;
        isVolumeAccessible: boolean;
    }>;
}
