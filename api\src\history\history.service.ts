import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { History, HistoryModel } from './interfaces/history.interface';
import { HistoryDto } from './dto/history.dto';

@Injectable()
export class HistoryService {
  constructor(
    @InjectModel('History')
    private readonly historyModel: HistoryModel,
  ) {}

  async create(createHistoryDto: HistoryDto): Promise<History> {
    const createdHistory = new this.historyModel(createHistoryDto);
    return createdHistory.save();
  }

  async findAll(
    page?: number, 
    limit?: number, 
    search?: string, 
    filterType: 'all' | 'name' | 'agent' = 'all'
  ): Promise<History[]> {

    const totalCount = await this.historyModel.countDocuments().exec();

    const filter: any = {};

    if (search && search.trim().length >= 1) {
      // Escape special characters in the search string
      const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const searchRegex = new RegExp(escapedSearch, 'i');
      
      if (filterType === 'all') {
        filter.$or = [
          { fullName: { $regex: searchRegex } },
          { agent: { $regex: searchRegex } }
        ];
      } else if (filterType === 'name') {
        filter.fullName = { $regex: searchRegex };
      } else if (filterType === 'agent') {
        filter.agent = { $regex: searchRegex };
      }
    }

    // Get filtered count if search is active
    let filteredCount = totalCount;
    if (Object.keys(filter).length > 0) {
      filteredCount = await this.historyModel.countDocuments(filter).exec();
    }
    

    // Create a query that we'll build based on provided parameters
    let query = this.historyModel.find(filter).sort({ callStartTime: -1 });
    
    // If both page and limit are provided, apply pagination
    if (page !== undefined && limit !== undefined) {
      const skip = (page - 1) * limit;
      query = query.skip(skip).limit(limit);
    }
    
    // Execute the query
    const calls = await query.exec();
    
    // Add the total count to each call object
    calls.forEach(call => {
      // Set the value that will be used by the virtual getter
      call['_totalCount'] = totalCount;
      call['_filteredCount'] = filteredCount;
    });
    
    return calls;
  }

  async findById(id: string): Promise<History> {
    const history = await this.historyModel.findById(id).exec();
    if (!history) {
      throw new NotFoundException('History record not found');
    }
    return history;
  }

  async delete(id: string): Promise<History> {
    const deletedHistory = await this.historyModel.findByIdAndDelete(id).exec();
    if (!deletedHistory) {
      throw new NotFoundException('History record not found');
    }
    return deletedHistory;
  }

}
