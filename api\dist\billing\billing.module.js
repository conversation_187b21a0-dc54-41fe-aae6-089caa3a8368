"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const config_1 = require("@nestjs/config");
const logger_module_1 = require("../logger/logger.module");
const users_module_1 = require("../users/users.module");
const organization_payment_method_schema_1 = require("./schemas/organization-payment-method.schema");
const organization_transaction_schema_1 = require("./schemas/organization-transaction.schema");
const billing_controller_1 = require("./billing.controller");
const billing_service_1 = require("./billing.service");
const organizations_module_1 = require("../organizations/organizations.module");
let BillingModule = class BillingModule {
};
exports.BillingModule = BillingModule;
exports.BillingModule = BillingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                {
                    name: "OrganizationPaymentMethod",
                    schema: organization_payment_method_schema_1.OrganizationPaymentMethodSchema,
                },
                {
                    name: "OrganizationTransaction",
                    schema: organization_transaction_schema_1.OrganizationTransactionSchema,
                },
            ]),
            config_1.ConfigModule,
            logger_module_1.LoggerModule,
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            (0, common_1.forwardRef)(() => organizations_module_1.OrganizationsModule),
        ],
        controllers: [billing_controller_1.BillingController],
        providers: [billing_service_1.BillingService],
        exports: [billing_service_1.BillingService],
    })
], BillingModule);
//# sourceMappingURL=billing.module.js.map