import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsBoolean } from 'class-validator';

export class CreatePaymentMethodDto {
  @ApiProperty({ description: 'Payment method type (card, paypal, apple_pay)' })
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty({ description: 'Stripe payment method ID' })
  @IsNotEmpty()
  @IsString()
  stripePaymentMethodId: string;

  @ApiProperty({ description: 'Last 4 digits of the card (for card type)' })
  @IsOptional()
  @IsString()
  last4?: string;

  @ApiProperty({ description: 'Expiration month (for card type)' })
  @IsOptional()
  @IsString()
  expMonth?: string;

  @ApiProperty({ description: 'Expiration year (for card type)' })
  @IsOptional()
  @IsString()
  expYear?: string;

  @ApiProperty({ description: 'Cardholder name (for card type)' })
  @IsOptional()
  @IsString()
  cardholderName?: string;

  @ApiProperty({ description: 'Whether this is the default payment method' })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @ApiProperty({ description: 'Card brand (visa, mastercard, etc.)' })
  @IsOptional()
  @IsString()
  brand?: string;
}
