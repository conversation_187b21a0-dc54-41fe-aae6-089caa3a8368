import { Model } from "mongoose";
import { ContactDocument } from "src/contacts/interfaces/contact.interface";
import { HistoryService } from "src/history/history.service";
import { LoggerService } from "src/logger/logger.service";
import { ScheduledCallService } from "src/scheduled-call/scheduled-call.service";
import { CampaignDocument } from "src/campaign/interfaces/campaign.interface";
import { AgentService } from "src/agent/agent.service";
import { GlobalSettingsService } from "src/global-settings/global-settings.service";
import { UsersService } from "src/users/users.service";
import { OrganizationsService } from "src/organizations/organizations.service";
import { History } from "src/history/interfaces/history.interface";
export declare class VapiService {
    private readonly historyService;
    private readonly loggerService;
    private readonly agentService;
    private readonly contactModel;
    private readonly scheduledCallService;
    private readonly campaignModel;
    private readonly historyModel;
    private readonly globalSettingsService;
    private readonly usersService;
    private readonly organizationsService;
    private readonly VAPI_API_TOKEN;
    private readonly PHONE_NUMBER_ID;
    private readonly plateform;
    private cachedAccessToken;
    private tokenFetchTime;
    private recentlyCalledContacts;
    private readonly CALL_TRACKING_EXPIRY;
    constructor(historyService: HistoryService, loggerService: LoggerService, agentService: AgentService, contactModel: Model<ContactDocument>, scheduledCallService: ScheduledCallService, campaignModel: Model<CampaignDocument>, historyModel: Model<History>, globalSettingsService: GlobalSettingsService, usersService: UsersService, organizationsService: OrganizationsService);
    getToken(retryCount?: number, maxRetries?: number, forceRefresh?: boolean): Promise<string>;
    callZohoApi(url: string, method?: "get" | "post" | "put" | "delete", data?: any): Promise<any>;
    private cleanupRecentlyCalledContacts;
    callContacts(contacts: any[], agentId: string, region: string, userId?: string): Promise<any[]>;
    processWebhook(body: any): Promise<any>;
    private findNextAllowedDay;
    private calculateNextCallTime;
    private triggerCallback;
    private normalizePhoneNumber;
}
