
/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { TimezoneSelector } from "@/components/ui/timezone-selector";
import { CalendarIcon, Settings2 } from "lucide-react";
import { format } from "@/lib/dateUtils";
import { DayOfWeek } from "./SettingsSteps";
import FadeIn from "@/animations/FadeIn";

// Define the days of the week for the UI
const daysOfWeek = [
  { label: "Mon", value: "monday" as DayOfWeek },
  { label: "Tue", value: "tuesday" as DayOfWeek },
  { label: "Wed", value: "wednesday" as DayOfWeek },
  { label: "Thu", value: "thursday" as DayOfWeek },
  { label: "Fri", value: "friday" as DayOfWeek },
  { label: "Sat", value: "saturday" as DayOfWeek },
  { label: "Sun", value: "sunday" as DayOfWeek },
];

export interface ScheduleStepProps {
  data: {
    startDate?: string;
    endDate?: string | null;
    callSchedule?: {
      startTime?: string;
      endTime?: string;
      timezone?: string;
      daysOfWeek?: DayOfWeek[];
      callTime?: string;
    };
    callWindow?: {
      startTime?: string;
      endTime?: string;
      timezone?: string;
      daysOfWeek?: DayOfWeek[];
    };
    followUpDays?: DayOfWeek[];
  };
  updateData: (newData: Partial<{
    startDate?: string;
    endDate?: string | null;
    callSchedule?: {
      startTime: string;
      endTime: string;
      timezone: string;
      daysOfWeek: DayOfWeek[];
      callTime?: string;
    };
    callWindow: {
      startTime?: string;
      endTime?: string;
      daysOfWeek?: DayOfWeek[];
    };
    followUpDays?: DayOfWeek[];
  }>) => void;
}

export default function ScheduleStep({ data, updateData }: ScheduleStepProps) {

    const [startDate, setStartDate] = useState<Date>(() => {
      if (data.startDate) {
        const date = new Date(data.startDate);
        return isNaN(date.getTime()) ? new Date() : date;
      }
      return new Date();
    });
    const [timezone, setTimezone] = useState(data.callSchedule?.timezone || "America/New_York");
    const [callTime, setCallTime] = useState(data.callSchedule?.callTime || "09:00");
    const [endDate, setEndDate] = useState<Date>(() => {
      if (data.endDate) {
          const date = new Date(data.endDate);
          return isNaN(date.getTime()) ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) : date;
      }
      return new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  });
    const [startTime, setStartTime] = useState(data.callSchedule?.startTime || "09:00");
    const [endTime, setEndTime] = useState(data.callSchedule?.endTime || "17:00");
    const [followUpOpen, setFollowUpOpen] = useState(false);
    const [selectedPreset, setSelectedPreset] = useState<number | null>(null);
    const [tempFollowUpDays, setTempFollowUpDays] = useState<DayOfWeek[]>(data.followUpDays || ["monday", "tuesday", "wednesday", "thursday", "friday"]);
    const [tempCallTime, setTempCallTime] = useState(data.callSchedule?.callTime || "09:00");
    const [hasEndDate, setHasEndDate] = useState<boolean>(data.endDate ? true : false);
    const [windowDays, setWindowDays] = useState<DayOfWeek[]>(
      data.callWindow?.daysOfWeek || ["monday", "tuesday", "wednesday", "thursday", "friday"]
    );
    
    const [windowStartTime, setWindowStartTime] = useState(data.callWindow?.startTime || "09:00");
    const [windowEndTime, setWindowEndTime] = useState(data.callWindow?.endTime || "17:00");

    // Default values if not provided
    const followUpDays = data.followUpDays || ["monday", "tuesday", "wednesday", "thursday", "friday"];
  
    // Date preset options
    const datePresets = [
      { label: "1 Week", value: 7 },
      { label: "2 Weeks", value: 14 },
      { label: "1 Month", value: 30 },
      { label: "3 Months", value: 90 },
    ];
  
    // Apply date preset
    const applyDatePreset = (days: number) => {
      const newStartDate = new Date();
      const newEndDate = new Date();
      newEndDate.setDate(newEndDate.getDate() + days);
      
      setStartDate(newStartDate);
      setEndDate(newEndDate);
      setSelectedPreset(days);
      
      // Ensure hasEndDate is true when applying a preset
      if (!hasEndDate) {
        setHasEndDate(true);
      }
      
      updateDates(newStartDate, newEndDate, startTime, endTime);
    };
  
    // Update dates in parent component
    const updateDates = (start: Date, end: Date | null, sTime: string, eTime: string) => {
      const formattedStartDate = format(start, "yyyy-MM-dd'T'HH:mm");
      const formattedEndDate = end ? format(end, "yyyy-MM-dd'T'HH:mm") : null;
      
      updateData({
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        callSchedule: {
          startTime: sTime,
          endTime: eTime,
          timezone: timezone,
          daysOfWeek: data.callSchedule?.daysOfWeek || ["monday", "tuesday", "wednesday", "thursday", "friday"],
          callTime: callTime
        }
      });
    };

    const handleEndDateCheckboxChange = (checked: boolean) => {
      setHasEndDate(checked);
      
      if (!checked) {
        // If unchecked, set endDate to null (infinite)
        updateData({
          endDate: null,
          callSchedule: {
            startTime: startTime,
            endTime: endTime,
            timezone: timezone,
            daysOfWeek: data.callSchedule?.daysOfWeek || ["monday", "tuesday", "wednesday", "thursday", "friday"],
            callTime: callTime
          }
        });
      } else {
        // If checked, set endDate to current value
        updateDates(startDate, endDate, startTime, endTime);
      }
    };
    
  
    const handleTimezoneChange = (value: string) => {
      setTimezone(value);
      updateData({
        callSchedule: {
          startTime: data.callSchedule?.startTime || "09:00",
          endTime: data.callSchedule?.endTime || "17:00",
          timezone: value,
          daysOfWeek: data.callSchedule?.daysOfWeek || ["monday", "tuesday", "wednesday", "thursday", "friday"],
          callTime: callTime
        }
      });
    };
  
    // Handle start date change
    const handleStartDateChange = (date: Date | undefined) => {
      if (date) {
        setStartDate(date);
        setSelectedPreset(null);

        // Only update the end date if hasEndDate is true
    if (hasEndDate) {
      updateDates(date, endDate, startTime, endTime);
    } else {
      // If hasEndDate is false, pass null as the end date
      updateData({
        endDate: null,
      });
    }
      }
    };
  
    // Handle end date change
    const handleEndDateChange = (date: Date | undefined) => {
      if (date) {
        setEndDate(date);
        setSelectedPreset(null);
        updateDates(startDate, date, startTime, endTime);
      }
    };
  
    // Handle time changes
    const handleStartTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value;
      
      // If input is empty or invalid, set a default time
      if (!value || !value.includes(':')) {
        value = "00:00";
      }
      
      setStartTime(value);
      updateDates(startDate, endDate, value, endTime);
    };
  
    const handleEndTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value;
      
      // If input is empty or invalid, set a default time
      if (!value || !value.includes(':')) {
        value = "00:00";
      }
      
      setEndTime(value);
      updateDates(startDate, endDate, startTime, value);
    };
  
    const handleTempCallTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value;
      
      // If input is empty or invalid, set a default time
      if (!value || !value.includes(':')) {
        value = "00:00";
      }
      
      setTempCallTime(value);
    };
  
    // Handle follow-up days change
    const handleTempFollowUpDaysChange = (day: DayOfWeek, checked: boolean) => {
      setTempFollowUpDays(prev => 
        checked ? [...prev, day] : prev.filter(d => d !== day)
      );
    };

    // Handle window time changes
    const handleWindowStartTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setWindowStartTime(value);
      updateData({
        callWindow: {
          startTime: value,
          endTime: windowEndTime,
          daysOfWeek: windowDays
        }
      });
    };

    const handleWindowEndTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setWindowEndTime(value);
      updateData({
        callWindow: {
          startTime: windowStartTime,
          endTime: value,
          daysOfWeek: windowDays
        }
      });
    };

    const handleWindowQuickSelect = (option: string) => {
      let newDays: DayOfWeek[] = [];
      
      if (option === "weekdays") {
        newDays = ["monday", "tuesday", "wednesday", "thursday", "friday"];
      } else if (option === "weekend") {
        newDays = ["saturday", "sunday"];
      } else if (option === "everyday") {
        newDays = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
      }
      
      setWindowDays(newDays);
      updateData({
        callWindow: {
          startTime: windowStartTime,
          endTime: windowEndTime,
          daysOfWeek: newDays
        }
      });
    };

  
    // Quick select options for days
    const handleQuickSelect = (option: string) => {
      if (option === "weekdays") {
        setTempFollowUpDays(["monday", "tuesday", "wednesday", "thursday", "friday"]);
      } else if (option === "weekend") {
        setTempFollowUpDays(["saturday", "sunday"]);
      } else if (option === "everyday") {
        setTempFollowUpDays(["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]);
      }
    };
  
    // Save follow-up schedule changes
    const saveFollowUpChanges = () => {
      updateData({ 
        followUpDays: tempFollowUpDays,
        callSchedule: {
          startTime: data.callSchedule?.startTime || "09:00",
          endTime: data.callSchedule?.endTime || "17:00",
          timezone: timezone,
          daysOfWeek: data.callSchedule?.daysOfWeek || ["monday", "tuesday", "wednesday", "thursday", "friday"],
          callTime: tempCallTime
        }
      });
      setCallTime(tempCallTime);
      setFollowUpOpen(false);
    };
  
    // Cancel follow-up schedule changes
    const cancelFollowUpChanges = () => {
      setTempFollowUpDays(followUpDays);
      setTempCallTime(callTime);
      setFollowUpOpen(false);
    };

    const handleWindowDaysChange = (day: DayOfWeek, checked: boolean) => {
      const newDays = checked 
        ? [...windowDays, day].filter((d, i, arr) => arr.indexOf(d) === i) 
        : windowDays.filter(d => d !== day);
      
      setWindowDays(newDays);
      
      // Update parent data
      updateData({
        callWindow: {
          startTime: windowStartTime,
          endTime: windowEndTime,
          daysOfWeek: newDays
        }
      });
    };

    useEffect(() => {
      // Only set initial values if they're not already set
      if (!data.startDate) {
        updateDates(startDate, hasEndDate ? endDate : null as unknown as Date, startTime, endTime);
      }
      
  }, []);
  
    // Initialize temp values when dialog opens
    useEffect(() => {
      if (followUpOpen) {
        setTempFollowUpDays(followUpDays);
        setTempCallTime(callTime);
      }
    }, [followUpOpen, followUpDays, callTime]);
  
    // Generate human-readable recurrence text
    const getRecurrenceText = () => {
      if (followUpDays.length === 7) {
        return "Occurs every day";
      } else if (followUpDays.length === 0) {
        return "No recurrence set";
      } else if (
        followUpDays.length === 5 && 
        followUpDays.includes("monday") && 
        followUpDays.includes("tuesday") && 
        followUpDays.includes("wednesday") && 
        followUpDays.includes("thursday") && 
        followUpDays.includes("friday")
      ) {
        return "Occurs every weekday";
      } else if (
        followUpDays.length === 2 && 
        followUpDays.includes("saturday") && 
        followUpDays.includes("sunday")
      ) {
        return "Occurs every weekend";
      } else {
        const dayNames = followUpDays.map(day => day.charAt(0).toUpperCase() + day.slice(1));
        return `Occurs every ${dayNames.join(", ")}`;
      }
    };
  
    return (
      <div className="space-y-10">
        {/* Campaign Duration */}
        <div className="space-y-8 ">
          <div className="flex items-center justify-between">
            <Label className="text-base font-medium">Campaign Duration</Label>
            <div className="flex ">
            {hasEndDate && (
              <FadeIn duration={0.5} delay={0.1}>             
              <div className="flex gap-2">
                {datePresets.map((preset) => (
                  <Button
                    key={preset.value}
                    variant={selectedPreset === preset.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => applyDatePreset(preset.value)}
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
              </FadeIn>
            )}
            </div>
          </div>
  
      
  
          {/* Date and timezone selection container */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Timezone first */}
          <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <div className="flex space-x-2">
              <TimezoneSelector 
              value={timezone} 
              onChange={handleTimezoneChange} 
            />
              </div>
            </div>

            {/* Start date first */}
            <div className="space-y-2">
              <Label>Start Date & Time</Label>
              <div className="flex space-x-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex-1 justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={handleStartDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <div className="relative">
                  <Input
                    type="time"
                    value={startTime}
                    onChange={handleStartTimeChange}
                    className="w-24"
                  />
                </div>
              </div>
            </div>
            {/* End date with checkbox */}
    <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="hasEndDate" 
              checked={hasEndDate} 
              onCheckedChange={handleEndDateCheckboxChange}
              className="cursor-pointer"
            />
            <Label htmlFor="hasEndDate" className="cursor-pointer">Set End Date & Time</Label>
          </div>
          
          {hasEndDate ? (
            <div className="flex space-x-2 mt-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="flex-1 justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={handleEndDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <div className="relative">
                <Input
                  type="time"
                  value={endTime}
                  onChange={handleEndTimeChange}
                  className="w-24"
                />
              </div>
            </div>
          ) : (
            <div className="text-sm text-muted-foreground mt-2">
              Campaign will run indefinitely
            </div>
          )}
        </div>
          </div>
        </div>
        
        {/* Call Window Section - NEW */}
    <div className="space-y-6 mt-5">
      <div className="flex justify-between items-center ">
        <Label className="text-base font-medium">Call Window</Label>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handleWindowQuickSelect("weekdays")}
          >
            Weekdays
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handleWindowQuickSelect("weekend")}
          >
            Weekend
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handleWindowQuickSelect("everyday")}
          >
            Every Day
          </Button>
        </div>
      </div>
      
      <div className="space-y-4 border rounded-md p-4 bg-muted/30">
        {/* Days of week selection */}
        <div className="space-y-6">
          <Label>Days of the Week</Label>
          <div className="grid grid-cols-7 gap-2">
            {daysOfWeek.map((day) => (
              <div key={`window-${day.value}`} className="flex flex-col items-center space-y-1.5">
                <Checkbox
                  id={`window-${day.value}`}
                  checked={windowDays.includes(day.value)}
                  onCheckedChange={(checked) => 
                    handleWindowDaysChange(day.value, checked as boolean)
                  }
                  className="h-6 w-6 cursor-pointer"
                />
                <Label htmlFor={`window-${day.value}`} className="text-xs cursor-pointer">
                  {day.label}
                </Label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Time range selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Start Time</Label>
            <Input
              type="time"
              value={windowStartTime}
              onChange={handleWindowStartTimeChange}
            />
          </div>
          <div className="space-y-2">
            <Label>End Time</Label>
            <Input
              type="time"
              value={windowEndTime}
              onChange={handleWindowEndTimeChange}
            />
          </div>
        </div>
        
        <p className="text-xs text-gray-500">
          Calls will only be made during these hours on the selected days in the {timezone} timezone.
        </p>
      </div>
    </div>
        

        {/* Follow-up Schedule */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Label className="text-base font-medium">Follow-up Schedule</Label>
            <Dialog open={followUpOpen} onOpenChange={setFollowUpOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings2 className="mr-2 h-4 w-4" />
                  Configure Recurring
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Configure Recurring Schedule</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-7">
                    <div className="flex justify-between items-center">
                      <Label>Days of the Week</Label>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickSelect("weekdays")}
                        >
                          Weekdays
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickSelect("weekend")}
                        >
                          Weekend
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickSelect("everyday")}
                        >
                          Every Day
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-7 gap-2">
                      {daysOfWeek.map((day) => (
                        <div key={day.value} className="flex flex-col items-center space-y-1.5">
                          <Checkbox
                            id={day.value}
                            checked={tempFollowUpDays.includes(day.value)}
                            onCheckedChange={(checked) => 
                              handleTempFollowUpDaysChange(day.value, checked as boolean)
                            }
                            className="h-6 w-6"
                          />
                          <Label htmlFor={day.value} className="text-xs cursor-pointer">
                            {day.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
  
                  {/* Call time input */}
                  <div className="space-y-4">
                    <Label>Call Time</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="time"
                        value={tempCallTime}
                        onChange={handleTempCallTimeChange}
                        className="w-22"
                      />
                      <span className="text-sm text-gray-500">
                        in {timezone}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500">
                      Calls will be initiated at this time on the selected days.
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={cancelFollowUpChanges}>
                    Cancel
                  </Button>
                  <Button onClick={saveFollowUpChanges}>
                    Save
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          <div className="p-4 border rounded-md bg-muted/50">
            <div className="text-sm font-medium mb-2">
              {getRecurrenceText()}
            </div>
          
            <div className="mt-2 text-sm">
              Calls scheduled for ({timezone})
            </div>
          </div>
        </div>
      </div>
    );
  }