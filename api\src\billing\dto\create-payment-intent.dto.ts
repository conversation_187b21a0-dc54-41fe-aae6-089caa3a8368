import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsOptional } from 'class-validator';

export class CreatePaymentIntentDto {
  @ApiProperty({ description: 'Amount to charge (in cents)' })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Currency code (e.g., usd)' })
  @IsNotEmpty()
  @IsString()
  currency: string;

  @ApiProperty({ description: 'Payment method ID to use' })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;

  @ApiProperty({ description: 'Payment method type (card, paypal, apple_pay)' })
  @IsNotEmpty()
  @IsString()
  paymentMethodType: string;

  @ApiProperty({ description: 'Description of the payment' })
  @IsOptional()
  @IsString()
  description?: string;
}
