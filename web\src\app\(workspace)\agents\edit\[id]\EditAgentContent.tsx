
"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON>ir<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";

import AgentProfileTab from "@/components/agentscomponents/AgentProfileTab";
import AgentRoleTab from "@/components/agentscomponents/AgentRoleTab";
import AgentActionsTab from "@/components/agentscomponents/AgentActionsTab";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { useAuth } from "@/hooks/useAuth";
import { useAgent } from "@/hooks/useAgent";
import AgentVoiceTab from "@/components/agentscomponents/AgentVoiceTab";
import AgentBrainTab from "@/components/agentscomponents/AgentBrainTab";
import AgentAdvancedTab from "@/components/agentscomponents/AgentAdvancedTab";
import AgentSidebar from "@/components/agentscomponents/AgentSidebar";
import { useAgentsList } from "@/hooks/useAgentList";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Agent } from "@/types/agent.types";


export const agentTabs = [
  { 
    value: 'profile',
    label: 'Profile',
    component: AgentProfileTab
  },
  {
    value: 'prompt',
    label: 'Prompt',
    component: AgentRoleTab
  },
  {
    value: 'voice',
    label: 'Voice',
    component: AgentVoiceTab
  },
  {
    value: 'brain',
    label: 'Brain',
    component: AgentBrainTab
  },
  {
    value: 'actions',
    label: 'Actions',
    component: AgentActionsTab
  },
  {
    value: 'advanced',
    label: 'Advanced',
    component: AgentAdvancedTab
  }
] as const;

interface EditAgentContentProps {
  agentId?: string;
}

export default function EditAgentContent({ agentId: propAgentId }: EditAgentContentProps) {
  const router = useRouter();
  const params = useParams();

  const [activeTab, setActiveTab] = useState("profile");
  const agentId = propAgentId || (params.id as string);
  const [showAgentsList, setShowAgentsList] = useState(false);
   // New state for saving and dialog
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [saveError, setShowError] = useState<string | null>(null);

  const { userRole } = useAuth();
  const { agent, setAgent, phoneNumbers, agentIsLoading, updateAgentMutation } = useAgent(agentId);
  const { agents, agentsisLoading } = useAgentsList();

   
  useEffect(() => {
    if (!agentId) {
      router.push("/agents");
    }
  }, [agentId, router]);


 const handleSaveChanges = async () => {
    if (!agent) return;
    setIsSaving(true);
    setShowError(null);

    try {
      await updateAgentMutation.mutateAsync(agent);
      setShowSuccessDialog(true);
    } catch (error) {
      console.error("Error updating agent:", error);
      setShowError(error instanceof Error ? error.message : "Failed to update agent");
    } finally {
      setIsSaving(false);
    }
  };


  // Update the status toggle function
  const handleToggleStatus = async () => {
    if (!agent) return;

    const newStatus = agent.status === 'active' ? 'inactive' : 'active';

    try {
      await updateAgentMutation.mutateAsync({
        ...agent,
        status: newStatus
      });
    } catch (error) {
      console.error("Error updating agent status:", error);
      setShowError(error instanceof Error ? error.message : "Failed to update agent status");
    }
  };

  const handleAgentSelect = (selectedAgentId: string) => {
    if (selectedAgentId !== agentId) {
      router.push(`/agents/edit/${selectedAgentId}`);
    }
    setShowAgentsList(false);
  };


  return (
    <>
    <div className="min-h-screen bg-gray-50/50 dark:bg-gray-900/50 ">

     {/* Header - Now outside and above everything */}
    <div className="w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 sm:h-16 items-center px-2 sm:px-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/agents")}
          className="mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
          <span className="sr-only">Back</span>
        </Button>

        <h1 className="text-lg sm:text-xl md:text-2xl font-bold 
        tracking-tight truncate">{agent?.name}</h1>
      </div>
    </div>


      {/* Main Content  Area*/}
    <div className="flex flex-col lg:flex-row">
      {/* Sidebar */}
        <div className="hidden xl:block">
          <AgentSidebar 
            currentAgentId={agentId}
            userRole={userRole} 
            agents={agents}
            agentsisLoading={agentsisLoading}
          />
        </div>

      {/* Main Content */}
      <div className="flex-1 min-h-[calc(100vh-4rem)]">
      {agentIsLoading ? (
          <div className="container py-4 px-4">
            <div className="animate-pulse">
              <div className="h-8 w-64 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
              <div className="h-4 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="mt-8 space-y-4">
                <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>
        ) : agent && (
        <div className="p-3 sm:p-4 md:p-6 max-w-full md:max-w-5xl mx-auto">
          {/* Tabs */}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-4"
          >
            <div className="border-b overflow-x-auto scrollbar-hide">
              <TabsList className="w-full justify-start h-auto bg-transparent p-0 flex">
                    {agentTabs.map(tab => (
                      <TabsTrigger
                        key={tab.value}
                        value={tab.value}
                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-2 sm:px-4 py-2 sm:py-3 bg-transparent text-xs sm:text-sm whitespace-nowrap"
                      >
                        {tab.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
            </div>

            <div className="bg-card rounded-lg border shadow-sm">
                  {agentTabs.map(tab => (
                    <TabsContent 
                      key={tab.value}
                      value={tab.value} 
                      className="m-0 focus-visible:outline-none focus-visible:ring-0"
                    >
                      <tab.component 
                        agent={agent} 
                        setAgent={setAgent} 
                        phoneNumbers={phoneNumbers}
                        isCreateMode={false}
                      />
                    </TabsContent>
                  ))}
                </div>
          </Tabs>
          {/* Save Changes Button */}
          <div className="mt-4 sm:mt-6 flex justify-end">
            <Button
              onClick={handleSaveChanges}
              disabled={isSaving}
              className="bg-black text-white dark:text-black dark:bg-white hover:from-purple-700 hover:to-blue-600 w-full sm:w-auto"
            >
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          </div>


        {/* Status indicator */}
          {userRole === "superadmin" && (
              <div className="mt-6 sm:mt-8 flex flex-col sm:flex-row sm:items-center justify-between py-3 sm:py-4 px-4 sm:px-6 bg-muted rounded-lg">
              <div className="flex items-center gap-2 mb-3 sm:mb-0">
                <div className={`h-3 w-3 rounded-full ${agent.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className="text-sm">
                  {agent.status === 'active' ? 'Active' : 'Inactive'}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleStatus}
              >
                {agent.status === 'active' ? 'Deactivate' : 'Activate'}
              </Button>
              </div>
            )}

         {/* Floating Agents List Button - Only visible on smaller screens */}
          <div className="fixed bottom-6 right-6 xl:hidden z-50">
            <Button
              onClick={() => setShowAgentsList(true)}
              size="icon"
              className="h-12 w-12 rounded-full shadow-lg bg-primary hover:bg-primary/90"
            >
              <Bot className="h-6 w-6" />
            </Button>
          </div>

    {/* Agents List Dialog */}
      <Dialog open={showAgentsList} onOpenChange={setShowAgentsList}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Select Agent
            </DialogTitle>
          </DialogHeader>
          
          {agentsisLoading ? (
            <div className="py-4 flex justify-center">
              <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
            </div>
          ) : (
            <ScrollArea className="h-[50vh] sm:h-[40vh] pr-4">
              <div className="space-y-1">
                {agents.map((agentItem: Agent) => (
                  <div 
                    key={agentItem.id}
                    onClick={() => handleAgentSelect(agentItem.id)}
                    className={`flex items-center gap-3 p-2 rounded-md cursor-pointer transition-colors
                      ${agentItem.id === agentId 
                        ? 'bg-primary/10 text-primary' 
                        : 'hover:bg-muted'
                      }`}
                  >
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={agentItem.avatar || ''} alt={agentItem.name} />
                      <AvatarFallback className="bg-primary/10 text-primary">
                        {agentItem.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{agentItem.name}</p>
                      <p className="text-xs text-muted-foreground truncate">{agentItem.role}</p>
                    </div>
                    {agentItem.id === agentId && (
                      <div className="h-2 w-2 rounded-full bg-primary" />
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

          {/* Success Dialog */}
        <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Agent Updated Successfully
              </DialogTitle>
            </DialogHeader>
            <p className="text-sm text-muted-foreground">
              Your changes to {agent?.name} have been saved.
            </p>
            <DialogFooter className="flex gap-2 sm:justify-start">
              <Button
                variant="outline"
                onClick={() => setShowSuccessDialog(false)}
              >
                Continue Editing
              </Button>
              <Button
                onClick={() => router.push("/agents")}
                className="bg-black text-white dark:text-black dark:bg-white"
              >
                Back to Agents
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Error Dialog */}
        {saveError && (
          <Dialog open={!!saveError} onOpenChange={() => setShowError(null)}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2 text-red-500">
                  <AlertCircle className="h-5 w-5" />
                  Error Updating Agent
                </DialogTitle>
              </DialogHeader>
              <p className="text-sm text-muted-foreground">
                {saveError}
              </p>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowError(null)}
                >
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
        </div>
 )}
  </div>
    </div>

    </div>

    </>
  );
}