import { Document } from 'mongoose';

export interface Contact {
  contactName: string;
  phoneNumber: string;
  lastCall?: Date;
  campaigns?: string[];
  region?: string;
  updatedAt: Date;
  customerId?: string;

  projectName?: string;
  unitNumber?: string;
  totalPayableAmount?: number;
  pendingPayableAmount?: number;
  dueDate?: Date;
  totalInstallments?: number;
  paymentType?: string;
  pendingInstallments?: number;
  lastPaymentDate?: Date;
  lastPaymentAmount?: number;
  lastPaymentType?: string;
  collectionBucket?: string;
  unitPrice?: number;
  paidAmtIncluding?: number;
  unansweredCalls: number;

  // Event related fields
  eventDate?: Date;
  eventLocation?: string;
  eventTime?: string;
  nameOfRegistrant?: string;
  createdAt?: Date;
  addedBy?: string;
  source?: string;
}

export type ContactDocument = Contact & Document;
