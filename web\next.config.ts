import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  // Configure image optimization
  images: {
    // Disable the static image import optimization that requires Sharp
    unoptimized: true,
    // Alternatively, you can use remote patterns if you need optimization
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    formats: ['image/webp'],
  },
  // Increase build memory limit if needed
  experimental: {
    // Increase memory limit for builds
    memoryBasedWorkersCount: true,
    // Configure Turbopack
    turbo: {
      // Add any Turbopack-specific configurations here if needed
      resolveAlias: {
        // Equivalent to webpack aliases
        'sharp': '',
        'node-gyp-build': '',
      },
    },
  },
};

export default nextConfig;
