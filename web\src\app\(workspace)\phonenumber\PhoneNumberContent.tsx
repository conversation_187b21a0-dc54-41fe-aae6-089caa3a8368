/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PhoneCall } from "lucide-react";
import { useRouter } from "next/navigation";

type PhoneNumber = {
  id: string;
  number: string;
  outboundThoughtly: string | null;
  inboundThoughtly: string | null;
  inboundAutomation: string | null;
  smsEnabled: boolean;
  datePurchased: Date;
};

export default function PhoneNumberContent() {
  const router = useRouter();
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);
  
  const handleBuyNumber = () => {
    router.push('/phonenumber/buy');
  };

  return (
    <>
      {/* Header with action buttons */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-semibold">Phone Numbers</h1>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm">
            Manage
          </Button>
          <Button
            size="sm"
            className=" transition-all duration-150 hover:scale-105"
            onClick={handleBuyNumber}
          >
            Buy a Number
          </Button>
          
          <Button variant="outline" size="sm">
            Regulatory Bundles
          </Button>
        </div>
      </div>

      {/* Phone Numbers Table */}
      <div className="bg-card rounded-lg border shadow-sm overflow-hidden mb-8">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Phone Number</TableHead>
              <TableHead>Outbound </TableHead>
              <TableHead>Inbound </TableHead>
              <TableHead>Inbound Automation</TableHead>
              <TableHead>SMS Enabled</TableHead>
              <TableHead>Date Purchased</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {phoneNumbers.length > 0 ? (
              phoneNumbers.map((number) => (
                <TableRow key={number.id}>
                  <TableCell className="font-medium">{number.number}</TableCell>
                  <TableCell>{number.outboundThoughtly || "-"}</TableCell>
                  <TableCell>{number.inboundThoughtly || "-"}</TableCell>
                  <TableCell>{number.inboundAutomation || "-"}</TableCell>
                  <TableCell>{number.smsEnabled ? "Yes" : "No"}</TableCell>
                  <TableCell>
                    {number.datePurchased.toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-80 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <div className="h-16 w-16 bg-muted rounded-full flex items-center justify-center mb-4">
                      <PhoneCall className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <p className="text-lg font-medium mb-4">
                      You don&apos;t have any phone numbers yet
                    </p>
                    <p className="text-sm text-muted-foreground max-w-md text-center mb-8">
                      Buy a phone number to get started connecting your
                      Thoughtlys for inbound and outbound calling.
                    </p>
                    <Button
                      className=" transition-all duration-150 hover:scale-105"
                      onClick={handleBuyNumber}
                    >
                      Buy a Number
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </>
  );
}