import Link from 'next/link'
import { But<PERSON> } from './ui/button'

export default function HeroSection() {
  return (
    <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
    <div className="max-w-7xl mx-auto text-center">
      <h1 className="text-5xl md:text-6xl font-bold bg-clip-text animate-gradient-x mb-6">
        Build <span className="bg-gradient-to-r from-[#4157ea] to-[#a855f7] text-transparent bg-clip-text ">AI Agents </span>  with
        <br />
        Natural Language
      </h1>
      <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
        Create, customize, and deploy AI agents effortlessly. No coding
        required. Just describe what you want, and watch your AI agent come to
        life.
      </p>
      <div className="flex justify-center space-x-4">
        <Link href="/Dashboard">
          <Button
            size="lg"
            className="bg-gradient-to-r from-[#4157ea] to-[#a855f7] text-white cursor-pointer transition-all duration-200 hover:scale-110"
          >
            Start Building
          </Button>
        </Link>
        <Button size="lg" className='cursor-pointer transition-all duration-200 hover:scale-110'>
          Watch Demo
        </Button>
      </div>
    </div>
  </section>
  )
}
