import { Document } from 'mongoose';
interface CallMetrics {
    totalCalls: number;
    totalMinutes: number;
    averageLength: number;
    connectionRate: number;
    answerRate: number;
}
interface TotalCounts {
    totalCampaigns: number;
    totalScheduledCalls: number;
    totalContacts: number;
}
interface Sentiments {
    positive: number;
    neutral: number;
    negative: number;
}
interface CallEndReason {
    reason: string;
    count: number;
    percentage: number;
}
interface TopAgent {
    id: string;
    name: string;
    avatar: string | null;
    role: string;
    status: string;
    callCount: number;
}
export declare class DashboardStats extends Document {
    dateRange: string;
    agentType: string;
    callMetrics: CallMetrics;
    totalCounts: TotalCounts;
    sentiments: Sentiments;
    callEndReasons: CallEndReason[];
    agentRoles: string[];
    topAgents: TopAgent[];
    recentCalls: Array<{
        _id: string;
        fullName: string;
        mobileNumber: string;
        callStartTime: string;
        callEndTime: string;
        callDuration: string;
        callRoute: string;
        callPurpose: string;
        callEndReason: string;
        agent: string;
    }>;
    recentSchedules: Array<{
        _id: string;
        agentId: string;
        contacts: {
            Name: string;
            MobileNumber: string;
        }[];
        scheduledTime: string;
        region: string;
        status: string;
        scheduledByName: string;
        scheduledByTimestamp: string;
    }>;
    recentCampaigns: Array<{
        _id: string;
        name: string;
        startDate: string;
        endDate: string;
        status: string;
    }>;
    lastUpdated: Date;
}
export declare const DashboardStatsSchema: import("mongoose").Schema<DashboardStats, import("mongoose").Model<DashboardStats, any, any, any, Document<unknown, any, DashboardStats> & DashboardStats & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, DashboardStats, Document<unknown, {}, import("mongoose").FlatRecord<DashboardStats>> & import("mongoose").FlatRecord<DashboardStats> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export {};
