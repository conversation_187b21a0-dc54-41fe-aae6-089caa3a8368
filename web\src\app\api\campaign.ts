import { authFetch } from "@/lib/authFetch";
import { DayOfWeek } from "../(workspace)/campaign/create/steps/SettingsSteps";


const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || '';

export interface Campaign {
  maxRecalls: number;
  followUpDays: DayOfWeek[];
  recallHours: number;
  callSchedule: { startTime: string; endTime: string; timezone: string; daysOfWeek: string[]; };
  callWindow: {
    startTime: string;
    endTime: string;
    timezone?: string;
    daysOfWeek: DayOfWeek[];
  };
  contacts: never[];
  agentId: string | undefined;
  _id: string;
  name: string;
  concurrentCalls: number;
  dailyCost: number;
  instantCall: boolean;
  batchIntervalMinutes: number;
  startDate: string;
  endDate: string | null;
  successRate: number;
  sentiment: string;
  status: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CampaignFormData {
  name: string;
  concurrentCalls: number;
  dailyCost: number;
  startDate: string;
  endDate: string | null;
  successRate?: number;
  sentiment?: string;
  status?: string;
  batchIntervalMinutes?: number;
}

/**
 * Fetch all campaigns or filter by status
 */
export async function fetchCampaigns(status?: string): Promise<Campaign[]> {
  const url = status && status !== 'all'
    ? `${API_URL}/api/campaigns?status=${status}`
    : `${API_URL}/api/campaigns`;

  const response = await authFetch(url);

  if (!response.ok) {
    throw new Error('Failed to fetch campaigns');
  }

  return await response.json();
}

/**
 * Create a new campaign
 */
export async function createCampaign(campaignData: CampaignFormData): Promise<Campaign> {
  const response = await authFetch(`${API_URL}/api/campaigns`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(campaignData),
  });

  if (!response.ok) {
    throw new Error('Failed to create campaign');
  }

  return await response.json();
}

/**
 * Update an existing campaign
 */
export async function updateCampaign(id: string, campaignData: CampaignFormData): Promise<Campaign> {
  const response = await authFetch(`${API_URL}/api/campaigns/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(campaignData),
  });

  if (!response.ok) {
    throw new Error('Failed to update campaign');
  }

  return await response.json();
}

/**
 * Update campaign status (active/paused/completed)
 */
export async function updateCampaignStatus(id: string, status: string): Promise<Campaign> {
  const response = await authFetch(`${API_URL}/api/campaigns/${id}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status }),
  });

  if (!response.ok) {
    throw new Error('Failed to update campaign status');
  }

  return await response.json();
}

/**
 * Delete a campaign
 */
export async function deleteCampaign(id: string): Promise<void> {
  const response = await authFetch(`${API_URL}/api/campaigns/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    throw new Error('Failed to delete campaign');
  }
}

/**
 * Get campaign by ID
 */
export async function getCampaign(id: string): Promise<Campaign> {
  const response = await authFetch(`${API_URL}/api/campaigns/${id}`);

  if (!response.ok) {
    throw new Error('Failed to fetch campaign');
  }

  return await response.json();
}

/**
 * Remove duplicate pending calls for a campaign
 */
export async function removeDuplicateCalls(campaignData: {
  agentId: string;
}): Promise<{ duplicatesRemoved: number }> {
  const response = await authFetch(`${API_URL}/api/scheduled-call/remove-duplicates`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(campaignData),
  });

  if (!response.ok) {
    throw new Error('Failed to remove duplicate calls');
  }

  return await response.json();
}

/**
 * Reschedule all pending calls for a campaign
 */
export async function rescheduleCampaignCalls(campaignData: {
  agentId: string;
  concurrentCalls: number;
  batchIntervalMinutes: number;
  callWindow: {
    startTime: string;
    endTime: string;
    daysOfWeek: string[];
  };
}): Promise<{ rescheduledCount: number; duplicatesRemoved: number; totalCount: number }> {
  const response = await authFetch(`${API_URL}/api/scheduled-call/reschedule-campaign`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(campaignData),
  });

  if (!response.ok) {
    throw new Error('Failed to reschedule campaign calls');
  }

  return await response.json();
}
