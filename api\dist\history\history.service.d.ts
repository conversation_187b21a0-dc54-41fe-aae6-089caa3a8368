import { History, HistoryModel } from './interfaces/history.interface';
import { HistoryDto } from './dto/history.dto';
export declare class HistoryService {
    private readonly historyModel;
    constructor(historyModel: HistoryModel);
    create(createHistoryDto: HistoryDto): Promise<History>;
    findAll(page?: number, limit?: number, search?: string, filterType?: 'all' | 'name' | 'agent'): Promise<History[]>;
    findById(id: string): Promise<History>;
    delete(id: string): Promise<History>;
}
