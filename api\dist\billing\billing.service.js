"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const config_1 = require("@nestjs/config");
const stripe_1 = __importDefault(require("stripe"));
const organization_payment_method_schema_1 = require("./schemas/organization-payment-method.schema");
const organization_transaction_schema_1 = require("./schemas/organization-transaction.schema");
const logger_service_1 = require("../logger/logger.service");
const users_service_1 = require("../users/users.service");
const organizations_service_1 = require("../organizations/organizations.service");
let BillingService = class BillingService {
    constructor(orgPaymentMethodModel, orgTransactionModel, configService, loggerService, usersService, organizationsService) {
        this.orgPaymentMethodModel = orgPaymentMethodModel;
        this.orgTransactionModel = orgTransactionModel;
        this.configService = configService;
        this.loggerService = loggerService;
        this.usersService = usersService;
        this.organizationsService = organizationsService;
        this.stripe = new stripe_1.default(this.configService.get('STRIPE_SECRET_KEY'), {
            apiVersion: '2025-04-30.basil',
        });
    }
    async createStripeCustomer(userId, email, name) {
        try {
            const customer = await this.stripe.customers.create({
                email,
                name,
                metadata: {
                    userId,
                },
            });
            return customer;
        }
        catch (error) {
            this.loggerService.error('Error creating Stripe customer', error);
            throw new common_1.HttpException('Failed to create customer account', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrCreateStripeCustomer(userId, email, name) {
        try {
            const customers = await this.stripe.customers.list({
                limit: 1,
                email,
            });
            let customer = customers.data.find(c => c.metadata.userId === userId);
            if (!customer) {
                customer = await this.createStripeCustomer(userId, email, name);
            }
            return customer;
        }
        catch (error) {
            this.loggerService.error('Error getting or creating Stripe customer', error);
            throw new common_1.HttpException('Failed to retrieve or create customer account', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createStripeOrganizationCustomer(organizationId, email, name) {
        try {
            const customer = await this.stripe.customers.create({
                email,
                name,
                metadata: {
                    organizationId,
                },
            });
            await this.organizationsService.setStripeCustomerId(organizationId, customer.id);
            return customer;
        }
        catch (error) {
            this.loggerService.error('Error creating Stripe organization customer', error);
            throw new common_1.HttpException('Failed to create organization customer account', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrCreateStripeOrganizationCustomer(organizationId, email, name) {
        try {
            const organization = await this.organizationsService.findOne(organizationId);
            if (organization.stripeCustomerId) {
                try {
                    const customer = await this.stripe.customers.retrieve(organization.stripeCustomerId);
                    if (customer && !('deleted' in customer)) {
                        return customer;
                    }
                }
                catch (error) {
                    this.loggerService.error(`Error retrieving Stripe customer for organization ${organizationId}`, error);
                }
            }
            return await this.createStripeOrganizationCustomer(organizationId, email, name);
        }
        catch (error) {
            this.loggerService.error('Error getting or creating Stripe organization customer', error);
            throw new common_1.HttpException('Failed to retrieve or create organization customer account', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserWithOrganization(userId) {
        try {
            const user = await this.usersService.findById(userId);
            if (!user) {
                throw new common_1.HttpException(`User with ID ${userId} not found`, common_1.HttpStatus.NOT_FOUND);
            }
            return user;
        }
        catch (error) {
            this.loggerService.error(`Error getting user with organization: ${error.message}`, error);
            throw new common_1.HttpException(error.message || 'Failed to get user with organization', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createOrganizationPaymentIntent(organizationId, userId, email, createPaymentIntentDto, name) {
        try {
            const { amount, currency, paymentMethodId, paymentMethodType, description } = createPaymentIntentDto;
            if (amount < 1000) {
                throw new common_1.HttpException('Minimum payment amount is $10', common_1.HttpStatus.BAD_REQUEST);
            }
            const customer = await this.getOrCreateStripeOrganizationCustomer(organizationId, email, name);
            const paymentIntentParams = {
                amount,
                currency,
                customer: customer.id,
                description,
                payment_method_types: [paymentMethodType],
                metadata: {
                    organizationId,
                    userId,
                },
            };
            if (paymentMethodId) {
                const paymentMethod = await this.orgPaymentMethodModel.findOne({
                    _id: paymentMethodId,
                    organizationId,
                });
                if (!paymentMethod) {
                    throw new common_1.HttpException('Payment method not found', common_1.HttpStatus.NOT_FOUND);
                }
                paymentIntentParams.payment_method = paymentMethod.stripePaymentMethodId;
                paymentIntentParams.confirm = true;
            }
            const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);
            const transaction = new this.orgTransactionModel({
                organizationId,
                userId,
                amount: amount / 100,
                currency,
                status: paymentIntent.status,
                paymentMethodId: paymentMethodId || null,
                stripePaymentIntentId: paymentIntent.id,
                stripeCustomerId: customer.id,
                description,
                email,
                metadata: {
                    paymentMethodType,
                },
            });
            await transaction.save();
            return {
                clientSecret: paymentIntent.client_secret,
                paymentIntentId: paymentIntent.id,
                status: paymentIntent.status,
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException)
                throw error;
            this.loggerService.error('Error creating organization payment intent', error);
            throw new common_1.HttpException('Failed to process payment', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createOrganizationPaymentMethod(organizationId, createPaymentMethodDto) {
        try {
            const newPaymentMethod = new this.orgPaymentMethodModel({
                organizationId,
                ...createPaymentMethodDto,
            });
            const existingMethods = await this.orgPaymentMethodModel.find({ organizationId }).exec();
            if (existingMethods.length === 0 || createPaymentMethodDto.isDefault) {
                newPaymentMethod.isDefault = true;
                if (existingMethods.length > 0) {
                    await this.orgPaymentMethodModel.updateMany({ organizationId, isDefault: true }, { $set: { isDefault: false } });
                }
            }
            return await newPaymentMethod.save();
        }
        catch (error) {
            this.loggerService.error('Error creating organization payment method', error);
            throw new common_1.HttpException('Failed to create organization payment method', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrganizationPaymentMethods(organizationId) {
        try {
            return await this.orgPaymentMethodModel.find({ organizationId }).sort({ isDefault: -1 }).exec();
        }
        catch (error) {
            this.loggerService.error('Error retrieving organization payment methods', error);
            throw new common_1.HttpException('Failed to retrieve organization payment methods', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async setDefaultOrganizationPaymentMethod(organizationId, paymentMethodId) {
        try {
            await this.orgPaymentMethodModel.updateMany({ organizationId, isDefault: true }, { $set: { isDefault: false } });
            const updatedMethod = await this.orgPaymentMethodModel.findOneAndUpdate({ _id: paymentMethodId, organizationId }, { $set: { isDefault: true } }, { new: true });
            if (!updatedMethod) {
                throw new common_1.HttpException('Organization payment method not found', common_1.HttpStatus.NOT_FOUND);
            }
            return updatedMethod;
        }
        catch (error) {
            if (error instanceof common_1.HttpException)
                throw error;
            this.loggerService.error('Error setting default organization payment method', error);
            throw new common_1.HttpException('Failed to set default organization payment method', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removeOrganizationPaymentMethod(organizationId, paymentMethodId) {
        try {
            const paymentMethod = await this.orgPaymentMethodModel.findOne({
                _id: paymentMethodId,
                organizationId,
            });
            if (!paymentMethod) {
                throw new common_1.HttpException('Organization payment method not found', common_1.HttpStatus.NOT_FOUND);
            }
            if (paymentMethod.stripePaymentMethodId) {
                await this.stripe.paymentMethods.detach(paymentMethod.stripePaymentMethodId);
            }
            await this.orgPaymentMethodModel.deleteOne({ _id: paymentMethodId });
            if (paymentMethod.isDefault) {
                const otherMethod = await this.orgPaymentMethodModel.findOne({ organizationId }).exec();
                if (otherMethod) {
                    await this.setDefaultOrganizationPaymentMethod(organizationId, otherMethod._id.toString());
                }
            }
            return { success: true, message: 'Organization payment method removed successfully' };
        }
        catch (error) {
            if (error instanceof common_1.HttpException)
                throw error;
            this.loggerService.error('Error removing organization payment method', error);
            throw new common_1.HttpException('Failed to remove organization payment method', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async processOrganizationPayment(organizationId, userId, email, paymentMethodId, paymentDetails, name, savePaymentMethod = false, setAsDefault = false) {
        try {
            if (paymentDetails.amount < 1000) {
                throw new common_1.HttpException('Minimum payment amount is $10', common_1.HttpStatus.BAD_REQUEST);
            }
            const customer = await this.getOrCreateStripeOrganizationCustomer(organizationId, email, name);
            const paymentMethod = await this.stripe.paymentMethods.retrieve(paymentMethodId);
            if (paymentMethod.customer !== customer.id) {
                await this.stripe.paymentMethods.attach(paymentMethodId, {
                    customer: customer.id,
                });
            }
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount: paymentDetails.amount,
                currency: paymentDetails.currency,
                customer: customer.id,
                payment_method: paymentMethodId,
                description: paymentDetails.description,
                confirm: true,
                metadata: {
                    organizationId,
                    userId,
                },
                automatic_payment_methods: {
                    enabled: true,
                    allow_redirects: 'never',
                },
                return_url: `${this.configService.get('FRONTEND_URL')}/billing`,
            });
            let dbPaymentMethod = null;
            if (savePaymentMethod) {
                dbPaymentMethod = await this.orgPaymentMethodModel.findOne({
                    stripePaymentMethodId: paymentMethodId,
                    organizationId,
                });
                if (!dbPaymentMethod) {
                    const card = paymentMethod.card;
                    dbPaymentMethod = new this.orgPaymentMethodModel({
                        organizationId,
                        type: 'card',
                        stripePaymentMethodId: paymentMethodId,
                        last4: card.last4,
                        expMonth: card.exp_month.toString().padStart(2, '0'),
                        expYear: card.exp_year.toString().slice(-2),
                        cardholderName: paymentMethod.billing_details.name || name,
                        brand: card.brand,
                        isDefault: false,
                    });
                    if (setAsDefault) {
                        await this.orgPaymentMethodModel.updateMany({ organizationId, isDefault: true }, { $set: { isDefault: false } });
                        dbPaymentMethod.isDefault = true;
                    }
                    else {
                        const existingMethods = await this.orgPaymentMethodModel.find({ organizationId }).exec();
                        if (existingMethods.length === 0) {
                            dbPaymentMethod.isDefault = true;
                        }
                    }
                    await dbPaymentMethod.save();
                }
                else if (setAsDefault && !dbPaymentMethod.isDefault) {
                    await this.orgPaymentMethodModel.updateMany({ organizationId, isDefault: true }, { $set: { isDefault: false } });
                    dbPaymentMethod.isDefault = true;
                    await dbPaymentMethod.save();
                }
            }
            else {
                dbPaymentMethod = await this.orgPaymentMethodModel.findOne({
                    stripePaymentMethodId: paymentMethodId,
                    organizationId,
                });
            }
            const transaction = new this.orgTransactionModel({
                organizationId,
                userId,
                amount: paymentDetails.amount / 100,
                currency: paymentDetails.currency,
                status: paymentIntent.status,
                paymentMethodId: dbPaymentMethod ? dbPaymentMethod._id : null,
                stripePaymentIntentId: paymentIntent.id,
                stripeCustomerId: customer.id,
                description: paymentDetails.description,
                email,
                metadata: {
                    paymentMethodType: 'card',
                },
            });
            await transaction.save();
            if (paymentIntent.status === 'succeeded') {
                try {
                    const organization = await this.organizationsService.findOne(organizationId);
                    const currentCredits = organization.credits || 0;
                    const amountInDollars = paymentDetails.amount / 100;
                    const newCredits = currentCredits + amountInDollars;
                    await this.organizationsService.updateBilling(organizationId, { credits: newCredits });
                    this.loggerService.log(`Updated credits for organization ${organizationId}: ${currentCredits} + ${amountInDollars} = ${newCredits}`);
                }
                catch (orgError) {
                    this.loggerService.error(`Error updating organization credits for payment ${paymentIntent.id}`, orgError);
                }
            }
            return {
                success: paymentIntent.status === 'succeeded',
                status: paymentIntent.status,
                transactionId: transaction._id,
                paymentIntentId: paymentIntent.id,
                paymentMethodId: dbPaymentMethod ? dbPaymentMethod._id : null,
                clientSecret: paymentIntent.client_secret,
                savedPaymentMethod: !!dbPaymentMethod,
            };
        }
        catch (error) {
            this.loggerService.error('Error processing organization payment', error);
            if (error.type) {
                switch (error.type) {
                    case 'StripeCardError':
                        throw new common_1.HttpException(error.message || 'Your card was declined', common_1.HttpStatus.BAD_REQUEST);
                    case 'StripeInvalidRequestError':
                        throw new common_1.HttpException('Invalid payment information', common_1.HttpStatus.BAD_REQUEST);
                    default:
                        throw new common_1.HttpException(error.message || 'Payment processing failed', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
            throw new common_1.HttpException('Failed to process organization payment', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrganizationTransactionHistory(organizationId, page = 1, limit = 10) {
        try {
            const skip = (page - 1) * limit;
            const transactions = await this.orgTransactionModel
                .find({ organizationId })
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .exec();
            const total = await this.orgTransactionModel.countDocuments({ organizationId });
            return {
                transactions,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit),
                },
            };
        }
        catch (error) {
            this.loggerService.error('Error retrieving organization transaction history', error);
            throw new common_1.HttpException('Failed to retrieve organization transaction history', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.BillingService = BillingService;
exports.BillingService = BillingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(organization_payment_method_schema_1.OrganizationPaymentMethod.name)),
    __param(1, (0, mongoose_1.InjectModel)(organization_transaction_schema_1.OrganizationTransaction.name)),
    __param(5, (0, common_1.Inject)((0, common_1.forwardRef)(() => organizations_service_1.OrganizationsService))),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        config_1.ConfigService,
        logger_service_1.LoggerService,
        users_service_1.UsersService,
        organizations_service_1.OrganizationsService])
], BillingService);
//# sourceMappingURL=billing.service.js.map