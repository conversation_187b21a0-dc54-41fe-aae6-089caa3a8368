import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
  } from "@/components/ui/accordion";


  
export default function FAQSection() {
  return (
    <section className="py-20">
    <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 className="text-3xl font-bold text-center mb-12">Common Questions</h2>
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="item-1">
          <AccordionTrigger>What is Orova AI?</AccordionTrigger>
          <AccordionContent>
            Orova AI is a platform that allows you to create, customize, and
            deploy AI agents using natural language. No coding required - just
            describe what you want your agent to do.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger>
            Do I need technical knowledge to use Orova AI?
          </AccordionTrigger>
          <AccordionContent>
            No technical knowledge is required! Our platform is designed to be
            user-friendly and accessible to everyone. You can create AI agents
            using natural language instructions.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger>
            What AI models are supported?
          </AccordionTrigger>
          <AccordionContent>
            We support integration with popular AI models including OpenAI
            (GPT-4), Anthropic Claude, Google Gemini, and more. You can easily
            configure and switch between different models.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-4">
          <AccordionTrigger>
            Can I customize the voice of my AI agents?
          </AccordionTrigger>
          <AccordionContent>
            Yes! Through our integration with ElevenLabs and Deepgram, you can
            select from a variety of voices or create custom voices for your AI
            agents.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  </section>
  )
}
