import { Agent } from '@/types/agent.types';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

async function fetchAgents() {
  const token = localStorage.getItem('access_token');
  const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents`, {
    headers: { 'Authorization': `Bear<PERSON> ${token}` },
  });
  
  if (!response.ok) {
    throw new Error('Failed to fetch agents');
  }
  
  return response.json();
}

export function useAgentsList() {
  const queryClient = useQueryClient();
  
  const { data: agents = [], isLoading: agentsisLoading, error: AgentsError} = useQuery<Agent[]>({
    queryKey: ['agents'],
    queryFn: fetchAgents,
    staleTime: Infinity,
    gcTime: Infinity, // Changed from cacheTime
  });

  // Use mutation for updating agents data
  const { mutate: setAgents } = useMutation({
    mutationFn: (newAgents: Agent[]) => Promise.resolve(newAgents),
    onSuccess: (newAgents) => {
      queryClient.setQueryData(['agents'], newAgents);
    },
  });

  // Add delete mutation
  const deleteAgentMutation = useMutation({
    mutationFn: async (agentId: string) => {
      const token = localStorage.getItem('access_token');
      if (!token) throw new Error("Authentication required");

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents/${agentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete agent: ${response.status}`);
      }
    },
    onMutate: async (agentId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['agents'] });

      // Snapshot the previous value
      const previousAgents = queryClient.getQueryData<Agent[]>(['agents']);

      // Optimistically remove the agent from the list
      queryClient.setQueryData<Agent[]>(['agents'], old => 
        old?.filter(agent => agent.id !== agentId) ?? []
      );

      return { previousAgents };
    },
    onSuccess: (_, agentId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      queryClient.removeQueries({ queryKey: ['agent', agentId] });
    }
  });

  return { 
    agents, 
    setAgents,
    agentsisLoading, 
    deleteAgentMutation,
    AgentsError : AgentsError instanceof Error ? AgentsError.message : null
  };
}