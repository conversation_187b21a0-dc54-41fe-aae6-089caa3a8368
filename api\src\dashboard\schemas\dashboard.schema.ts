import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

interface CallMetrics {
  totalCalls: number;
  totalMinutes: number;
  averageLength: number;
  connectionRate: number;
  answerRate: number;
}

interface TotalCounts {
  totalCampaigns: number;
  totalScheduledCalls: number;
  totalContacts: number;
}

interface Sentiments {
  positive: number;
  neutral: number;
  negative: number;
}

interface CallEndReason {
  reason: string;
  count: number;
  percentage: number;
}

interface TopAgent {
  id: string;
  name: string;
  avatar: string | null;
  role: string;
  status: string;
  callCount: number;
}

@Schema({ timestamps: true })
export class DashboardStats extends Document {
  @Prop({ required: true })
  dateRange: string; // 'all', '7', '14', '30', '90'

  @Prop({ required: true })
  agentType: string; // 'all' or specific agent role

  @Prop({ type: Object, required: true })
  callMetrics: CallMetrics;

  @Prop({ type: Object, required: true })
  totalCounts: TotalCounts;

  @Prop({ type: Object, required: true })
  sentiments: Sentiments;

  @Prop([{ type: Object }])
  callEndReasons: CallEndReason[];

  @Prop([String])
  agentRoles: string[];

  @Prop([{ type: Object }])
  topAgents: TopAgent[];

  @Prop([{ type: Object }])
  recentCalls: Array<{
    _id: string;
    fullName: string;
    mobileNumber: string;
    callStartTime: string;
    callEndTime: string;
    callDuration: string;
    callRoute: string;
    callPurpose: string;
    callEndReason: string;
    agent: string;
  }>;

  @Prop([{ type: Object }])
  recentSchedules: Array<{
    _id: string;
    agentId: string;
    contacts: { Name: string; MobileNumber: string }[];
    scheduledTime: string;
    region: string;
    status: string;
    scheduledByName: string;
    scheduledByTimestamp: string;
  }>;

  @Prop([{ type: Object }])
  recentCampaigns: Array<{
    _id: string;
    name: string;
    startDate: string;
    endDate: string;
    status: string;
  }>;

  @Prop({ default: Date.now })
  lastUpdated: Date;
}

export const DashboardStatsSchema = SchemaFactory.createForClass(DashboardStats);

// Add indexes for better query performance
DashboardStatsSchema.index({ dateRange: 1, agentType: 1 });
DashboardStatsSchema.index({ lastUpdated: -1 });