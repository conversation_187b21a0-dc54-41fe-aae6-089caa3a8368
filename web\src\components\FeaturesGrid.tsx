
import { <PERSON>, CardContent } from './ui/card'
import { <PERSON><PERSON>, <PERSON>, MessageSquare, Mic, Settings, Spark<PERSON> } from 'lucide-react'



export default function FeaturesGrid() {
  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-center mb-12">
          Everything You Need to Build AI Agents
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card>
            <CardContent className="p-6">
              <Bot className="h-10 w-10 text-[#a855f7] mb-4" />
              <h3 className="text-xl font-semibold mb-2">Custom Agents</h3>
              <p className="text-gray-600">
                Create AI agents with specific roles and personalities using
                natural language instructions.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <Brain className="h-10 w-10 text-[#4157ea] mb-4" />
              <h3 className="text-xl font-semibold mb-2">Knowledge Base</h3>
              <p className="text-gray-600">
                Train your agents with custom knowledge by uploading documents
                and creating collections.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <MessageSquare className="h-10 w-10 text-green-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Natural Chat</h3>
              <p className="text-gray-600">
                Engage in natural conversations with your agents through text or
                voice interface.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <Mic className="h-10 w-10 text-red-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Voice Enabled</h3>
              <p className="text-gray-600">
                Give your agents unique voices using ElevenLabs or Deepgram
                integration.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <Settings className="h-10 w-10 text-orange-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Easy Integration</h3>
              <p className="text-gray-600">
                Connect with popular AI models like OpenAI, Claude, and Gemini
                with simple configuration.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <Sparkles className="h-10 w-10 text-yellow-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">AI Templates</h3>
              <p className="text-gray-600">
                Get started quickly with pre-built agent templates for common use
                cases.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
