"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, CheckCircle, ChevronRight, Loader2, Search } from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

// Provider information with logos
const providers = [
  {
    id: "twilio",
    name: "<PERSON>wi<PERSON>",
    logo: "https://www.vectorlogo.zone/logos/twilio/twilio-icon.svg",
  },
  {
    id: "vonage",
    name: "Von<PERSON>",
    logo: "https://www.vectorlogo.zone/logos/ansi/ansi-icon.svg", 
  },
  {
    id: "telnyx",
    name: "Telnyx",
    logo: "https://www.vectorlogo.zone/logos/google_cloud_run/google_cloud_run-icon.svg", 
  }
];

type AvailableNumber = {
  id: string;
  number: string;
  region: string;
  capabilities: string[];
  monthlyPrice: number;
  setupFee: number;
  provider: "twilio" | "vonage" | "telnyx";
};

export default function BuyPhoneNumberContent() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [areaCode, setAreaCode] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<"twilio" | "vonage" | "telnyx">("twilio");
  const [isSearching, setIsSearching] = useState(false);
  const [activeTab, setActiveTab] = useState("search");
  const [selectedNumber, setSelectedNumber] = useState<AvailableNumber | null>(null);
  const [isPurchasing, setIsPurchasing] = useState(false);

  // Mock available numbers by provider
  const availableNumbersByProvider: Record<string, AvailableNumber[]> = {
    twilio: [
      { id: "1", number: "+****************", region: "San Francisco, CA", capabilities: ["voice", "sms", "mms"], monthlyPrice: 1.00, setupFee: 0, provider: "twilio" },
      { id: "2", number: "+****************", region: "New York, NY", capabilities: ["voice", "sms"], monthlyPrice: 1.00, setupFee: 0, provider: "twilio" },
      { id: "3", number: "+****************", region: "Chicago, IL", capabilities: ["voice", "sms", "mms"], monthlyPrice: 1.00, setupFee: 0, provider: "twilio" },
      // Add more mock numbers to demonstrate scrolling
      { id: "9", number: "+****************", region: "Pittsburgh, PA", capabilities: ["voice", "sms"], monthlyPrice: 1.00, setupFee: 0, provider: "twilio" },
      { id: "10", number: "+1 (617) 555-3344", region: "Boston, MA", capabilities: ["voice", "sms", "mms"], monthlyPrice: 1.00, setupFee: 0, provider: "twilio" },
      { id: "11", number: "+1 (503) 555-5566", region: "Portland, OR", capabilities: ["voice", "sms"], monthlyPrice: 1.00, setupFee: 0, provider: "twilio" },
      { id: "12", number: "+1 (713) 555-7788", region: "Houston, TX", capabilities: ["voice", "sms", "mms"], monthlyPrice: 1.00, setupFee: 0, provider: "twilio" },
    ],
    vonage: [
      { id: "4", number: "+****************", region: "San Mateo, CA", capabilities: ["voice", "sms"], monthlyPrice: 0.90, setupFee: 0, provider: "vonage" },
      { id: "5", number: "+****************", region: "Miami, FL", capabilities: ["voice", "sms"], monthlyPrice: 0.90, setupFee: 0, provider: "vonage" },
      { id: "13", number: "+****************", region: "Minneapolis, MN", capabilities: ["voice", "sms"], monthlyPrice: 0.90, setupFee: 0, provider: "vonage" },
    ],
    telnyx: [
      { id: "6", number: "+****************", region: "Dallas, TX", capabilities: ["voice", "sms", "mms"], monthlyPrice: 0.80, setupFee: 0, provider: "telnyx" },
      { id: "7", number: "+****************", region: "Atlanta, GA", capabilities: ["voice", "sms"], monthlyPrice: 0.80, setupFee: 0, provider: "telnyx" },
      { id: "8", number: "+****************", region: "Seattle, WA", capabilities: ["voice", "sms", "mms"], monthlyPrice: 0.80, setupFee: 0, provider: "telnyx" },
      { id: "14", number: "+****************", region: "Las Vegas, NV", capabilities: ["voice", "sms"], monthlyPrice: 0.80, setupFee: 0, provider: "telnyx" },
    ]
  };

  // Filter available numbers based on search query and area code
  const filteredNumbers = availableNumbersByProvider[selectedProvider].filter(num => {
    if (!searchQuery && !areaCode) return true;
    
    const matchesSearch = searchQuery ? 
      num.number.includes(searchQuery) || num.region.toLowerCase().includes(searchQuery.toLowerCase()) 
      : true;
      
    const matchesAreaCode = areaCode ? 
      num.number.replace(/\D/g, '').substring(1, 4) === areaCode 
      : true;
      
    return matchesSearch && matchesAreaCode;
  });

  const handleSearchNumbers = () => {
    setIsSearching(true);
    
    // Simulate API call delay
    setTimeout(() => {
      setIsSearching(false);
    }, 1000);
  };

  const handlePurchaseNumber = async () => {
    if (!selectedNumber) return;

    setIsPurchasing(true);
    
    // Simulate API call to purchase number
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Redirect to phone numbers page
    router.push('/phonenumber');
  };

  return (
    <div className="container py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Button 
            variant="ghost" 
            className="mr-2" 
            onClick={() => router.push('/phonenumber')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
          </Button>
          <h1 className="text-2xl font-semibold">Buy a Phone Number</h1>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="search">Search for Numbers</TabsTrigger>
          <TabsTrigger value="configure" disabled={!selectedNumber}>Configure Number</TabsTrigger>
        </TabsList>

        <TabsContent value="search">
          <div className="grid gap-6 md:grid-cols-4">
            {/* Search sidebar */}
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Search Filters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Provider selection with logos */}
                <div className="space-y-2">
  <Label>Provider</Label>
  <RadioGroup 
    value={selectedProvider} 
    onValueChange={(value: "twilio" | "vonage" | "telnyx") => setSelectedProvider(value)}
    className="space-y-2"
  >
    {providers.map(provider => (
      <div 
        key={provider.id} 
        className={`flex items-center space-x-2 rounded-md border p-3 ${
          selectedProvider === provider.id ? 'border-primary bg-primary/5' : ''
        }`}
      >
        <RadioGroupItem value={provider.id} id={provider.id} />
        <Label 
          htmlFor={provider.id} 
          className="flex flex-1 items-center cursor-pointer"
        >
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-md flex items-center justify-center bg-background border">
              <div className="relative h-6 w-6">
                <Image 
                  src={provider.logo} 
                  alt={provider.name} 
                  fill 
                  className="object-contain" 
                />
              </div>
            </div>
            <p className="text-sm font-medium">{provider.name}</p>
          </div>
        </Label>
      </div>
    ))}
  </RadioGroup>
</div>

                <div className="space-y-2">
                  <Label>Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Number or location"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Area Code</Label>
                  <Input
                    placeholder="e.g. 415"
                    value={areaCode}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '');
                      if (value.length <= 3) {
                        setAreaCode(value);
                      }
                    }}
                  />
                </div>

                <Button
                  className="w-full mt-4"
                  onClick={handleSearchNumbers}
                  disabled={isSearching}
                >
                  {isSearching ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    "Search Numbers"
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Results table */}
            <Card className="md:col-span-3">
              <CardHeader>
                <CardTitle>Available Numbers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-md overflow-hidden">
                  <div className="max-h-[calc(100vh-340px)] overflow-auto">
                    <Table>
                      <TableHeader className="sticky top-0 bg-background z-10">
                        <TableRow>
                          <TableHead className="bg-background w-[180px]">Number</TableHead>
                          <TableHead className="bg-background w-[200px]">Location</TableHead>
                          <TableHead className="bg-background">Capabilities</TableHead>
                          <TableHead className="bg-background">Price</TableHead>
                          <TableHead className="bg-background w-[100px]"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredNumbers.length > 0 ? (
                          filteredNumbers.map((num) => (
                            <TableRow key={num.id} className={selectedNumber?.id === num.id ? "bg-muted/50" : ""}>
                              <TableCell className="font-medium">{num.number}</TableCell>
                              <TableCell>{num.region}</TableCell>
                              <TableCell>
                                <div className="flex flex-wrap gap-1">
                                  {num.capabilities.map(cap => (
                                    <Badge key={cap} variant="outline" className="capitalize">
                                      {cap}
                                    </Badge>
                                  ))}
                                </div>
                              </TableCell>
                              <TableCell>${num.monthlyPrice.toFixed(2)}/mo</TableCell>
                              <TableCell>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full"
                                  onClick={() => {
                                    setSelectedNumber(num);
                                    setActiveTab("configure");
                                  }}
                                >
                                  Select <ChevronRight className="ml-1 h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center h-24 text-muted-foreground">
                              {searchQuery || areaCode ? 
                                "No numbers found matching your search criteria" :
                                "Enter search criteria to find available numbers"
                              }
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="configure">
          {selectedNumber && (
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Number Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-6 border rounded-md bg-muted/30">
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Selected Number</p>
                        <p className="text-2xl font-semibold">{selectedNumber.number}</p>
                        <p className="text-sm">{selectedNumber.region}</p>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <div className="h-12 w-12 rounded-md flex items-center justify-center bg-background border">
                          <div className="relative h-7 w-7">
                            <Image 
                              src={providers.find(p => p.id === selectedNumber.provider)?.logo || ''} 
                              alt={selectedNumber.provider} 
                              fill 
                              className="object-contain" 
                            />
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Provider</p>
                          <p className="text-lg font-medium capitalize">{selectedNumber.provider}</p>
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm text-muted-foreground">Capabilities</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {selectedNumber.capabilities.map(cap => (
                            <Badge key={cap} variant="outline" className="capitalize">
                              {cap}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Number Verification</Label>
                    <div className="flex items-center space-x-2 p-4 border rounded-md bg-green-50 dark:bg-green-900/20">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <p>This number is ready to purchase</p>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => {
                        setSelectedNumber(null);
                        setActiveTab("search");
                      }}
                    >
                      Go Back
                    </Button>
                    <Button
                      className="flex-1 bg-primary text-primary-foreground hover:bg-primary/90"
                      onClick={handlePurchaseNumber}
                      disabled={isPurchasing}
                    >
                      {isPurchasing ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Purchasing...
                        </>
                      ) : (
                        "Purchase Number"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Pricing Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border rounded-md p-4">
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Monthly fee</span>
                        <span className="font-medium">${selectedNumber.monthlyPrice.toFixed(2)}/month</span>
                      </div>
                      
                      {selectedNumber.setupFee > 0 && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Setup fee</span>
                          <span className="font-medium">${selectedNumber.setupFee.toFixed(2)}</span>
                        </div>
                      )}
                      
                      <div className="flex justify-between pt-4 border-t">
                        <span className="font-semibold">Total today</span>
                        <span className="font-semibold">${(selectedNumber.monthlyPrice + selectedNumber.setupFee).toFixed(2)}</span>
                      </div>
                      
                      <div className="flex justify-between text-muted-foreground text-sm">
                        <span>Recurring monthly charge</span>
                        <span>${selectedNumber.monthlyPrice.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Additional Information</Label>
                    <div className="text-sm text-muted-foreground space-y-2">
                      <p>• Voice calls are billed at per-minute rates according to your plan.</p>
                      <p>• SMS messages are charged per segment according to your plan.</p>
                      <p>• You can cancel this number at any time with no further charges.</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}