/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { User, Mail, Lock, Camera, Loader2 } from "lucide-react";
import { UserInfo } from "../../(auth)/actions/auth";
import FadeIn from "@/animations/FadeIn";
import { getCurrentUser } from "@/lib/auth-client";
import { set } from "zod";

export default function ProfileContent() {
  const router = useRouter();
  const [user, setUser] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Form state
  const [name, setName] = useState("");
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  // Fetch user data when component mounts
  useEffect(() => {
    async function fetchUserData() {
      try {
        const result = await getCurrentUser();
        
        if (result.success && result.user) {
          setUser(result.user);
          setFullName(result.user.fullName);
          // Initialize form with user data
          setName(result.user.email.split('@')[0]); // Use part of email as name if no name provided
          setEmail(result.user.email);
        } else {
          // If not authenticated, redirect to login
          router.push('/login');
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    }
    
    fetchUserData();
  }, [router]);

  // const handleUpdateProfile = async (e: React.FormEvent) => {
  //   e.preventDefault();
  //   setSaving(true);
    
  //   // Demo only - simulate saving
  //   setTimeout(() => {
  //     setSaving(false);
  //   }, 1000);
  // };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-160px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <FadeIn>
      <div className="max-w-md mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Your Profile</h1>
        
        <div className="flex flex-col items-center mb-8">
          <div className="relative mb-6 group">
            <Avatar className="h-24 w-24 border-4 border-white dark:border-gray-800 shadow-lg">
              <AvatarImage src="" alt={name} />
              <AvatarFallback className="text-3xl bg-gradient-to-r from-[#383D73] to-[#74546D] text-white">
                {name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="absolute bottom-0 right-0 bg-white dark:bg-gray-800 text-primary dark:text-gray-300 rounded-full p-2 shadow-md cursor-pointer">
              <Camera className="h-5 w-5" />
            </div>
          </div>
        </div>

        <Card className="bg-white dark:bg-gray-800">
          <CardContent className="pt-6">
            <form className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    Display Name
                  </Label>
                  <Input 
                    id="fullName" 
                    value={fullName} 
                    onChange={(e) => setName(e.target.value)}
                    className="bg-gray-50 dark:bg-gray-900/50"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    Email Address
                  </Label>
                  <Input 
                    id="email" 
                    type="email" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)}
                    className="bg-gray-50 dark:bg-gray-900/50"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password" className="flex items-center gap-2">
                    <Lock className="h-4 w-4 text-gray-500" />
                    Password
                  </Label>
                  <Input 
                    id="password" 
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="bg-gray-50 dark:bg-gray-900/50"
                  />
                </div>
              </div>
              
              {/* <div className="pt-4 flex justify-center">
                <Button 
                  type="submit" 
                  disabled={saving}
                  className="w-full"
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </div> */}
            </form>
          </CardContent>
        </Card>
      </div>
    </FadeIn>
  );
}