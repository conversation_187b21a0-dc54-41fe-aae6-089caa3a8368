
"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {Table,TableBody,TableCell,TableHead,TableHeader,TableRow} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Search, PauseCircle, PlayCircle, Edit, Trash2, Download,Plus,Loader2, MoreVertical} from "lucide-react";
import {DropdownMenu,DropdownMenuContent,DropdownMenuItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {Select,SelectContent,SelectItem,SelectTrigger,SelectValue} from "@/components/ui/select";
import {Dialog,DialogContent,DialogDescription,DialogFooter,DialogHeader,DialogTitle} from "@/components/ui/dialog";

import FadeIn from "@/animations/FadeIn";
import { deleteCampaign, fetchCampaigns, updateCampaignStatus } from "@/app/api/campaign";
import agentLisa from "@/assets/img/Binghatti-Lisa.jpeg";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useCredits } from "@/contexts/CreditContext";
import { LowCreditAlert } from "@/components/LowCreditAlert";
import { CreditWarningAlert } from "@/components/CreditWarningAlert";
import Link from "next/link";
import { Agent } from "@/types/agent.types";

// Interface for Campaign
interface Campaign {
  _id: string;
  name: string;
  concurrentCalls: number;
  dailyCost: number;
  startDate: string;
  endDate: string | null;
  successRate: number;
  sentiment: string;
  status: string;
  agentId?: string;
  maxRecalls?: number;
  createdAt?: string;
  updatedAt?: string;
}



// Function to format dates
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};



export default function CampaignContent() {

  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { credits, organizationCreditThreshold } = useCredits();



  // Load campaigns function
  const loadCampaigns = async (status?: string) => {
    setError(null);
    try {
      const data = await fetchCampaigns(status);
      setCampaigns(data);
    } catch (err) {
      console.error('Error fetching campaigns:', err);
      setError('Failed to load campaigns. Please try again later.');
    }
  };

  const fetchAgents = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error('No access token available');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/agents`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch agents: ${response.status}`);
      }

      const fetchedAgents = await response.json();
      if (Array.isArray(fetchedAgents)) {
        setAgents(fetchedAgents);
      } else {
        console.error('Unexpected response format from API:', fetchedAgents);
      }
    } catch (error) {
      console.error('Error loading agents:', error);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch agents
        await fetchAgents();
        // Fetch campaigns
        await loadCampaigns();
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  // Filter campaigns
  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || campaign.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Toggle campaign status
  const handleToggleCampaignStatus = async (id: string) => {
    try {
      const campaign = campaigns.find(c => c._id === id);
      if (!campaign) return;

      const newStatus = campaign.status === "active" ? "paused" : "active";

      await updateCampaignStatus(id, newStatus);

      // Update local state
      setCampaigns(campaigns.map(c =>
        c._id === id ? { ...c, status: newStatus } : c
      ));

    } catch (err) {
      console.error('Error updating campaign status:', err);

    }
  };

  // Delete campaign
  const handleDeleteCampaign = async () => {
    if (!selectedCampaign) return;

    setIsSubmitting(true);
    try {
      await deleteCampaign(selectedCampaign._id);

      // Update local state
      setCampaigns(campaigns.filter(c => c._id !== selectedCampaign._id));

      setIsDeleteDialogOpen(false);
    } catch (err) {
      console.error('Error deleting campaign:', err);

    } finally {
      setIsSubmitting(false);
    }
  };



  // Open delete dialog
  const handleDeleteClick = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setIsDeleteDialogOpen(true);
  };




  return (
    <FadeIn>
      {/* Credit Alerts */}
      <CreditWarningAlert credits={credits} threshold={organizationCreditThreshold} />
      <LowCreditAlert credits={credits} threshold={organizationCreditThreshold} />

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Campaigns</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your outbound call campaigns
            </p>
          </div>
          <Link href="/campaign/create">
          <Button
            className="flex items-center gap-2"
            >
            <Plus className="h-4 w-4" />
            New Campaign
          </Button>
            </Link>
        </div>

        <Card>
          <CardContent>
            <div className="flex justify-between items-center mb-6">
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                <Input
                  placeholder="Search campaigns..."
                  className="pl-8 bg-gray-50 dark:bg-gray-800"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2">
                <Select
                  value={statusFilter}
                  onValueChange={(value) => {
                    setStatusFilter(value);
                    loadCampaigns(value !== 'all' ? value : undefined);
                  }}
                >
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="paused">Paused</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
            <Table>
  <TableHeader>
    <TableRow>
      <TableHead>Campaign Name</TableHead>
      <TableHead className="text-center">Agent</TableHead>
      <TableHead className="text-center">Concurrent Calls</TableHead>
      <TableHead>Started</TableHead>
      <TableHead>Ending</TableHead>
      <TableHead className="text-center">Follow-ups</TableHead>
      <TableHead>Status</TableHead>
      <TableHead className="text-right">Actions</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    {loading ? (
      <TableRow>
        <TableCell colSpan={8} className="text-center py-10">
          <div className="flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </TableCell>
      </TableRow>
    ) : error ? (
      <TableRow>
        <TableCell colSpan={8} className="text-center py-6 text-red-500">
          {error}
        </TableCell>
      </TableRow>
    ) : filteredCampaigns.length > 0 ? (
      filteredCampaigns.map((campaign) => (
        <TableRow key={campaign._id}>
          <TableCell className="font-medium">{campaign.name}</TableCell>
          <TableCell>
          {(() => {
            const agent = agents.find(agent => agent.id === campaign.agentId);
            return (
              <div className="flex flex-col items-center justify-center">
                <Avatar className="h-8 w-8">
                  <AvatarImage  src={agent ? agent.avatar : agentLisa.src}
                    alt={agent?.name || 'Unknown'}
                  />
                  <AvatarFallback className="bg-indigo-50 text-indigo-700 text-xs">
                    {agent?.name?.charAt(0) || 'A'}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs font-medium text-gray-600">
                  {agent?.name || ''}
                </span>
              </div>
            );
          })()}
        </TableCell>
          <TableCell className="text-center">{campaign.concurrentCalls}</TableCell>
          <TableCell>{formatDate(campaign.startDate)}</TableCell>
          <TableCell>
          {campaign.endDate ? formatDate(campaign.endDate) : "No End Date"}
        </TableCell>
          <TableCell className="text-center">{campaign.maxRecalls || "—"}</TableCell>
          <TableCell>
            <Badge
              variant={campaign.status === "active" ? "default" : "secondary"}
              className={campaign.status === "active" ? "bg-green-500" : ""}
            >
              {campaign.status === "active" ? "Active" :
               campaign.status === "paused" ? "Paused" :
               campaign.status === "completed" ? "Completed" : "Inactive"}
            </Badge>
          </TableCell>
          <TableCell className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <Link href={`/campaign/edit/${campaign._id}`}>
                <DropdownMenuItem className="cursor-pointer">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Campaign
                </DropdownMenuItem>
                  </Link>
                <DropdownMenuItem onClick={() => handleToggleCampaignStatus(campaign._id)}>
                  {campaign.status === "active" ? (
                    <>
                      <PauseCircle className="mr-2 h-4 w-4 cursor-pointer" />
                      Pause Campaign
                    </>
                  ) : (
                    <>
                      <PlayCircle className="mr-2 h-4 w-4 cursor-pointer" />
                      Resume Campaign
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => handleDeleteClick(campaign)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Campaign
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
      ))
    ) : (
      <TableRow>
        <TableCell colSpan={8} className="text-center py-6 text-gray-500 dark:text-gray-400">
          No campaigns found matching your criteria
        </TableCell>
      </TableRow>
    )}
  </TableBody>
</Table>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Campaign</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this campaign? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedCampaign && (
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
                <p className="font-medium text-red-800 dark:text-red-300">
                  {selectedCampaign.name}
                </p>
                <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                {formatDate(selectedCampaign.startDate)} - {selectedCampaign.endDate ? formatDate(selectedCampaign.endDate) : "Indefinite"}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCampaign}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Campaign"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </FadeIn>
  );
}