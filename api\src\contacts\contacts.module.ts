import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ContactsService } from './contacts.service';
import { ContactsController } from './contacts.controller';
import { ContactSchema } from './schemas/contact.schema';
import { CampaignModule } from 'src/campaign/campaign.module';
import { LoggerModule } from 'src/logger/logger.module';
import { ScheduledCallModule } from 'src/scheduled-call/scheduled-call.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'Contact', schema: ContactSchema }]),
    forwardRef(() => CampaignModule),
    LoggerModule,
    forwardRef(() => ScheduledCallModule)
  ],
  providers: [ContactsService],
  controllers: [ContactsController],
  exports: [ContactsService,MongooseModule],
})
export class ContactsModule {}