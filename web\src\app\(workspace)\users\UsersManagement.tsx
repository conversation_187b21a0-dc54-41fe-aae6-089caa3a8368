
"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Pencil, Trash2, Plus, Loader2, Search, ShieldCheck, ShieldAlert } from "lucide-react";
import { toast } from "sonner";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { addUser, approveUser, deleteUser, fetchUsers, NewUser, revokeUserAccess, updateUser, User } from '@/app/api/users';



export default function UsersManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAccessDialogOpen, setIsAccessDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [formData, setFormData] = useState<NewUser>({
    fullName: '',
    email: '',
    password: '',
  });
  const [processingUserAction, setProcessingUserAction] = useState<string | null>(null);


  // Fetch users on component mount
  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const data = await fetchUsers();
      setUsers(data);
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setProcessingUserAction('add');
    
    try {
      // Store the current form data to pass to the API
      const newUserData = {
        fullName: formData.fullName,
        email: formData.email,
        password: formData.password,
      };
      
      // First clear the form and close the dialog for better UX
      setFormData({ fullName: '', email: '', password: '' });
      setIsAddDialogOpen(false);
      
      // Then make the API call
      await addUser(newUserData);
      
      // Immediately load users after successful addition
      await loadUsers();
    } catch (error) {
      console.error('Error adding user:', error);
    } finally {
      setProcessingUserAction(null);
    }
  };

  const handleEditUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;
    
    setProcessingUserAction('edit');
    
    try {
      // Only send fields that have values
      const updateData: Record<string, string> = {};
      if (formData.fullName) updateData.fullName = formData.fullName;
      if (formData.email) updateData.email = formData.email;
      if (formData.password) updateData.password = formData.password;
      
      await updateUser(selectedUser._id, updateData);
      
      toast.success('User updated successfully');
      setIsEditDialogOpen(false);
      setFormData({ fullName: '', email: '', password: '' });
      loadUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    } finally {
      setProcessingUserAction(null);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    
    setProcessingUserAction('delete');
    
    try {
      await deleteUser(selectedUser._id);
      
      toast.success('User deleted successfully');
      setIsDeleteDialogOpen(false);
      loadUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    } finally {
      setProcessingUserAction(null);
    }
  };

  const toggleUserApproval = async () => {
    if (!selectedUser) return;
    
    const approve = !selectedUser.isApproved;
    setProcessingUserAction('access');
    
    try {
      if (approve) {
        // Approve user
        await approveUser(selectedUser._id);
      } else {
        // Revoke access
        await revokeUserAccess(selectedUser._id);
      }
      
      toast.success(`User ${approve ? 'access granted' : 'access revoked'} successfully`);
      setIsAccessDialogOpen(false);
      loadUsers();
    } catch (error) {
      console.error('Error updating user status:', error);
      toast.error('Failed to update user status');
    } finally {
      setProcessingUserAction(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return isNaN(date.getTime()) 
      ? 'N/A' 
      : date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        });
  };

  // Filter users based on search term
  const filteredUsers = users.filter(
    (user) =>
      user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Users Management</h1>
        <Button
          className="bg-primary hover:bg-primary/90 flex items-center gap-2"
          onClick={() => setIsAddDialogOpen(true)}
        >
          <Plus className="h-4 w-4" />
          Add User
        </Button>
      </div>

      {/* Search */}
      <div className="flex flex-col sm:flex-row gap-2 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search users..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-card rounded-lg border shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Date Created</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="h-64 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                    <p className="text-lg font-medium text-gray-600 dark:text-gray-300">
                      Loading users...
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <TableRow key={user._id}>
                  <TableCell className="font-medium">{user.fullName}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{formatDate(user.createdAt)}</TableCell>
                  <TableCell>
                    {user.isApproved ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-50">
                        <ShieldCheck className="h-3 w-3 mr-1" />
                        Approved
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 hover:bg-red-50">
                        <ShieldAlert className="h-3 w-3 mr-1" />
                        Denied
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setIsAccessDialogOpen(true);
                        }}
                        className={user.isApproved ? 
                          "text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700" : // Changed to red for revoke
                        "text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700"
                        }
                        disabled={processingUserAction === user._id}
                      >
                        {user.isApproved ? "Revoke Access" : "Grant Access"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setFormData({
                            fullName: '',
                            email: '',
                            password: '',
                          });
                          setIsEditDialogOpen(true);
                        }}
                        className="text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700"
                      >
                        <Pencil className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
                        onClick={() => {
                          setSelectedUser(user);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="h-64 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <p className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
                      No users found
                    </p>
                    <p className="text-sm text-muted-foreground max-w-md text-center mb-4">
                      {searchTerm ? "No users match your search criteria" : "Create users to manage access to the platform"}
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => setIsAddDialogOpen(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add your first user
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Add User Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
  <DialogContent className="sm:max-w-[425px]">
    <DialogHeader>
      <DialogTitle>Add New User</DialogTitle>
    </DialogHeader>
    <form onSubmit={handleAddUser}>
      <div className="grid gap-4 py-4">
        <div className="grid gap-2">
          <Label htmlFor="fullName">Full Name</Label>
          <Input
            id="fullName"
            name="fullName"
            value={formData.fullName}
            onChange={handleInputChange}
            required
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            required
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            required
          />
        
        </div>
      </div>
      <DialogFooter className="flex flex-col sm:flex-row gap-2">
        <Button 
          type="button" 
          variant="outline" 
          onClick={() => setIsAddDialogOpen(false)}
          disabled={!!processingUserAction}
        >
          Cancel
        </Button>
        <Button 
          type="submit"
          disabled={!!processingUserAction}
          className="bg-primary hover:bg-primary/90"
        >
          {processingUserAction === 'add' ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </>
          ) : (
            'Add User'
          )}
        </Button>
      </DialogFooter>
    </form>
  </DialogContent>
</Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleEditUser}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-fullName">Full Name</Label>
                <Input
                  id="edit-fullName"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  placeholder={selectedUser?.fullName}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-email">Email</Label>
                <Input
                  id="edit-email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder={selectedUser?.email}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-password">New Password (leave blank to keep current)</Label>
                <Input
                  id="edit-password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsEditDialogOpen(false)}
                disabled={!!processingUserAction}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={!!processingUserAction}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {processingUserAction === 'edit' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Update User'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete User</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete user <strong>{selectedUser?.fullName}</strong>? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={!!processingUserAction}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={e => {
                e.preventDefault();
                handleDeleteUser();
              }}
              disabled={!!processingUserAction}
            >
              {processingUserAction === 'delete' ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete User'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Access Control Dialog */}
      <AlertDialog open={isAccessDialogOpen} onOpenChange={setIsAccessDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {selectedUser?.isApproved ? 'Revoke Access' : 'Grant Access'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedUser?.isApproved ? (
                <>Are you sure you want to <strong>revoke access</strong> for {selectedUser?.fullName}? They will no longer be able to log in until access is restored.</>
              ) : (
                <>Are you sure you want to <strong>grant access</strong> to {selectedUser?.fullName}? This will allow them to log in to the platform.</>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={processingUserAction === 'access'}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              className={selectedUser?.isApproved ? 
                 "bg-black hover:bg-gray-800 text-white" : // Changed from amber to black
    "bg-green-600 hover:bg-green-700 text-white"
              }
              onClick={e => {
                e.preventDefault();
                toggleUserApproval();
              }}
              disabled={processingUserAction === 'access'}
            >
              {processingUserAction === 'access' ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : selectedUser?.isApproved ? (
                'Revoke Access'
              ) : (
                'Grant Access'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}