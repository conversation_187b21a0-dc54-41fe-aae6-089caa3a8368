import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ScheduledCallDocument = ScheduledCall & Document;

@Schema({ timestamps: true })
export class ScheduledCall {
  @Prop({ required: true })
  agentId: string;

  @Prop({ required: true, type: [{ Name: String, MobileNumber: String }] })
  contacts: { Name: string; MobileNumber: string }[];

  @Prop({ required: true })
  scheduledTime: Date;

  @Prop({ required: true })
  region: string;

  @Prop({ default: 'pending' })
  status: string;

  @Prop({ required: true })
  scheduledByName: string;

  @Prop({ required: true })
  scheduledByTimestamp: Date;

  @Prop({ default: 0 })
  retryCount: number;

  @Prop()
  lastProcessedAt: Date;
}

export const ScheduledCallSchema = SchemaFactory.createForClass(ScheduledCall);


ScheduledCallSchema.set('toJSON', { 
  virtuals: true,
  transform: (doc, ret) => {
    delete ret.id;
    return ret;
  }
});

ScheduledCallSchema.set('toObject', { 
  virtuals: true,
  transform: (doc, ret) => {
    delete ret.id;
    return ret;
  }
});

// Static method to get total count
ScheduledCallSchema.statics.getTotalCount = async function() {
  return await this.countDocuments();
};

// Virtual for total schedules count
ScheduledCallSchema.virtual('totalSchedules').get(function(this: any) {
  return this._totalCount;
});


export interface UpdateScheduledCallDto {
  agentId?: string;
  contacts?: { Name: string; MobileNumber: string }[];
  scheduledTime?: Date;
  region?: string;
  status?: string;
  scheduledByName?: string;
  scheduledByTimestamp?: Date;
  retryCount?: number;
  lastProcessedAt?: Date;
}
