import { API_URL } from "./config";

// Types
export interface Organization {
  _id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive' | 'suspended';
  credits: number; // Paid credits
  monthlyFreeCredits: number; // Monthly allowance in minutes
  monthlyFreeCreditsUsed: number; // Used free credits this month
  lastMonthlyReset: string; // Last time monthly credits were reset
  usingFreeCredits: boolean; // Whether currently using free credits
  autoRechargeEnabled: boolean;
  autoRechargeThreshold: number;
  autoRechargeAmount: number;
  // Billing configuration settings (moved from global settings)
  callPricePerMinute: number;
  minimumCreditsThreshold: number;
  monthlyMinutesAllowance: number;
  stripeCustomerId?: string;
  // Email notification settings
  fullName?: string; // Client's full name for email personalization
  email?: string; // Email address to send credit notifications to
  lastWarningEmailSent?: string; // Last time warning email was sent
  lastRunoutEmailSent?: string; // Last time runout email was sent
  adminUsers: string[];
  users: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateOrganizationRequest {
  name: string;
  description?: string;
  status?: 'active' | 'inactive' | 'suspended';
  adminUsers?: string[];
}

export interface UpdateOrganizationRequest {
  name?: string;
  description?: string;
  status?: 'active' | 'inactive' | 'suspended';
  adminUsers?: string[];
  users?: string[];
}

export interface UpdateOrganizationBillingRequest {
  credits?: number;
  autoRechargeEnabled?: boolean;
  autoRechargeThreshold?: number;
  autoRechargeAmount?: number;
  callPricePerMinute?: number;
  minimumCreditsThreshold?: number;
  monthlyMinutesAllowance?: number;
  monthlyResetDate?: number;
}

// API Functions
export const getOrganizations = async (): Promise<Organization[]> => {
  const response = await fetch(`${API_URL}/api/organizations`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch organizations');
  }

  return response.json();
};

export const getMyOrganizations = async (): Promise<Organization[]> => {
  const response = await fetch(`${API_URL}/api/organizations/my-organizations`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch your organizations');
  }

  return response.json();
};

export const getOrganization = async (id: string): Promise<Organization> => {
  const response = await fetch(`${API_URL}/api/organizations/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch organization');
  }

  return response.json();
};

export const createOrganization = async (data: CreateOrganizationRequest): Promise<Organization> => {
  const response = await fetch(`${API_URL}/api/organizations`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create organization');
  }

  return response.json();
};

export const updateOrganization = async (id: string, data: UpdateOrganizationRequest): Promise<Organization> => {
  const response = await fetch(`${API_URL}/api/organizations/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update organization');
  }

  return response.json();
};

export const updateOrganizationBilling = async (id: string, data: UpdateOrganizationBillingRequest): Promise<Organization> => {
  const response = await fetch(`${API_URL}/api/organizations/${id}/billing`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update organization billing');
  }

  return response.json();
};

export const deleteOrganization = async (id: string): Promise<{ message: string }> => {
  const response = await fetch(`${API_URL}/api/organizations/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete organization');
  }

  return response.json();
};

export const addUserToOrganization = async (organizationId: string, userId: string, isAdmin: boolean): Promise<Organization> => {
  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    body: JSON.stringify({ isAdmin }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to add user to organization');
  }

  return response.json();
};

export const removeUserFromOrganization = async (organizationId: string, userId: string): Promise<Organization> => {
  const response = await fetch(`${API_URL}/api/organizations/${organizationId}/users/${userId}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to remove user from organization');
  }

  return response.json();
};


