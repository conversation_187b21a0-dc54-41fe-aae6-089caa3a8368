"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Trash2 } from "lucide-react";
import { EmptyState } from "./EmptyState";

type WebLink = {
  id: string;
  url: string;
  title: string;
  description: string;
  status: "active" | "broken";
  lastChecked: Date;
};

type WebLinksViewProps = {
  webLinks: WebLink[];
  isEmpty: boolean;
  onAddClick: () => void;
  onDelete: (id: string) => void;
};

export function WebLinksView({ webLinks, isEmpty, onAddClick, onDelete }: WebLinksViewProps) {
  if (isEmpty) {
    return <EmptyState type="link" onAdd={onAddClick} />;
  }

  return (
    <div className="rounded-md border dark:border-gray-700">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>URL</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Last Checked</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {webLinks.map((link) => (
            <TableRow key={link.id}>
              <TableCell className="font-medium">{link.title}</TableCell>
              <TableCell>
                <a 
                  href={link.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:underline"
                >
                  {link.url}
                  <ExternalLink className="h-3 w-3" />
                </a>
              </TableCell>
              <TableCell>
                <Badge
                  variant={link.status === "active" ? "default" : "destructive"}
                >
                  {link.status}
                </Badge>
              </TableCell>
              <TableCell className="text-gray-500">
                {link.lastChecked.toLocaleDateString()}
              </TableCell>
              <TableCell>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-red-500 hover:text-red-700 dark:text-red-400 hover:dark:text-red-300"
                  onClick={() => onDelete(link.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}