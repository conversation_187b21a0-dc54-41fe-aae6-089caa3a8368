services:
  mongodb:
    image: mongo:latest
    restart: unless-stopped
    env_file:
      - ./mongodb/.env
    volumes:
      - ./mongodb/data:/data/db
    networks:
      - internal

  nestjs:
    build:
      context: ./api
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - ./api/.env
    depends_on:
      - mongodb
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.binghatti-api.rule=Host(`binghatti-test.orova.ai`) && PathPrefix(`/api`)"
      - "traefik.http.routers.binghatti-api.entrypoints=websecure"
      - "traefik.http.routers.binghatti-api.tls=true"
      - "traefik.http.routers.binghatti-api.tls.certresolver=myresolver"
      - "traefik.http.services.binghatti-api.loadbalancer.server.port=4000"
    networks:
      - internal
      - traefik

  nextjs:
    build:
      context: ./web
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - ./web/.env
    # depends_on:
    #   - nestjs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.binghatti-frontend.rule=Host(`binghatti-test.orova.ai`)"
      - "traefik.http.routers.binghatti-frontend.entrypoints=websecure"
      - "traefik.http.routers.binghatti-frontend.tls=true"
      - "traefik.http.routers.binghatti-frontend.tls.certresolver=myresolver"
      - "traefik.http.services.binghatti-frontend.loadbalancer.server.port=3000"
      - "traefik.http.routers.binghatti-frontend.priority=1"
      - "traefik.http.middlewares.binghatti-frontend-headers.headers.customrequestheaders.X-Forwarded-Prefix=/"
      - "traefik.http.routers.binghatti-frontend.middlewares=binghatti-frontend-headers"
    networks:
      - internal
      - traefik

  webhook:
    build:
      context: ./webhook
      dockerfile: Dockerfile
    restart: unless-stopped
    # depends_on:
    #   - nestjs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.binghatti-webhook.rule=Host(`binghatti-test.orova.ai`) && PathPrefix(`/webhook`)"
      - "traefik.http.routers.binghatti-webhook.entrypoints=websecure"
      - "traefik.http.routers.binghatti-webhook.tls=true"
      - "traefik.http.routers.binghatti-webhook.tls.certresolver=myresolver"
      - "traefik.http.services.binghatti-webhook.loadbalancer.server.port=5000"
    networks:
      - internal
      - traefik

  call:
    build:
      context: ./call
      dockerfile: Dockerfile
    restart: unless-stopped
    # depends_on:
    #   - nestjs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.binghatti-call.rule=Host(`binghatti-test.orova.ai`) && PathPrefix(`/call`)"
      - "traefik.http.routers.binghatti-call.entrypoints=websecure"
      - "traefik.http.routers.binghatti-call.tls=true"
      - "traefik.http.routers.binghatti-call.tls.certresolver=myresolver"
      - "traefik.http.services.binghatti-call.loadbalancer.server.port=3000"
    networks:
      - internal
      - traefik

  mongo-express:
    image: mongo-express:latest
    restart: unless-stopped
    env_file:
      - ./mongo-express/.env
    depends_on:
      - mongodb
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.binghatti-mongo-express.rule=Host(`mongo-binghatti-test.orova.ai`)"
      - "traefik.http.routers.binghatti-mongo-express.entrypoints=websecure"
      - "traefik.http.routers.binghatti-mongo-express.tls=true"
      - "traefik.http.routers.binghatti-mongo-express.tls.certresolver=myresolver"
      - "traefik.http.services.binghatti-mongo-express.loadbalancer.server.port=8081"
    networks:
      - internal
      - traefik


networks:
  internal:
    driver: bridge
  traefik:
    external: true
