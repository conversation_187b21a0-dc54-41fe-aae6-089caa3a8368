"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryDto = exports.Emotion = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var Emotion;
(function (Emotion) {
    Emotion["Positive"] = "Positive";
    Emotion["Neutral"] = "Neutral";
    Emotion["SlightlyPositive"] = "Slightly Positive";
    Emotion["SlightlyNegative"] = "Slightly Negative";
    Emotion["Negative"] = "Negative";
})(Emotion || (exports.Emotion = Emotion = {}));
class HistoryDto {
}
exports.HistoryDto = HistoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'marouane', description: 'Full name of the caller' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '+212606387336', description: 'Mobile number' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "mobileNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'home for personal use', description: 'Interest type' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "interest", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null, description: 'Timezone', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "timezone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Call transcript details...', description: 'Call transcript', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callTranscript", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Call summary details...', description: 'Call summary', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callSummary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2025-03-12T10:25:17.759Z', description: 'Call start time' }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], HistoryDto.prototype, "callStartTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2025-03-12T10:28:26.020Z', description: 'Call end time' }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], HistoryDto.prototype, "callEndTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '188261', description: 'Call duration in milliseconds' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null, description: 'Call route', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callRoute", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null, description: 'Call purpose', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callPurpose", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'customer-ended-call', description: 'Call end reason' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callEndReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '0.2028', description: 'Cost of the call' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callCost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null, description: 'Booked status', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "bookedStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null, description: 'Confirmed status', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "confirmedStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null, description: 'Additional questions', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "additionalQuestions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'https://example.com/recording.mp3', description: 'Recording URL' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "recordingUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'personal use', description: 'Preferred project' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "preferredProject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Jumeirah Village Circle or Dubai Marina', description: 'Preferred location' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "preferredLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2 bedroom', description: 'Preferred unit type' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "preferredUnitType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'off plan projects', description: 'Project type' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "projectType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'home for personal use', description: 'Investment type' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "investmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '1000000 US dollar', description: 'Budget amount' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "budget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Recent contact flag' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HistoryDto.prototype, "recentContact", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'lisa', description: 'Agent handling the call' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "agent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Neutral',
        description: 'Emotion expressed by the client. Must be one of: Positive, Neutral, Slightly Positive, Slightly Negative, or Negative.',
        enum: Emotion
    }),
    (0, class_validator_1.IsEnum)(Emotion),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "emotions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if there was a broken promise' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "brokenPromise", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'en', description: 'Call back language' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callBackLanguage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if a call back was requested' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "callBackRequest", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the claim is paid awaiting proof of payment (POP)' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "claimedPaidAwaitingPOP", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller should not be contacted' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "doNotCall", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller is following a payment plan' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "followingPaymentPlan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller is fully paid' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "fullyPaid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller is fully paid by PDC' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "fullyPaidByPDC", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the contact details are incorrect' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "incorrectContactDetails", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the call is related to a mortgage' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "mortgage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller is not responding' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "notResponding", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller is not responding after an SOA was sent' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "notRespondingSOASent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller is not willing to pay' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "notWillingToPay", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if a proof of payment (POP) has been raised' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "popRaised", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if there was a promise to pay' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "promiseToPay", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if there was a partial promise to pay' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "promiseToPayPartial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller refused to pay' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "refuseToPay", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if a third party is involved' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "thirdParty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Indicates if the caller is willing to pay' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "willingToPay", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "Response", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "Channel", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "Notes", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", String)
], HistoryDto.prototype, "GuestRequest", void 0);
//# sourceMappingURL=history.dto.js.map