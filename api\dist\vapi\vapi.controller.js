"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VapiController = void 0;
const common_1 = require("@nestjs/common");
const vapi_service_1 = require("./vapi.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const users_service_1 = require("../users/users.service");
let VapiController = class VapiController {
    constructor(vapiService, usersService) {
        this.vapiService = vapiService;
        this.usersService = usersService;
    }
    async callContacts(payload, req, res) {
        try {
            const { contacts, agentId, region } = payload;
            const userId = req.user.userId;
            const hasSufficientCredits = await this.usersService.hasSufficientCredits(userId, 1);
            if (!hasSufficientCredits) {
                return res.status(common_1.HttpStatus.PAYMENT_REQUIRED).json({
                    error: "Insufficient credits. Please add funds to your account.",
                });
            }
            const results = await this.vapiService.callContacts(contacts, agentId, region, userId);
            await this.usersService.deductCredits(userId, 1);
            return res.status(common_1.HttpStatus.OK).json({
                message: "Calls processed",
                results,
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: error.message,
            });
        }
    }
    async vapiWebhook(body, res) {
        try {
            const result = await this.vapiService.processWebhook(body);
            return res.status(common_1.HttpStatus.OK).json(result);
        }
        catch (error) {
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: "Failed to process webhook and send data to Callback API",
            });
        }
    }
};
exports.VapiController = VapiController;
__decorate([
    (0, common_1.Post)("call-contacts"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], VapiController.prototype, "callContacts", null);
__decorate([
    (0, common_1.Post)("webhook"),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], VapiController.prototype, "vapiWebhook", null);
exports.VapiController = VapiController = __decorate([
    (0, common_1.Controller)("vapi"),
    __metadata("design:paramtypes", [vapi_service_1.VapiService,
        users_service_1.UsersService])
], VapiController);
//# sourceMappingURL=vapi.controller.js.map