// src/conversations/conversations.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AiService } from '../ai/ai.service';
import { ConversationDocument } from './interfaces/conversation.interface';

@Injectable()
export class ConversationsService {
  constructor(
    @InjectModel('Conversation')
    private readonly conversationModel: Model<ConversationDocument>,
    private readonly aiService: AiService,
  ) {}

  async startConversation(agentId: string, userId: string, type: 'chat' | 'call') {
    const conversation = new this.conversationModel({
      agentId,
      userId,
      type,
      status: 'active',
    });
    return conversation.save();
  }

  async getConversationById(id: string) {
    const conversation = await this.conversationModel.findById(id).exec();
    if (!conversation) throw new NotFoundException('Conversation not found');
    return conversation;
  }

  async addUserMessage(conversationId: string, message: string) {
    // Get conversation document
    const conversation = await this.getConversationById(conversationId);
    if (conversation.status === 'ended') {
      throw new Error('Cannot add messages to an ended conversation');
    }
  
    // Append the new user message
    conversation.messages.push({
      role: 'user',
      content: message,
      timestamp: new Date(),
    });
    // Save the user message concurrently (fire-and-forget)
    const userSavePromise = conversation.save();
  
    // Build conversation history excluding the newly added user message.
    const historyExcludingCurrent = conversation.messages.slice(0, -1).map(m => ({
      role: m.role,
      content: m.content,
    }));
    // Limit history to only the last 3 messages.
    const limitedHistory = historyExcludingCurrent.slice(-3);
  
    // Get AI response using our new wrapper method.
    const agentReply = await this.aiService.getChatResponse(
      conversation.agentId.toString(),
      message,
      limitedHistory,
    );
  
    // Append the assistant's response
    conversation.messages.push({
      role: 'assistant',
      content: agentReply,
      timestamp: new Date(),
    });
    // Save both changes concurrently.
    await Promise.all([userSavePromise, conversation.save()]);
  
    return agentReply;
  }

  async endConversation(conversationId: string) {
    const conversation = await this.getConversationById(conversationId);
    conversation.status = 'ended';
    conversation.endedAt = new Date();
    return conversation.save();
  }
}
