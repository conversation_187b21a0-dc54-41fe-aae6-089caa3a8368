"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { APIKeyInput } from "../APIKeyInput";

export function APISettings() {
  const [openai<PERSON>ey, setOpenaiKey] = useState("");
  const [claude<PERSON><PERSON>, set<PERSON>laude<PERSON><PERSON>] = useState("");
  const [gemini<PERSON>ey, setGeminiKey] = useState("");
  const [deepseek<PERSON>ey, setDeepseekKey] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const testOpenAI = async () => {
    // Implement actual API test
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return true;
  };

  const testClaude = async () => {
    // Implement actual API test
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return true;
  };

  const testGemini = async () => {
    // Implement actual API test
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return true;
  };

  const testDeepseek = async () => {
    // Implement actual API test
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return false; // Simulate a failure for demonstration
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);
    // Implement API key saving
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSaving(false);
    // You could show a success toast here
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Language Model APIs</h3>
        <div className="grid gap-6">
          <APIKeyInput
            label="OpenAI API Key"
            description="Required for GPT-3.5, GPT-4, and DALL-E models"
            value={openaiKey}
            onChange={setOpenaiKey}
            onTest={testOpenAI}
            placeholder="sk-..."
          />

          <APIKeyInput
            label="Anthropic API Key"
            description="Required for Claude models"
            value={claudeKey}
            onChange={setClaudeKey}
            onTest={testClaude}
            placeholder="sk-ant-..."
          />

          <APIKeyInput
            label="Google API Key"
            description="Required for Gemini models"
            value={geminiKey}
            onChange={setGeminiKey}
            onTest={testGemini}
          />

          <APIKeyInput
            label="DeepSeek API Key"
            description="Required for DeepSeek models"
            value={deepseekKey}
            onChange={setDeepseekKey}
            onTest={testDeepseek}
          />
        </div>
      </div>

      <div className="flex justify-end">
        <Button 
          onClick={handleSaveChanges}
          disabled={isSaving}
          className="bg-primary text-primary-foreground hover:bg-primary/90"
        >
          {isSaving ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </div>
  );
}