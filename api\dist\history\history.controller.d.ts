import { HistoryService } from './history.service';
export declare class HistoryController {
    private readonly historyService;
    constructor(historyService: HistoryService);
    findAll(page?: string, limit?: string, search?: string, filterType?: 'all' | 'name' | 'agent'): Promise<import("./interfaces/history.interface").History[]>;
    findById(id: string): Promise<import("./interfaces/history.interface").History>;
    delete(id: string): Promise<import("./interfaces/history.interface").History>;
}
