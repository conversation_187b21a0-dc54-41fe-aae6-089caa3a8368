"use client";

import { But<PERSON> } from "@/components/ui/button";
import { PenLine, Plus } from "lucide-react";

type EmptyStateProps = {
  type: "paragraph" | "link" | "file" | "spreadsheet";
  onAdd: () => void;
};

export function EmptyState({ type, onAdd }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center h-[calc(100vh-20rem)] text-center">
      <div className="h-20 w-20 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
        <PenLine className="h-10 w-10 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium mb-2">No content yet</h3>
      <p className="text-gray-500 dark:text-gray-400 mb-4 max-w-md">
        Start adding {type === "link" ? "web links" : type + "s"} to build your knowledge base
      </p>
      <Button
        variant="outline"
        onClick={onAdd}
      >
        <Plus className="h-5 w-5 mr-2" />
        Add {type === "link" ? "Link" : type.charAt(0).toUpperCase() + type.slice(1)}
      </Button>
    </div>
  );
}