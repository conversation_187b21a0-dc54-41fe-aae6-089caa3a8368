// src/conversations/conversations.controller.ts

import { <PERSON>, Post, Body, Param } from '@nestjs/common';
import { ConversationsService } from './conversations.service';

@Controller('conversations')
export class ConversationsController {
  constructor(private readonly conversationsService: ConversationsService) {}

  @Post('start')
  async start(@Body() body: { agentId: string; userId: string; type: 'chat' | 'call' }) {
    return this.conversationsService.startConversation(body.agentId, body.userId, body.type);
  }

  @Post(':conversationId/message')
  async sendMessage(
    @Param('conversationId') conversationId: string,
    @Body() body: { message: string },
  ) {
    return this.conversationsService.addUserMessage(conversationId, body.message);
  }

  @Post(':conversationId/end')
  async end(@Param('conversationId') conversationId: string) {
    return this.conversationsService.endConversation(conversationId);
  }
}
