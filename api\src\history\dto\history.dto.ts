import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsEnum } from 'class-validator';

export enum Emotion {
  Positive = 'Positive',
  Neutral = 'Neutral',
  SlightlyPositive = 'Slightly Positive',
  SlightlyNegative = 'Slightly Negative',
  Negative = 'Negative',
}

export class HistoryDto {
  @ApiProperty({ example: 'marouane', description: 'Full name of the caller' })
  @IsString()
  @IsNotEmpty()
  fullName: string;

  @ApiProperty({ example: '+212606387336', description: 'Mobile number' })
  @IsString()
  @IsNotEmpty()
  mobileNumber: string;

  @ApiProperty({ example: 'home for personal use', description: 'Interest type' })
  @IsString()
  @IsNotEmpty()
  interest: string;

  @ApiProperty({ example: null, description: 'Timezone', required: false })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({ example: 'Call transcript details...', description: 'Call transcript', required: false })
  @IsOptional()
  @IsString()
  callTranscript?: string;

  @ApiProperty({ example: 'Call summary details...', description: 'Call summary', required: false })
  @IsOptional()
  @IsString()
  callSummary?: string;

  @ApiProperty({ example: '2025-03-12T10:25:17.759Z', description: 'Call start time' })
  @IsNotEmpty()
  callStartTime: Date;

  @ApiProperty({ example: '2025-03-12T10:28:26.020Z', description: 'Call end time' })
  @IsNotEmpty()
  callEndTime: Date;

  @ApiProperty({ example: '188261', description: 'Call duration in milliseconds' })
  @IsString()
  @IsNotEmpty()
  callDuration: string;

  @ApiProperty({ example: null, description: 'Call route', required: false })
  @IsOptional()
  @IsString()
  callRoute?: string;

  @ApiProperty({ example: null, description: 'Call purpose', required: false })
  @IsOptional()
  @IsString()
  callPurpose?: string;

  @ApiProperty({ example: 'customer-ended-call', description: 'Call end reason' })
  @IsString()
  @IsNotEmpty()
  callEndReason: string;

  @ApiProperty({ example: '0.2028', description: 'Cost of the call' })
  @IsString()
  @IsNotEmpty()
  callCost: string;

  @ApiProperty({ example: null, description: 'Booked status', required: false })
  @IsOptional()
  @IsString()
  bookedStatus?: string;

  @ApiProperty({ example: null, description: 'Confirmed status', required: false })
  @IsOptional()
  @IsString()
  confirmedStatus?: string;

  @ApiProperty({ example: null, description: 'Additional questions', required: false })
  @IsOptional()
  @IsString()
  additionalQuestions?: string;

  @ApiProperty({ example: 'https://example.com/recording.mp3', description: 'Recording URL' })
  @IsString()
  @IsNotEmpty()
  recordingUrl: string;

  @ApiProperty({ example: 'personal use', description: 'Preferred project' })
  @IsString()
  @IsNotEmpty()
  preferredProject: string;

  @ApiProperty({ example: 'Jumeirah Village Circle or Dubai Marina', description: 'Preferred location' })
  @IsString()
  @IsNotEmpty()
  preferredLocation: string;

  @ApiProperty({ example: '2 bedroom', description: 'Preferred unit type' })
  @IsString()
  @IsNotEmpty()
  preferredUnitType: string;

  @ApiProperty({ example: 'off plan projects', description: 'Project type' })
  @IsString()
  @IsNotEmpty()
  projectType: string;

  @ApiProperty({ example: 'home for personal use', description: 'Investment type' })
  @IsString()
  @IsNotEmpty()
  investmentType: string;

  @ApiProperty({ example: '1000000 US dollar', description: 'Budget amount' })
  @IsString()
  @IsNotEmpty()
  budget: string;

  @ApiProperty({ example: true, description: 'Recent contact flag' })
  @IsBoolean()
  recentContact: boolean;

  @ApiProperty({ example: 'lisa', description: 'Agent handling the call' })
  @IsString()
  @IsNotEmpty()
  agent: string;

  @ApiProperty({ 
    example: 'Neutral', 
    description: 'Emotion expressed by the client. Must be one of: Positive, Neutral, Slightly Positive, Slightly Negative, or Negative.', 
    enum: Emotion 
  })
  @IsEnum(Emotion)
  @IsNotEmpty()
  emotions: Emotion;

  // New fields
  @ApiProperty({ example: true, description: 'Indicates if there was a broken promise' })
  @IsBoolean()
  brokenPromise: String;

  @ApiProperty({ example: 'en', description: 'Call back language' })
  @IsString()
  @IsNotEmpty()
  callBackLanguage: String;

  @ApiProperty({ example: true, description: 'Indicates if a call back was requested' })
  @IsBoolean()
  callBackRequest: String;

  @ApiProperty({ example: true, description: 'Indicates if the claim is paid awaiting proof of payment (POP)' })
  @IsBoolean()
  claimedPaidAwaitingPOP: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller should not be contacted' })
  @IsBoolean()
  doNotCall: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller is following a payment plan' })
  @IsBoolean()
  followingPaymentPlan: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller is fully paid' })
  @IsBoolean()
  fullyPaid: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller is fully paid by PDC' })
  @IsBoolean()
  fullyPaidByPDC: String;

  @ApiProperty({ example: true, description: 'Indicates if the contact details are incorrect' })
  @IsBoolean()
  incorrectContactDetails: String;

  @ApiProperty({ example: true, description: 'Indicates if the call is related to a mortgage' })
  @IsBoolean()
  mortgage: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller is not responding' })
  @IsBoolean()
  notResponding: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller is not responding after an SOA was sent' })
  @IsBoolean()
  notRespondingSOASent: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller is not willing to pay' })
  @IsBoolean()
  notWillingToPay: String;

  @ApiProperty({ example: true, description: 'Indicates if a proof of payment (POP) has been raised' })
  @IsBoolean()
  popRaised: String;

  @ApiProperty({ example: true, description: 'Indicates if there was a promise to pay' })
  @IsBoolean()
  promiseToPay: String;

  @ApiProperty({ example: true, description: 'Indicates if there was a partial promise to pay' })
  @IsBoolean()
  promiseToPayPartial: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller refused to pay' })
  @IsBoolean()
  refuseToPay: String;

  @ApiProperty({ example: true, description: 'Indicates if a third party is involved' })
  @IsBoolean()
  thirdParty: String;

  @ApiProperty({ example: true, description: 'Indicates if the caller is willing to pay' })
  @IsBoolean()
  willingToPay: String;

  @IsBoolean()
  Response: String;

  @IsBoolean()
  Channel: String;

  @IsBoolean()
  Notes: String;

  @IsBoolean()
  GuestRequest: String;
}
