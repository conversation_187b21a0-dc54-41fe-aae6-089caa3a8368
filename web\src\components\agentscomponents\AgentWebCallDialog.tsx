/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Agent } from "@/types/agent.types";
import { toast } from 'sonner';
import { Loader2, Mic, MicOff } from 'lucide-react';
import Vapi from "@vapi-ai/web";

interface WebCallDialogProps {
  isOpen: boolean;
  onClose: () => void;
  agent: Agent | null;
}


export function AgentWebCallDialog({ isOpen, onClose, agent }: WebCallDialogProps) {
  const [isCallActive, setIsCallActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [lastMessage, setLastMessage] = useState<string>('');
  const [vapi, setVapi] = useState<Vapi | null>(null);

  // Initialize Vapi instance
  useEffect(() => {
    if (isOpen && !vapi) {
      const vapiInstance = new Vapi({
        apiKey: process.env.NEXT_PUBLIC_VAPI_API_KEY || '',
        debug: true,
        audioConfig: {
          sampleRate: 16000,
          channelCount: 1,
        },
        cors: {
        allowedOrigins: ['http://localhost:3000'] // Add CORS configuration
      }
      });

      // Set up event handlers
      vapiInstance.on('call-start', () => {
        console.log('Call started successfully');
        setIsCallActive(true);
        setIsLoading(false);
        toast.success('Call started');
      });

      vapiInstance.on('call-end', () => {
        setIsCallActive(false);
        toast.info('Call ended');
      });

      vapiInstance.on('message', (message: { content: string }) => {
        setLastMessage(message.content);
      });

      vapiInstance.on('speech-start', () => {
        setIsSpeaking(true);
      });

      vapiInstance.on('speech-end', () => {
        setIsSpeaking(false);
      });

      vapiInstance.on('error', (error: any) => {
        console.error('VAPI error details:', error);
        setError(error.message || 'An unexpected error occurred');
        setIsLoading(false);
        toast.error(`Call error: ${error.message}`);
      });

      setVapi(vapiInstance);
    }

    // Cleanup on dialog close
    return () => {
      if (vapi) {
      try {
        vapi.stop();
      } catch (error) {
        console.error('Error stopping call:', error);
      }
      setVapi(null);
    }
    };
  }, [isOpen, vapi]);

  const handleToggleCall = async () => {
  if (!agent || !vapi) return;

  if (isCallActive) {
    vapi.stop();
    setIsCallActive(false);
  } else {
    setIsLoading(true);
    setError(null);

    try {
      // Check if we have microphone permission first
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // Clean up the test stream

      // Start the call with more detailed configuration
      await vapi.start({
        assistantId: agent.id,
        audioConfig: {
          sampleRate: 16000,
          channelCount: 1,
        },
        transcriber: {
          language: agent.transcriber?.language || 'en-US',
        },
      });

    } catch (error: any) {
      console.error('Web call error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      
      if (error.name === 'NotAllowedError') {
        setError('Microphone permission denied. Please enable microphone access.');
      } else if (!navigator.onLine) {
        setError('No internet connection. Please check your network.');
      } else {
        setError(error.message || 'Failed to start web call. Please try again.');
      }
      
      toast.error('Failed to start web call');
      setIsLoading(false);
    }
  }
};

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        if (isCallActive) {
          vapi?.stop();
        }
        setError(null);
        setLastMessage('');
      }
      onClose();
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Web Call with {agent?.name}</DialogTitle>
          <DialogDescription>
            {isCallActive ? 'Call in progress' : 'Start a web call with your assistant'}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {!isCallActive && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Make sure your microphone is connected and you have granted browser permissions.
            </p>
          )}

          {isCallActive && lastMessage && (
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
              <p className="text-sm font-medium mb-1">Last Response:</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">{lastMessage}</p>
            </div>
          )}

          {error && (
            <div className="mb-4 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
              {error}
            </div>
          )}

          <div className="mt-6 flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleToggleCall}
              disabled={isLoading}
              className={`${
                isCallActive 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-black hover:bg-gray-800 dark:bg-white dark:text-black'
              } text-white`}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Starting...
                </>
              ) : isCallActive ? (
                <>
                  {isSpeaking ? <Mic className="mr-2 h-4 w-4" /> : <MicOff className="mr-2 h-4 w-4" />}
                  End Call
                </>
              ) : (
                'Start Call'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}