import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { PhoneNumberService } from './phone-number.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';

@ApiTags('Phone Numbers')
@Controller('phone-numbers')
export class PhoneNumberController {
  constructor(private readonly phoneNumberService: PhoneNumberService) {}

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all phone numbers (syncs with VAPI)' })
  @ApiResponse({ status: 200, description: 'Returns all phone numbers' })
  findAll() {
    return this.phoneNumberService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a phone number by ID' })
  @ApiResponse({ status: 200, description: 'Returns a phone number' })
  @ApiResponse({ status: 404, description: 'Phone number not found' })
  findById(@Param('id') id: string) {
    return this.phoneNumberService.findById(id);
  }
}