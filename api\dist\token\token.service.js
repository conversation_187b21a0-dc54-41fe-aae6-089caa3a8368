"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = __importDefault(require("axios"));
let TokenService = class TokenService {
    constructor(configService) {
        this.configService = configService;
        this.TWILIO_ACCOUNT_SID = this.configService.get('TWILIO_ACCOUNT_SID');
        this.TWILIO_AUTH_TOKEN = this.configService.get('TWILIO_AUTH_TOKEN');
    }
    async getTwilioCredits() {
        try {
            const response = await axios_1.default.get(`https://api.twilio.com/2010-04-01/Accounts/${this.TWILIO_ACCOUNT_SID}/Balance.json`, {
                auth: {
                    username: this.TWILIO_ACCOUNT_SID,
                    password: this.TWILIO_AUTH_TOKEN,
                },
            });
            return {
                balance: response.data.balance,
                currency: response.data.currency,
            };
        }
        catch (error) {
            throw new Error(`Failed to fetch Twilio credits: ${error.message}`);
        }
    }
};
exports.TokenService = TokenService;
exports.TokenService = TokenService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], TokenService);
//# sourceMappingURL=token.service.js.map