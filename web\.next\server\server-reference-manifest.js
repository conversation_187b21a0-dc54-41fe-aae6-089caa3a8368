self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"006e6a7598517a64a0222bb89b73ddd257764d50ac\": {\n      \"workers\": {\n        \"app/(workspace)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(workspace)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"006f4095f78e30eaa701b85c396d30de187fe7f260\": {\n      \"workers\": {\n        \"app/(workspace)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(workspace)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"00c349c6c9b93ac440dea00915d65f1b054d5a71a5\": {\n      \"workers\": {\n        \"app/(workspace)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(workspace)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"408d4ffcda2adfadc68aafde596a5102a7ebb51409\": {\n      \"workers\": {\n        \"app/(workspace)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(workspace)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40ae9d6f5ad7238997dd0ef7497e335399ead00b2f\": {\n      \"workers\": {\n        \"app/(workspace)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(workspace)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/(auth)/actions/auth.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(workspace)/dashboard/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"tEM6TKGLe8j5cQrCriCePVJhjSUUbfs7lZlYOl4PwKc=\"\n}"