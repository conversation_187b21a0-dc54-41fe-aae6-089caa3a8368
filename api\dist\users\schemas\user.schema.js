"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSchema = void 0;
const mongoose_1 = require("mongoose");
const role_enum_1 = require("../enums/role.enum");
exports.UserSchema = new mongoose_1.Schema({
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    fullName: { type: String, required: true },
    role: { type: String, enum: role_enum_1.ROLES, default: 'user' },
    isApproved: { type: Boolean, default: false },
    organizationId: { type: mongoose_1.Types.ObjectId, ref: 'Organization' },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
//# sourceMappingURL=user.schema.js.map