export declare enum Emotion {
    Positive = "Positive",
    Neutral = "Neutral",
    SlightlyPositive = "Slightly Positive",
    SlightlyNegative = "Slightly Negative",
    Negative = "Negative"
}
export declare class HistoryDto {
    fullName: string;
    mobileNumber: string;
    interest: string;
    timezone?: string;
    callTranscript?: string;
    callSummary?: string;
    callStartTime: Date;
    callEndTime: Date;
    callDuration: string;
    callRoute?: string;
    callPurpose?: string;
    callEndReason: string;
    callCost: string;
    bookedStatus?: string;
    confirmedStatus?: string;
    additionalQuestions?: string;
    recordingUrl: string;
    preferredProject: string;
    preferredLocation: string;
    preferredUnitType: string;
    projectType: string;
    investmentType: string;
    budget: string;
    recentContact: boolean;
    agent: string;
    emotions: Emotion;
    brokenPromise: String;
    callBackLanguage: String;
    callBackRequest: String;
    claimedPaidAwaitingPOP: String;
    doNotCall: String;
    followingPaymentPlan: String;
    fullyPaid: String;
    fullyPaidByPDC: String;
    incorrectContactDetails: String;
    mortgage: String;
    notResponding: String;
    notRespondingSOASent: String;
    notWillingToPay: String;
    popRaised: String;
    promiseToPay: String;
    promiseToPayPartial: String;
    refuseToPay: String;
    thirdParty: String;
    willingToPay: String;
    Response: String;
    Channel: String;
    Notes: String;
    GuestRequest: String;
}
