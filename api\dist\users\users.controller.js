"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const swagger_1 = require("@nestjs/swagger");
const register_user_dto_1 = require("./dto/register-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const auto_recharge_service_1 = require("../credit/auto-recharge.service");
class AutoRechargeSettingsDto {
}
let UsersController = class UsersController {
    constructor(usersService, autoRechargeService) {
        this.usersService = usersService;
        this.autoRechargeService = autoRechargeService;
    }
    async register(userDto) {
        await this.usersService.create(userDto);
        return 'User registered successfully! Awaiting admin approval.';
    }
    async approveUser(id) {
        await this.usersService.approveUser(id);
        return { message: 'User approved successfully' };
    }
    async getAllUsers() {
        return this.usersService.findAll();
    }
    async getUserById(id) {
        return this.usersService.findById(id);
    }
    async updateUser(id, updateDto) {
        await this.usersService.updateUser(id, updateDto);
        return { message: 'User updated successfully' };
    }
    async deleteUser(id) {
        await this.usersService.deleteUser(id);
        return { message: 'User deleted successfully' };
    }
    async getUserCredits(req) {
        const userId = req.user.userId;
        const user = await this.usersService.findById(userId);
        if (!user.organizationId) {
            console.error(`User ${userId} has no organization assigned`);
            return {
                freeCreditsRemaining: 0,
                paidCredits: 0,
                totalAvailable: 0,
                usingFreeCredits: false,
                freeMinutesRemaining: 0,
                paidMinutes: 0,
                totalMinutesAvailable: 0,
                callPricePerMinute: 0.1,
                monthlyResetDate: 1,
                monthlyAllowance: 0,
                minimumCreditsThreshold: 1.0,
                credits: 0,
                minutes: 0
            };
        }
        try {
            const organizationsService = this.usersService.getOrganizationsService();
            const availableCredits = await organizationsService.getAvailableCredits(user.organizationId.toString());
            const organization = await organizationsService.findOne(user.organizationId.toString());
            const callPricePerMinute = organization.callPricePerMinute;
            const freeMinutesRemaining = availableCredits.freeCreditsRemaining;
            const paidMinutes = callPricePerMinute > 0 ? availableCredits.paidCredits / callPricePerMinute : 0;
            const totalMinutesAvailable = freeMinutesRemaining + paidMinutes;
            return {
                freeCreditsRemaining: availableCredits.freeCreditsRemaining,
                paidCredits: availableCredits.paidCredits,
                totalAvailable: availableCredits.totalAvailable,
                usingFreeCredits: availableCredits.usingFreeCredits,
                freeMinutesRemaining,
                paidMinutes,
                totalMinutesAvailable,
                callPricePerMinute,
                monthlyResetDate: organization.monthlyResetDate || 1,
                monthlyAllowance: organization.monthlyMinutesAllowance || 0,
                minimumCreditsThreshold: organization.minimumCreditsThreshold || 1.0,
                credits: availableCredits.totalAvailable,
                minutes: totalMinutesAvailable
            };
        }
        catch (error) {
            console.error('Error getting organization credits:', error);
            return {
                freeCreditsRemaining: 0,
                paidCredits: 0,
                totalAvailable: 0,
                usingFreeCredits: false,
                freeMinutesRemaining: 0,
                paidMinutes: 0,
                totalMinutesAvailable: 0,
                callPricePerMinute: 0.1,
                monthlyResetDate: 1,
                monthlyAllowance: 0,
                minimumCreditsThreshold: 1.0,
                credits: 0,
                minutes: 0
            };
        }
    }
    async getAutoRechargeSettings(req) {
        try {
            const userId = req.user.userId;
            const organization = await this.usersService.getUserOrganization(userId);
            if (!organization) {
                throw new common_1.NotFoundException('User does not belong to an organization');
            }
            return {
                autoRechargeEnabled: organization.autoRechargeEnabled,
                autoRechargeThreshold: organization.autoRechargeThreshold,
                autoRechargeAmount: organization.autoRechargeAmount
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to get auto-recharge settings', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateAutoRechargeSettings(req, updateDto) {
        try {
            const userId = req.user.userId;
            const organization = await this.usersService.getUserOrganization(userId);
            if (!organization) {
                throw new common_1.NotFoundException('User does not belong to an organization');
            }
            const organizationsService = this.usersService.getOrganizationsService();
            const updatedOrganization = await organizationsService.updateBilling(organization._id.toString(), {
                autoRechargeEnabled: updateDto.autoRechargeEnabled,
                autoRechargeThreshold: updateDto.autoRechargeThreshold,
                autoRechargeAmount: updateDto.autoRechargeAmount
            });
            return {
                message: 'Auto-recharge settings updated successfully',
                settings: {
                    autoRechargeEnabled: updatedOrganization.autoRechargeEnabled,
                    autoRechargeThreshold: updatedOrganization.autoRechargeThreshold,
                    autoRechargeAmount: updatedOrganization.autoRechargeAmount
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to update auto-recharge settings', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async testAutoRecharge(req) {
        try {
            const userId = req.user.userId;
            const organization = await this.usersService.getUserOrganization(userId);
            if (!organization) {
                throw new common_1.NotFoundException('User does not belong to an organization');
            }
            const currentSettings = {
                autoRechargeEnabled: organization.autoRechargeEnabled,
                autoRechargeThreshold: organization.autoRechargeThreshold,
                autoRechargeAmount: organization.autoRechargeAmount,
                currentCredits: organization.credits
            };
            const autoRechargeResult = await this.autoRechargeService.checkAndProcessAutoRecharge(userId);
            const updatedOrganization = await this.usersService.getUserOrganization(userId);
            return {
                message: autoRechargeResult ? 'Auto-recharge test successful' : 'Auto-recharge test did not trigger a payment',
                beforeTest: currentSettings,
                afterTest: {
                    autoRechargeEnabled: updatedOrganization.autoRechargeEnabled,
                    autoRechargeThreshold: updatedOrganization.autoRechargeThreshold,
                    autoRechargeAmount: updatedOrganization.autoRechargeAmount,
                    currentCredits: updatedOrganization.credits
                },
                autoRechargeTriggered: autoRechargeResult
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to test auto-recharge', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'Register a new user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'User successfully registered' }),
    (0, swagger_1.ApiBody)({ type: register_user_dto_1.RegisterUserDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "register", null);
__decorate([
    (0, common_1.Patch)('approve/:id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Approve user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User approved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'User ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "approveUser", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Get all users' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns all users' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getAllUsers", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Get user by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns a user by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'User ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getUserById", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Update user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User updated successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'User ID' }),
    (0, swagger_1.ApiBody)({ type: update_user_dto_1.UpdateUserDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Delete user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User deleted successfully' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'User ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "deleteUser", null);
__decorate([
    (0, common_1.Get)('me/credits'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user credits' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the current user credits in both dollars and minutes',
        schema: {
            type: 'object',
            properties: {
                credits: { type: 'number' },
                minutes: { type: 'number' },
                callPricePerMinute: { type: 'number' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getUserCredits", null);
__decorate([
    (0, common_1.Get)('me/auto-recharge'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get auto-recharge settings for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the auto-recharge settings',
        schema: {
            type: 'object',
            properties: {
                autoRechargeEnabled: { type: 'boolean' },
                autoRechargeThreshold: { type: 'number' },
                autoRechargeAmount: { type: 'number' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getAutoRechargeSettings", null);
__decorate([
    (0, common_1.Patch)('me/auto-recharge'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update auto-recharge settings for the user\'s organization' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Auto-recharge settings updated successfully' }),
    (0, swagger_1.ApiBody)({ type: AutoRechargeSettingsDto }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, AutoRechargeSettingsDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateAutoRechargeSettings", null);
__decorate([
    (0, common_1.Post)('me/test-auto-recharge'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Test auto-recharge functionality' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Auto-recharge test results' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "testAutoRecharge", null);
exports.UsersController = UsersController = __decorate([
    (0, common_1.Controller)('users'),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => auto_recharge_service_1.AutoRechargeService))),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        auto_recharge_service_1.AutoRechargeService])
], UsersController);
//# sourceMappingURL=users.controller.js.map