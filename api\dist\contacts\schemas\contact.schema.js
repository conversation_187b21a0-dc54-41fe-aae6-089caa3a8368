"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactSchema = void 0;
const mongoose_1 = require("mongoose");
exports.ContactSchema = new mongoose_1.Schema({
    contactName: { type: String, required: true },
    phoneNumber: { type: String, required: true },
    customerId: { type: String },
    lastCall: { type: Date },
    campaigns: [{ type: mongoose_1.Types.ObjectId, ref: "Campaign", }],
    campaignNames: [{ type: String }],
    region: { type: String },
    updatedAt: { type: Date, default: Date.now },
    projectName: { type: String },
    unitNumber: { type: String },
    totalPayableAmount: { type: Number },
    pendingPayableAmount: { type: Number },
    dueDate: { type: Date },
    totalInstallments: { type: Number },
    paymentType: { type: String },
    pendingInstallments: { type: Number },
    lastPaymentDate: { type: Date },
    lastPaymentAmount: { type: Number },
    lastPaymentType: { type: String },
    collectionBucket: { type: String },
    unitPrice: { type: Number },
    paidAmtIncluding: { type: Number },
    unansweredCalls: {
        type: Number,
        default: 0,
        min: [0, "Unanswered calls cannot be negative"]
    },
    eventDate: { type: String },
    eventLocation: { type: String },
    eventTime: { type: String },
    nameOfRegistrant: { type: String },
    addedBy: { type: String },
    createdAt: {
        type: Date,
        default: Date.now,
        immutable: true
    },
    source: {
        type: String,
        enum: ['manual', 'file Upload', 'CRM', '-'],
        default: '-'
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    id: false,
});
exports.ContactSchema.pre("save", function (next) {
    this.updatedAt = new Date();
    next();
});
exports.ContactSchema.statics.getTotalCount = async function () {
    return await this.countDocuments();
};
exports.ContactSchema.virtual('totalContacts').get(function () {
    return this._totalCount;
});
exports.ContactSchema.virtual('filteredContacts').get(function () {
    return this._filteredCount;
});
exports.ContactSchema.index({ contactName: 1, phoneNumber: 1 }, { unique: true });
//# sourceMappingURL=contact.schema.js.map