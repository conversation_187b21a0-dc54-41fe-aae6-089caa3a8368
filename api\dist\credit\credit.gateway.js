"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
let CreditGateway = class CreditGateway {
    constructor() {
        this.logger = new common_1.Logger('CreditGateway');
        this.userSocketMap = new Map();
    }
    afterInit() {
        this.logger.log('Credit WebSocket Gateway initialized');
    }
    handleConnection(client) {
        this.logger.log(`Client connected: ${client.id}`);
    }
    handleDisconnect(client) {
        this.logger.log(`Client disconnected: ${client.id}`);
        for (const [userId, sockets] of this.userSocketMap.entries()) {
            const index = sockets.indexOf(client.id);
            if (index !== -1) {
                sockets.splice(index, 1);
                if (sockets.length === 0) {
                    this.userSocketMap.delete(userId);
                }
                else {
                    this.userSocketMap.set(userId, sockets);
                }
                break;
            }
        }
    }
    registerUserSocket(userId, socketId) {
        if (!this.userSocketMap.has(userId)) {
            this.userSocketMap.set(userId, []);
        }
        const sockets = this.userSocketMap.get(userId);
        if (!sockets.includes(socketId)) {
            sockets.push(socketId);
            this.userSocketMap.set(userId, sockets);
        }
        this.logger.log(`User ${userId} registered with socket ${socketId} via API`);
    }
    handleRegisterUser(client, payload) {
        const { userId } = payload;
        if (!userId) {
            this.logger.error('No userId provided for socket registration');
            return { success: false, message: 'No userId provided' };
        }
        if (!this.userSocketMap.has(userId)) {
            this.userSocketMap.set(userId, []);
        }
        const sockets = this.userSocketMap.get(userId);
        if (!sockets.includes(client.id)) {
            sockets.push(client.id);
            this.userSocketMap.set(userId, sockets);
        }
        this.logger.log(`User ${userId} registered with socket ${client.id} via WebSocket`);
        return { success: true, message: 'Socket registered successfully' };
    }
    handlePing(client, payload) {
        this.logger.debug(`Received ping from client ${client.id}`);
        return { success: true, timestamp: new Date().toISOString() };
    }
    sendCreditUpdate(userId, credits) {
        const sockets = this.userSocketMap.get(userId);
        if (sockets && sockets.length > 0) {
            this.logger.log(`Sending credit update to user ${userId}: ${credits}`);
            sockets.forEach(socketId => {
                this.server.to(socketId).emit('creditUpdate', { credits });
            });
            return true;
        }
        return false;
    }
    sendOrganizationCreditUpdate(organizationId, credits) {
        if (!organizationId) {
            this.logger.error('Cannot send organization credit update: organizationId is empty');
            return;
        }
        this.logger.log(`Broadcasting credit update to organization ${organizationId}: ${credits}`);
        const orgId = organizationId.toString();
        this.server.emit('organizationCreditUpdate', {
            organizationId: orgId,
            credits: Number(credits.toFixed(2))
        });
        this.logger.log(`Sent organization credit update: ${JSON.stringify({ organizationId: orgId, credits })}`);
    }
};
exports.CreditGateway = CreditGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], CreditGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('registerUser'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", void 0)
], CreditGateway.prototype, "handleRegisterUser", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('ping'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", void 0)
], CreditGateway.prototype, "handlePing", null);
exports.CreditGateway = CreditGateway = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: '*',
        },
        namespace: 'credits',
    })
], CreditGateway);
//# sourceMappingURL=credit.gateway.js.map