"use client";

import { authFetch } from "./authFetch";

export type UserInfo = {
  fullName: string;
  userId: string;
  email: string;
  role: string;
};

const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

// Client-side version of getCurrentUser
export async function getCurrentUser(): Promise<{
  success: boolean;
  user?: UserInfo;
  error?: string;
}> {
  try {
    // Use the authFetch utility instead of regular fetch
    const response = await authFetch(`${API_BASE_URL}/api/auth/me`, {
      method: "GET"
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Error: ${response.status}`
      };
    }

    const userData = await response.json();

    // More flexible check - look for any ID field
    const userId = userData.userId || userData._id || userData.id;
    const email = userData.email;

    if (userId && email) {
      // Normalize the user object to match expected structure
      return {
        success: true,
        user: {
          fullName: userData.fullName || email.split('@')[0],
          userId: userId,
          email: email,
          role: userData.role || "user"
        }
      };
    }

    return {
      success: false,
      error: "Invalid user data received"
    };
  } catch (error) {
    console.error("Error fetching user data:", error);
    return {
      success: false,
      error: "An error occurred while fetching user data"
    };
  }
}

export function setupTokenRefresh(intervalMinutes = 10): () => void {
  if (typeof window === 'undefined') return () => {};

  const intervalId = setInterval(async () => {
    // Only refresh if we're logged in
    const accessToken = localStorage.getItem('access_token');
    if (accessToken) {
      try {
        await refreshAccessToken();
      } catch (error) {
        console.error('Background token refresh failed:', error);
      }
    }
  }, intervalMinutes * 60 * 1000);

  // Return cleanup function
  return () => clearInterval(intervalId);
}

// Client-side version of refreshAccessToken
export async function refreshAccessToken(): Promise<{
  success: boolean;
  newAccessToken?: string;
}> {
  const refreshToken = localStorage.getItem('refresh_token');

  if (!refreshToken) {
    return { success: false };
  }

  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (!response.ok) {
      return { success: false };
    }

    const data = await response.json();

    if (data.access_token) {
      localStorage.setItem('access_token', data.access_token);
      return {
        success: true,
        newAccessToken: data.access_token,
      };
    }

    return { success: false };
  } catch (error) {
    console.error("Token refresh error:", error);
    return { success: false };
  }
}

// Client-side logout function
export function logoutClient(): void {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user_data');
}