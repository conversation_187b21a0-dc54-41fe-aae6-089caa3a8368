import { Model } from 'mongoose';
import { OrganizationDocument } from './interfaces/organization.interface';
import { GlobalSettingsService } from '../global-settings/global-settings.service';
export declare class MonthlyCreditsService {
    private organizationModel;
    private globalSettingsService;
    private readonly logger;
    constructor(organizationModel: Model<OrganizationDocument>, globalSettingsService: GlobalSettingsService);
    checkAndResetMonthlyCredits(organizationId: string): Promise<void>;
    private shouldResetCredits;
    resetMonthlyCredits(organization: OrganizationDocument): Promise<void>;
    getAvailableCredits(organizationId: string): Promise<{
        freeCreditsRemaining: number;
        paidCredits: number;
        totalAvailable: number;
        usingFreeCredits: boolean;
    }>;
    deductCredits(organizationId: string, amount: number): Promise<{
        success: boolean;
        freeCreditsDeducted: number;
        paidCreditsDeducted: number;
        remainingFreeCredits: number;
        remainingPaidCredits: number;
    }>;
    checkAllOrganizationsForReset(): Promise<void>;
    initializeMonthlyCredits(organizationId: string): Promise<void>;
}
