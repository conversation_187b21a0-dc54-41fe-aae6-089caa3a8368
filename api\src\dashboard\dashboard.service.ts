
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Campaign } from 'src/campaign/interfaces/campaign.interface';
import { ScheduledCall } from 'src/scheduled-call/schemas/scheduled-call.schema';
import { Agent } from 'src/agent/interfaces/agent.interface';
import { History } from 'src/history/interfaces/history.interface';
import { Contact } from 'src/contacts/interfaces/contact.interface';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DashboardStats } from './schemas/dashboard.schema';

@Injectable()
export class DashboardService {

  private readonly logger = new Logger(DashboardService.name);

  constructor(
    @InjectModel('History') private readonly historyModel: Model<History>,
    @InjectModel('ScheduledCall') private readonly scheduledCallModel: Model<ScheduledCall>,
    @InjectModel('Campaign') private readonly campaignModel: Model<Campaign>,
    @InjectModel('Agent') private readonly agentModel: Model<Agent>,
    @InjectModel('Contact') private readonly contactModel: Model<Contact>,
    @InjectModel('DashboardStats') private readonly dashboardStatsModel: Model<DashboardStats>,
  ) {}


  @Cron(CronExpression.EVERY_HOUR)
  async updateStats() {
 
    try {
      // Time ranges to pre-calculate
      const timeRanges = ['all', '7', '14', '30', '90'];
      // Get all agent roles
      const allAgentRoles = await this.getAllAgentRoles();
      
      // Include 'all' in agent types
      const agentTypes = ['all', ...allAgentRoles];

      // Generate stats for each combination
      for (const timeRange of timeRanges) {
        for (const agentType of agentTypes) {
          await this.updateStatsForCombination(timeRange, agentType);
        }
      }

    } catch (error) {
      this.logger.error('Error updating dashboard statistics:', error);
    }
  }

  async getLatestStats(timeRange: string, agentType: string) {
    const stats = await this.dashboardStatsModel.findOne({
      dateRange: timeRange,
      agentType,
    }).sort({ lastUpdated: -1 }).exec();

    // If stats are older than 2 hours, trigger an update
    if (!stats || Date.now() - stats.lastUpdated.getTime() > 2 * 60 * 60 * 1000) {
      await this.updateStatsForCombination(timeRange, agentType);
      return this.dashboardStatsModel.findOne({
        dateRange: timeRange,
        agentType,
      }).exec();
    }

    return stats;
  }

  private async updateStatsForCombination(timeRange: string, agentType: string) {
    try {
      // Get metrics from dashboard service
      const metrics = await this.getDashboardMetrics(timeRange, agentType, false);

      // Update or create stats document
      await this.dashboardStatsModel.findOneAndUpdate(
      { dateRange: timeRange, agentType },
      {
        $set: {
          ...metrics,
          lastUpdated: new Date(),
        },
      },
      { upsert: true, new: true }
    );
    } catch (error) {
      this.logger.error(
        `Error updating stats for timeRange: ${timeRange}, agentType: ${agentType}`,
        error
      );
    }
  }

  async clearCachedStats(): Promise<void> {
  try {
    // Delete all documents in the dashboard stats collection
    const result = await this.dashboardStatsModel.deleteMany({}).exec();
    
    // this.updateStats();
  } catch (error) {
    throw error;
  }
}

  async getDashboardMetrics(timeRange: string, agentType: string, fromCache: boolean = true) {

    try {
       // Get all agents to extract all available roles
      const allAgentRoles = await this.agentModel.distinct('role');

       // Normalize agent type case to match database
    const normalizedAgentType = agentType === 'all' ? 'all' : 
      allAgentRoles.find(role => role.toLowerCase() === agentType.toLowerCase());

      // Try to get cached stats first

    if (fromCache) {
      const cachedStats = await this.dashboardStatsModel.findOne({
        dateRange: timeRange,
        agentType: normalizedAgentType || agentType
      }).sort({ lastUpdated: -1 }).exec();

      if (cachedStats && 
          Date.now() - cachedStats.lastUpdated.getTime() <= 2 * 60 * 60 * 1000) {
        return cachedStats;
      }
    }

      // Calculate date range
    const dateFilter = this.getDateFilter(timeRange);
    
    
    // Get agents matching the filter
  const agents = await this.agentModel.find(
        normalizedAgentType && normalizedAgentType !== 'all' ? { role: normalizedAgentType } : {} ).exec();
  const agentIds = agents.map(agent => agent.id);
  const agentNames = agents.map(agent => agent.name); // Get agent names for filtering
    


    // Build call history filter
    const callHistoryFilter: any = {};
    if (dateFilter) {
      callHistoryFilter.callStartTime = dateFilter;
    }
  

    if (normalizedAgentType && normalizedAgentType !== 'all') {
    callHistoryFilter.agent = { $in: agentNames };
  }

    
    // Get call history data
    const callHistory = await this.historyModel
      .find(callHistoryFilter)
      .sort({ callStartTime: -1 })
      .exec();
    
    // Get recent calls (limited to 3)
    const recentCalls = await this.historyModel
      .find(callHistoryFilter)
      .sort({ callStartTime: -1 })
      .limit(3)
      .exec();
    
    // Get scheduled calls
    const scheduledCallsFilter: any = {};
    if (agentType !== 'all') {
      scheduledCallsFilter.agentId = { $in: agentIds };
    }

     // Get campaigns
    const campaignFilter = {};
    if (agentType !== 'all') {
      campaignFilter['agentId'] = { $in: agentIds };
    }
    
    const recentSchedules = await this.scheduledCallModel
      .find(scheduledCallsFilter)
      .sort({ scheduledTime: 1 })
      .limit(3)
      .exec();
    
    // Get campaigns
    const recentCampaigns = await this.campaignModel
      .find(campaignFilter)
      .sort({ startDate: -1 })
      .limit(3)
      .exec();
    
    // Calculate call metrics
    const callMetrics = this.calculateCallMetrics(callHistory);

     // Get total counts
    const totalCounts = await this.calculateTotalCounts(normalizedAgentType || agentType, agentIds, agentNames);
    
    // Calculate call end reasons
    const callEndReasons = this.calculateCallEndReasons(callHistory);
    
    // Calculate top agents
    const topAgents = this.calculateTopAgents(callHistory, agents);
    
    // Calculate sentiments
    const sentiments = this.calculateSentiments(callHistory);

    
    const metrics = {
      callMetrics,
      totalCounts,
      sentiments,
      callEndReasons,
      agentRoles: allAgentRoles,
      topAgents,
      recentCalls,
      recentSchedules,
      recentCampaigns,
    };

     await this.dashboardStatsModel.deleteMany({
      agentType: { $nin: [...allAgentRoles, 'all'] }
    });


     // Cache the new results
    await this.dashboardStatsModel.findOneAndUpdate(
      { dateRange: timeRange, agentType },
      {
        $set: {
          ...metrics,
          lastUpdated: new Date(),
        },
      },
      { upsert: true, new: true }
    );

    return metrics;

    } catch (error) {
      throw new Error(`Error fetching dashboard metrics: ${error.message}`);
    } 
  }
  
  async getAllAgentRoles(): Promise<string[]> {
    return this.agentModel.distinct('role').exec();
  }
  
  private async calculateTotalCounts(agentType: string, agentIds: string[], agentNames: string[]) {
    // Build campaign filter
    const campaignFilter = {};
    if (agentType !== 'all') {
      campaignFilter['agentId'] = { $in: agentIds };
    }
    
    // Build scheduled calls filter
    const scheduledCallsFilter = {};
    if (agentType !== 'all') {
      scheduledCallsFilter['agentId'] = { $in: agentIds };
    }

    // Build history filter for total calls count
    const historyFilter = {};
    if (agentType !== 'all') {
        historyFilter['agent'] = { $in: agentNames };
    }
    
    // Get total counts
    const totalCampaigns = await this.campaignModel.countDocuments(campaignFilter).exec();
    const totalScheduledCalls = await this.scheduledCallModel.countDocuments(scheduledCallsFilter).exec();
    const totalContacts = await this.contactModel.countDocuments().exec();
    const totalCalls = await this.historyModel.countDocuments(historyFilter).exec();

    return {
      totalCampaigns,
      totalScheduledCalls,
      totalContacts,
      totalCalls,
    };
  }

  private getDateFilter(timeRange: string) {
    if (timeRange === 'all') {
      return null;
    }
    
    const days = parseInt(timeRange, 10) || 7;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    return { $gte: startDate };
  }

  private calculateCallMetrics(callHistory: History[]) {
    // Total calls
    const totalCalls = callHistory.length;
    
    // Calculate total minutes
    const totalMinutes = callHistory.reduce((total, call) => {
      if (call.callDuration) {
        const durationMs = typeof call.callDuration === 'string' 
          ? parseInt(call.callDuration) 
          : call.callDuration;
        return total + (durationMs / (1000 * 60));
      }
      return total;
    }, 0);
    
    // Calculate average call length
    const callsWithDuration = callHistory.filter(call => call.callDuration);
    let averageLength = 0;
    
    if (callsWithDuration.length > 0) {
      const totalDurationMs = callsWithDuration.reduce((total, call) => {
        const durationMs = typeof call.callDuration === 'string' 
          ? parseInt(call.callDuration) 
          : call.callDuration;
        return total + durationMs;
      }, 0);
      
      averageLength = totalDurationMs / callsWithDuration.length;
    }
    
    // Calculate connection rate
    const connectedCalls = callHistory.filter(call => {
      if (call.callDuration) {
        const durationMs = typeof call.callDuration === 'string' 
          ? parseInt(call.callDuration) 
          : call.callDuration;
        return durationMs > 50000; // 50 seconds threshold
      }
      return false;
    }).length;
    
    const connectionRate = totalCalls > 0 ? Math.round((connectedCalls / totalCalls) * 100) : 0;
    
    // Calculate answer rate
    const answeredCalls = callHistory.filter(call => {
      const reason = call.callEndReason?.toLowerCase() || '';
      return reason.includes('customer-ended-call') || reason.includes('assistant-ended-call');
    }).length;
    
    const answerRate = totalCalls > 0 ? Math.round((answeredCalls / totalCalls) * 100) : 0;
    
    return {
      totalCalls,
      totalMinutes: Math.round(totalMinutes * 100) / 100, // Round to 2 decimal places
      averageLength,
      connectionRate,
      answerRate,
    };
  }
  
  private calculateCallEndReasons(callHistory: History[]) {
    if (callHistory.length === 0) {
      return [];
    }
    
    // Count occurrences of each reason
    const reasonCounts = callHistory.reduce<Record<string, number>>((acc, call) => {
      const reason = call.callEndReason || 'unknown';
      acc[reason] = (acc[reason] || 0) + 1;
      return acc;
    }, {});
    
    // Convert to array of objects with percentages
    const total = callHistory.length;
    const reasonsArray = Object.entries(reasonCounts).map(([reason, count]) => {
      const percentage = Math.round((count / total) * 100);
      return { reason, count, percentage };
    });
    
    // Sort by percentage (highest first)
    return reasonsArray.sort((a, b) => b.percentage - a.percentage);
  }

  private calculateTopAgents(callHistory: History[], agents: Agent[]) {
    // Create a map of agent IDs to call counts
    const agentCallCounts = callHistory.reduce<Record<string, number>>((acc, call) => {
      if (call.agent) {
        acc[call.agent] = (acc[call.agent] || 0) + 1;
      }
      return acc;
    }, {});
    
    // Map agent IDs to agent objects with call counts
    const topAgents = agents
      .map(agent => {
        const callCount = agentCallCounts[agent.name] || 0;
        return {
          id: agent.id,
          name: agent.name,
          avatar: agent.avatar,
          role: agent.role,
          status: agent.status,
          callCount,
        };
      })
      .sort((a, b) => b.callCount - a.callCount)
      .slice(0, 5); // Get top 5 agents
    
    return topAgents;
  }

  private calculateSentiments(callHistory: History[]) {
    // Count emotion occurrences
    const sentimentCounts = callHistory.reduce<Record<string, number>>((acc, call) => {
      const emotion = call.emotions || 'Neutral';
      
      // Map emotions to sentiment categories
      let sentiment = 'neutral';
      if (emotion === 'Positive' || emotion === 'Slightly Positive') {
        sentiment = 'positive';
      } else if (emotion === 'Negative' || emotion === 'Slightly Negative') {
        sentiment = 'negative';
      }
      
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    }, { positive: 0, neutral: 0, negative: 0 });
    
    // Calculate percentages
    const total = callHistory.length || 1; // Avoid division by zero
    
    return {
      positive: Math.round((sentimentCounts.positive / total) * 100),
      neutral: Math.round((sentimentCounts.neutral / total) * 100),
      negative: Math.round((sentimentCounts.negative / total) * 100),
    };
  }
}