"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const users_service_1 = require("../users/users.service");
const core_1 = require("@nestjs/core");
const websocket_gateway_1 = require("../websocket/websocket.gateway");
const credit_gateway_1 = require("../credit/credit.gateway");
const monthly_credits_service_1 = require("./monthly-credits.service");
const notifications_service_1 = require("../notifications/notifications.service");
let OrganizationsService = class OrganizationsService {
    constructor(organizationModel, usersService, moduleRef, monthlyCreditsService, notificationsService) {
        this.organizationModel = organizationModel;
        this.usersService = usersService;
        this.moduleRef = moduleRef;
        this.monthlyCreditsService = monthlyCreditsService;
        this.notificationsService = notificationsService;
    }
    onModuleInit() {
        this.websocketGateway = this.moduleRef.get(websocket_gateway_1.WebsocketGateway, { strict: false });
        this.creditGateway = this.moduleRef.get(credit_gateway_1.CreditGateway, { strict: false });
    }
    async create(createOrganizationDto) {
        const newOrganization = new this.organizationModel({
            ...createOrganizationDto,
            credits: 0,
            autoRechargeEnabled: false,
            autoRechargeThreshold: 1.0,
            autoRechargeAmount: 0,
        });
        const savedOrganization = await newOrganization.save();
        await this.monthlyCreditsService.initializeMonthlyCredits(savedOrganization._id.toString());
        return savedOrganization;
    }
    async findAll() {
        return this.organizationModel.find().exec();
    }
    async findOne(id) {
        const organization = await this.organizationModel.findById(id).exec();
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
        return organization;
    }
    async update(id, updateOrganizationDto) {
        const organization = await this.organizationModel.findById(id).exec();
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
        Object.assign(organization, {
            ...updateOrganizationDto,
            updatedAt: new Date()
        });
        return organization.save();
    }
    async updateBilling(id, updateBillingDto) {
        const organization = await this.organizationModel.findById(id).exec();
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
        const oldMonthlyAllowance = organization.monthlyMinutesAllowance;
        const newMonthlyAllowance = updateBillingDto.monthlyMinutesAllowance;
        Object.assign(organization, {
            ...updateBillingDto,
            updatedAt: new Date()
        });
        if (newMonthlyAllowance !== undefined && newMonthlyAllowance !== oldMonthlyAllowance) {
            organization.monthlyFreeCredits = newMonthlyAllowance;
            if (newMonthlyAllowance === 0) {
                organization.monthlyFreeCreditsUsed = 0;
                organization.usingFreeCredits = false;
            }
            else if (newMonthlyAllowance > oldMonthlyAllowance) {
                organization.usingFreeCredits = true;
            }
            console.log(`Updated monthly allowance for organization ${id} from ${oldMonthlyAllowance} to ${newMonthlyAllowance} minutes`);
        }
        const updatedOrganization = await organization.save();
        if (updateBillingDto.credits !== undefined) {
            console.log(`Updated credits for organization ${id} to ${updateBillingDto.credits}`);
            const availableCredits = await this.monthlyCreditsService.getAvailableCredits(id);
            if (this.websocketGateway) {
                this.websocketGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
            }
            if (this.creditGateway) {
                this.creditGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
            }
            const orgForNotification = { ...updatedOrganization.toObject(), credits: availableCredits.totalAvailable };
            await this.notificationsService.processCreditNotifications(orgForNotification);
        }
        return updatedOrganization;
    }
    async addCredits(id, amount) {
        if (amount <= 0) {
            throw new common_1.BadRequestException('Credit amount must be positive');
        }
        const organization = await this.organizationModel.findById(id).exec();
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
        organization.credits += amount;
        organization.updatedAt = new Date();
        const updatedOrganization = await organization.save();
        const availableCredits = await this.monthlyCreditsService.getAvailableCredits(id);
        if (this.websocketGateway) {
            this.websocketGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
        }
        if (this.creditGateway) {
            this.creditGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
        }
        const orgForNotification = { ...updatedOrganization.toObject(), credits: availableCredits.totalAvailable };
        await this.notificationsService.processCreditNotifications(orgForNotification);
        return updatedOrganization;
    }
    async deductCredits(id, amount) {
        if (amount <= 0) {
            throw new common_1.BadRequestException('Credit amount must be positive');
        }
        const deductionResult = await this.monthlyCreditsService.deductCredits(id, amount);
        if (!deductionResult.success) {
            throw new common_1.BadRequestException('Insufficient credits');
        }
        const updatedOrganization = await this.organizationModel.findById(id).exec();
        if (!updatedOrganization) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
        const availableCredits = await this.monthlyCreditsService.getAvailableCredits(id);
        if (this.websocketGateway) {
            this.websocketGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
        }
        if (this.creditGateway) {
            this.creditGateway.sendOrganizationCreditUpdate(id, availableCredits.totalAvailable);
        }
        const orgForNotification = { ...updatedOrganization.toObject(), credits: availableCredits.totalAvailable };
        await this.notificationsService.processCreditNotifications(orgForNotification);
        return updatedOrganization;
    }
    async getAvailableCredits(id) {
        return this.monthlyCreditsService.getAvailableCredits(id);
    }
    async addUserToOrganization(organizationId, userId, isAdmin = false) {
        const organization = await this.organizationModel.findById(organizationId).exec();
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${organizationId} not found`);
        }
        if (isAdmin) {
            if (!organization.adminUsers.includes(userId)) {
                organization.adminUsers.push(userId);
            }
        }
        else {
            if (!organization.users.includes(userId)) {
                organization.users.push(userId);
            }
        }
        if (!organization.users.includes(userId)) {
            organization.users.push(userId);
        }
        organization.updatedAt = new Date();
        await organization.save();
        try {
            await this.usersService.updateUser(userId, { organizationId: organizationId });
            console.log(`Updated organizationId for user ${userId} to ${organizationId}`);
        }
        catch (error) {
            console.error(`Failed to update organizationId for user ${userId}:`, error);
        }
        return organization;
    }
    async removeUserFromOrganization(organizationId, userId) {
        const organization = await this.organizationModel.findById(organizationId).exec();
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${organizationId} not found`);
        }
        organization.adminUsers = organization.adminUsers.filter(id => id.toString() !== userId);
        organization.users = organization.users.filter(id => id.toString() !== userId);
        organization.updatedAt = new Date();
        await organization.save();
        try {
            await this.usersService.updateUser(userId, { organizationId: null });
            console.log(`Cleared organizationId for user ${userId}`);
        }
        catch (error) {
            console.error(`Failed to clear organizationId for user ${userId}:`, error);
        }
        return organization;
    }
    async delete(id) {
        const result = await this.organizationModel.deleteOne({ _id: id }).exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
    }
    async findByUser(userId) {
        return this.organizationModel.find({
            $or: [
                { adminUsers: userId },
                { users: userId }
            ]
        }).exec();
    }
    async findByAdmin(adminId) {
        return this.organizationModel.find({
            adminUsers: adminId
        }).exec();
    }
    async setStripeCustomerId(id, stripeCustomerId) {
        const organization = await this.organizationModel.findById(id).exec();
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${id} not found`);
        }
        organization.stripeCustomerId = stripeCustomerId;
        organization.updatedAt = new Date();
        return organization.save();
    }
    async initializeMonthlyCreditsForOrganization(id) {
        await this.monthlyCreditsService.initializeMonthlyCredits(id);
        return this.findOne(id);
    }
    async updateSettings(id, updateSettingsDto) {
        const organization = await this.organizationModel.findById(id).exec();
        if (!organization) {
            throw new Error('Organization not found');
        }
        if (updateSettingsDto.monthlyResetDate !== undefined) {
            organization.monthlyResetDate = updateSettingsDto.monthlyResetDate;
        }
        if (updateSettingsDto.fullName !== undefined) {
            organization.fullName = updateSettingsDto.fullName;
        }
        if (updateSettingsDto.email !== undefined) {
            organization.email = updateSettingsDto.email;
        }
        organization.updatedAt = new Date();
        return organization.save();
    }
    async updateAllOrganizationsMonthlyCredits(newMonthlyMinutesAllowance) {
        try {
            const organizations = await this.organizationModel.find({ status: 'active' }).exec();
            console.log(`Updating monthly credits for ${organizations.length} organizations to ${newMonthlyMinutesAllowance} minutes`);
            for (const organization of organizations) {
                try {
                    organization.monthlyFreeCredits = newMonthlyMinutesAllowance;
                    organization.monthlyFreeCreditsUsed = 0;
                    organization.usingFreeCredits = newMonthlyMinutesAllowance > 0;
                    organization.lastMonthlyReset = new Date();
                    organization.updatedAt = new Date();
                    await organization.save();
                    console.log(`Updated monthly credits for organization ${organization.name}: ${newMonthlyMinutesAllowance} minutes`);
                    const availableCredits = await this.monthlyCreditsService.getAvailableCredits(organization._id.toString());
                    if (this.websocketGateway) {
                        this.websocketGateway.sendOrganizationCreditUpdate(organization._id.toString(), availableCredits.totalAvailable);
                    }
                    if (this.creditGateway) {
                        this.creditGateway.sendOrganizationCreditUpdate(organization._id.toString(), availableCredits.totalAvailable);
                    }
                }
                catch (error) {
                    console.error(`Error updating monthly credits for organization ${organization.name}:`, error);
                }
            }
            console.log('Completed updating monthly credits for all organizations');
        }
        catch (error) {
            console.error('Error in updateAllOrganizationsMonthlyCredits:', error);
            throw error;
        }
    }
};
exports.OrganizationsService = OrganizationsService;
exports.OrganizationsService = OrganizationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Organization')),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => users_service_1.UsersService))),
    __metadata("design:paramtypes", [mongoose_2.Model,
        users_service_1.UsersService,
        core_1.ModuleRef,
        monthly_credits_service_1.MonthlyCreditsService,
        notifications_service_1.NotificationsService])
], OrganizationsService);
//# sourceMappingURL=organizations.service.js.map