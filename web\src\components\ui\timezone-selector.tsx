"use client"

import moment from "moment-timezone";
import { useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import { Button } from "./button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "./command";



export const TimezoneSelector = ({ value, onChange }: { 
    value: string, 
    onChange: (value: string) => void 
  }) => {
    const [open, setOpen] = useState(false)
    const [searchQuery, setSearchQuery] = useState("")
  
    // Group timezones
    const groupedZones = moment.tz.names().reduce((acc, zone) => {
      const region = zone.split('/')[0];
      if (!acc[region]) {
        acc[region] = [];
      }
      acc[region].push(zone);
      return acc;
    }, {} as Record<string, string[]>);
  
    return (
      <Popover modal={true} open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {value
              ? `${value.split('/').pop()?.replace(/_/g, ' ')} (${moment.tz(value).format('Z')})`
              : "Select timezone..."}
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          className="w-[250px] p-0" 
          align="start"
          side="bottom"
          sideOffset={5}
        >
          <Command className="max-h-[300px]">
            <div className="sticky top-0 bg-background z-10 border-b">
              <CommandInput
                placeholder="Search timezone..." 
                onValueChange={setSearchQuery}
                className="py-2"
              />
            </div>
            <div className="overflow-y-auto max-h-[250px]">
              <CommandEmpty>No timezone found.</CommandEmpty>
              {Object.entries(groupedZones).map(([region, zones]) => {
                const filteredZones = zones.filter(zone => 
                  zone.toLowerCase().includes(searchQuery.toLowerCase())
                );
                
                if (filteredZones.length === 0) return null;
  
                return (
                  <CommandGroup key={region} heading={region}>
                    {filteredZones.map((zone) => (
                      <CommandItem
                        key={zone}
                        value={zone}
                        onSelect={() => {
                          onChange(zone)
                          setOpen(false)
                        }}
                        className="cursor-pointer"
                      >
                        {zone.split('/').pop()?.replace(/_/g, ' ')} ({moment.tz(zone).format('Z')})
                      </CommandItem>
                    ))}
                  </CommandGroup>
                );
              })}
            </div>
          </Command>
        </PopoverContent>
      </Popover>
    );
  };