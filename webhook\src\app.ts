import express from "express";
import bodyParser from "body-parser";
import cors from "cors";
import { setWebhookRoutes } from "./routes/webhookRoutes";

const app = express();
const PORT =5000;

app.use(
  cors({
    origin: "https://api.vapi.ai", 
  })
);

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

setWebhookRoutes(app);

app.listen(PORT, () => {
  console.log(`Webhook is running on ${PORT}`);
});