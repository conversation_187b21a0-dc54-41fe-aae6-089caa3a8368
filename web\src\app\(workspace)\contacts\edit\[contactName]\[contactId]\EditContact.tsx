/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {Card,CardContent,CardDescription,CardHeader,CardTitle,} from "@/components/ui/card";
import { ArrowLeft, Check } from "lucide-react";
import Link from "next/link";
import {Dialog,DialogContent,DialogDescription,DialogFooter,DialogHeader,DialogTitle} from "@/components/ui/dialog";
import {Campaign, fetchCampaign} from "@/app/api/contacts";
import {Select,SelectContent,SelectItem,SelectTrigger,SelectValue} from "@/components/ui/select";
import { getRegionFromPhoneNumber } from "@/lib/phone-utils";
import { TimezoneSelector } from "@/components/ui/timezone-selector";

// Define a proper type for the contact state
interface ContactState {
  _id: string;
  contactName: string;
  phoneNumber: string;
  region: string;
  projectName?: string;
  unitNumber?: string;
  totalPayableAmount?: string | number;
  pendingPayableAmount?: string | number;
  dueDate?: string;
  totalInstallments?: string | number;
  paymentType?: string;
  pendingInstallments?: string | number;
  lastPaymentDate?: string;
  lastPaymentAmount?: string | number;
  lastPaymentType?: string;
  collectionBucket?: string;
  unitPrice?: string | number;
  paidAmtIncluding?: string | number;
  // Event related fields
  eventDate?: string;
  eventLocation?: string;
  eventTime?: string;
  nameOfRegistrant?: string;
  campaigns: (string | { _id: string; name: string })[];
  lastCall?: string;
}

interface EditContactProps {
  contactName?: string;
  contactId?: string;
}

export default function EditContact({ contactName: propContactName, contactId: propContactId }: EditContactProps) {
  const params = useParams();
  const contactName = propContactName || (params?.contactName as string);
  const contactId = propContactId || (params?.contactId as string);
  const decodedContactId = contactId ? decodeURIComponent(contactId) : '';
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [originalPhoneNumber, setOriginalPhoneNumber] = useState("");
  const [contact, setContact] = useState<ContactState>({
    _id: "",
    contactName: "",
    phoneNumber: "",
    region: "",
    projectName: "",
    unitNumber: "",
    totalPayableAmount: "",
    pendingPayableAmount: "",
    dueDate: "",
    totalInstallments: "",
    paymentType: "",
    pendingInstallments: "",
    lastPaymentDate: "",
    lastPaymentAmount: "",
    lastPaymentType: "",
    collectionBucket: "",
    unitPrice: "",
    paidAmtIncluding: "",
    // Event related fields
    eventDate: "",
    eventLocation: "",
    eventTime: "",
    nameOfRegistrant: "",
    campaigns: [],
  });

  const loadContacts = async () => {
    try {
       // Get token from localStorage
    const token = localStorage.getItem("access_token");
    if (!token) {
      console.error("No access token available");
      setError("Authentication required");
      return;
    }

    // Fetch the specific contact by ID instead of all contacts
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contacts/${decodedContactId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch contact: ${response.status}`);
    }

      // Find contact by ID instead of just name
      const currentContact = await response.json();


      if (currentContact) {
        // Handle MongoDB date format which might come as an object with $date property
        let eventDateValue = "";
        if (currentContact.eventDate) {
          if (typeof currentContact.eventDate === 'object' && '$date' in currentContact.eventDate) {
            // Handle MongoDB date format
            eventDateValue = new Date((currentContact.eventDate as { $date: string | number }).$date).toISOString();
          } else {
            // Handle regular string date
            eventDateValue = currentContact.eventDate.toString();
          }
        }

        console.log('Processed eventDate:', eventDateValue);

        // Cast the contact to ensure all required fields are present
        setContact({
          ...currentContact,
          contactName: currentContact.contactName || "",
          phoneNumber: currentContact.phoneNumber || "",
          region: currentContact.region || "",
          campaigns: currentContact.campaigns || [],
          // Explicitly preserve event-related fields with proper handling
          eventDate: eventDateValue,
          eventLocation: currentContact.eventLocation || "",
          eventTime: currentContact.eventTime || "",
          nameOfRegistrant: currentContact.nameOfRegistrant || "",
        } as ContactState);
        setOriginalPhoneNumber(currentContact.phoneNumber || "");
      } else {
        setError("Contact not found");
      }
    } catch (err) {
      console.error("Error fetching contact:", err);
      setError("Failed to fetch contact data");
    }
  };

  const loadCampaigns = async () => {
    try {
      const data = await fetchCampaign();
      console.log('Loaded campaigns:', data);
      setCampaigns(data);

      // Check if AquaRiseEvent campaign exists
      const aquaRiseEvent = data.find((c: { name: string; }) => c.name === 'AquaRiseEvent');
      console.log('AquaRiseEvent campaign:', aquaRiseEvent);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
    }
  };

  const formatDate = (isoString: string | null) => {
    if (!isoString) return "";
    return new Date(isoString).toISOString().split("T")[0]; // Extract YYYY-MM-DD
  };

  // Fetch contact data and campaigns on mount
  useEffect(() => {
    if (decodedContactId) {
      loadContacts();
      loadCampaigns();
    }
  }, [decodedContactId]);

  // Log contact state changes for debugging
  useEffect(() => {
    console.log('Contact state updated:', {
      campaigns: contact.campaigns,
      eventDate: contact.eventDate,
      eventLocation: contact.eventLocation,
      eventTime: contact.eventTime,
      nameOfRegistrant: contact.nameOfRegistrant
    });
  }, [contact]);

  // Helper function to determine if the selected campaign is Collections, Sales, or AquaRiseEvent
  const getSelectedCampaignType = (): 'Sales' | 'Collections' | 'AquaRiseEvent' | null => {
    if (!contact.campaigns || contact.campaigns.length === 0 || !campaigns) {
      return null;
    }

    // Check if the campaign is an object or a string ID
    let campaignId;
    if (typeof contact.campaigns[0] === 'object') {
      // Handle MongoDB object format
      if ('$oid' in (contact.campaigns[0] as Record<string, any>)) {
        campaignId = (contact.campaigns[0] as unknown as { $oid: string }).$oid;
      } else {
        campaignId = (contact.campaigns[0] as { _id: string })._id;
      }
    } else {
      campaignId = contact.campaigns[0] as string;
    }

    console.log('Campaign ID for lookup:', campaignId);
    console.log('Available campaigns:', campaigns);

    // Try to find the campaign by ID
    const selectedCampaign = campaigns.find(c => {
      // Handle different ID formats
      if (c._id === campaignId) return true;
      if (typeof c._id === 'object' && '$oid' in (c._id as { $oid: string }) && (c._id as { $oid: string }).$oid === campaignId) return true;
      return false;
    });

    if (!selectedCampaign) {
      console.log('No campaign found with ID:', campaignId);
      return null;
    }

    // Case-insensitive comparison for more robustness
    const campaignName = selectedCampaign.name.toLowerCase();
    if (campaignName.includes('collections')) return 'Collections';
    if (campaignName.includes('sales')) return 'Sales';
    if (campaignName.includes('aquarise') || campaignName.includes('aqua rise')) return 'AquaRiseEvent';

    return null;
  };

  useEffect(() => {
    if (contact.phoneNumber && contact.phoneNumber !== originalPhoneNumber) {
      const detectedRegion = getRegionFromPhoneNumber(contact.phoneNumber);
      if (detectedRegion) {
        setContact((prev: ContactState) => ({ ...prev, region: detectedRegion }));
      }
    }
  }, [contact.phoneNumber, originalPhoneNumber]);

  const handleUpdate = async () => {
    if (!contact.contactName || !contact.phoneNumber || !contact.region || !contact.campaigns || contact.campaigns.length === 0) {
      setError("Contact Name, Phone Number, Region, and Campaign are required");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Convert string numeric values to actual numbers before submitting
      const contactToSubmit = {
        ...contact,
        totalPayableAmount: typeof contact.totalPayableAmount === 'string' ?
          parseFloat(contact.totalPayableAmount) || 0 : contact.totalPayableAmount,
        pendingPayableAmount: typeof contact.pendingPayableAmount === 'string' ?
          parseFloat(contact.pendingPayableAmount) || 0 : contact.pendingPayableAmount,
        lastPaymentAmount: typeof contact.lastPaymentAmount === 'string' ?
          parseFloat(contact.lastPaymentAmount) || 0 : contact.lastPaymentAmount,
        unitPrice: typeof contact.unitPrice === 'string' ?
          parseFloat(contact.unitPrice) || 0 : contact.unitPrice,
        paidAmtIncluding: typeof contact.paidAmtIncluding === 'string' ?
          parseFloat(contact.paidAmtIncluding) || 0 : contact.paidAmtIncluding,
        totalInstallments: typeof contact.totalInstallments === 'string' ?
          parseInt(contact.totalInstallments) || 0 : contact.totalInstallments,
        pendingInstallments: typeof contact.pendingInstallments === 'string' ?
          parseInt(contact.pendingInstallments) || 0 : contact.pendingInstallments,
        // Format eventDate as ISO string if it exists
        eventDate: contact.eventDate ?
          (() => {
            try {
              // If it's already an ISO string, just use it
              if (typeof contact.eventDate === 'string' && contact.eventDate.includes('T')) {
                return contact.eventDate;
              }
              // Otherwise, convert to ISO string
              return new Date(contact.eventDate).toISOString();
            } catch (e) {
              console.error('Error formatting eventDate for submission:', e);
              return contact.eventDate; // Return as is if conversion fails
            }
          })() : undefined,
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contacts/${contact._id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
          body: JSON.stringify(contactToSubmit),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update contact");
      }

      setShowSuccessDialog(true);
    } catch (err) {
      setError("Failed to update contact");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto py-2">
      <div className="mb-6">
        <Link
          href="/contacts"
          className="text-sm text-muted-foreground hover:text-primary flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Contacts
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Edit Contact</CardTitle>
          <CardDescription>Update contact information.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Campaigns - Moved to the top */}
            <div className="space-y-4">
              <div>
                <Label className="mb-2">Campaign*</Label>
                <Select
                  value={(() => {
                    // Get the current campaign name
                    let campaignName = "";
                    if (contact.campaigns && contact.campaigns.length > 0) {
                      // Handle different campaign object formats
                      if (typeof contact.campaigns[0] === "object") {
                        if ('$oid' in (contact.campaigns[0] as Record<string, any>)) {
                          // MongoDB format with $oid
                          const campaignId = (contact.campaigns[0] as unknown as { $oid: string }).$oid;
                          const foundCampaign = campaigns.find(c => {
                            if (c._id === campaignId) return true;
                            if (typeof c._id === 'object' && '$oid' in (c._id as Record<string, any>) && (c._id as { $oid: string }).$oid === campaignId) return true;
                            return false;
                          });
                          campaignName = foundCampaign?.name || "";
                        } else if ('_id' in (contact.campaigns[0] as Record<string, any>)) {
                          // Regular object with _id property
                          campaignName = (contact.campaigns[0] as { _id: string, name: string }).name || "";
                        }
                      } else {
                        // String ID
                        const foundCampaign = campaigns.find(c => c._id === (contact.campaigns || [])[0]);
                        campaignName = foundCampaign?.name || "";
                      }
                    }

                    return campaignName;
                  })()}
                  onValueChange={(value) => {
                    const selectedCampaign = campaigns.find(
                      (c) => c.name === value
                    );
                    console.log('Found campaign:', selectedCampaign);
                    if (selectedCampaign) {
                      setContact((prev: ContactState) => ({
                        ...prev,
                        campaigns: [selectedCampaign._id],
                      }));
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a campaign" />
                  </SelectTrigger>
                  <SelectContent>
                    {campaigns?.map((campaign) => (
                      <SelectItem key={campaign._id} value={campaign.name}>
                        {campaign.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Contact Information - Always displayed */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="contactName" className="mb-2">
                    Contact Name*
                  </Label>
                  <Input
                    id="contactName"
                    value={contact.contactName ?? ""}
                    onChange={(e) =>
                      setContact((prev: ContactState) => ({
                        ...prev,
                        contactName: e.target.value,
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="phoneNumber" className="mb-2">
                    Phone Number*
                  </Label>
                  <Input
                    id="phoneNumber"
                    value={contact.phoneNumber ?? ""}
                    onChange={(e) =>
                      setContact((prev: ContactState) => ({
                        ...prev,
                        phoneNumber: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="region" className="mb-2">
                    Region*
                  </Label>
                  <TimezoneSelector
                    value={contact.region ?? ""}
                    onChange={(zone) =>
                      setContact((prev: ContactState) => ({ ...prev, region: zone }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* Show event-related fields only for AquaRiseEvent campaign */}
            {getSelectedCampaignType() === 'AquaRiseEvent' && (
              <>
                {/* Event Information */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="eventDate" className="mb-2">
                        Event Date
                      </Label>
                      <Input
                        id="eventDate"
                        type="datetime-local"
                        value={(() => {
                          if (!contact.eventDate) return "";

                          try {
                            console.log('Formatting eventDate for input:', contact.eventDate);
                            // Convert to ISO string and take only the date and time part (YYYY-MM-DDTHH:MM)
                            return contact.eventDate.slice(0, 16);
                          } catch (e) {
                            console.error('Error formatting eventDate:', e);
                            return "";
                          }
                        })()}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            eventDate: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="eventLocation" className="mb-2">
                        Event Location
                      </Label>
                      <Input
                        id="eventLocation"
                        value={(() => {
                          console.log('Event location value:', contact.eventLocation);
                          return contact.eventLocation ?? "";
                        })()}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            eventLocation: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    {/* <div>
                      <Label htmlFor="eventTime" className="mb-2">
                        Event Time
                      </Label>
                      <Input
                        id="eventTime"
                        value={(() => {
                          console.log('Event time value:', contact.eventTime);
                          return contact.eventTime ?? "";
                        })()}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            eventTime: e.target.value,
                          }))
                        }
                      />
                    </div> */}
                    <div>
                      <Label htmlFor="nameOfRegistrant" className="mb-2">
                        Name of Registrant
                      </Label>
                      <Input
                        id="nameOfRegistrant"
                        value={(() => {
                          console.log('Name of registrant value:', contact.nameOfRegistrant);
                          return contact.nameOfRegistrant ?? "";
                        })()}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            nameOfRegistrant: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Show additional fields only for Collections campaign */}
            {getSelectedCampaignType() === 'Collections' && (
              <>
                {/* Project Details */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="projectName" className="mb-2">
                        Project Name
                      </Label>
                      <Input
                        id="projectName"
                        value={contact.projectName ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            projectName: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="unitNumber" className="mb-2">
                        Unit Number
                      </Label>
                      <Input
                        id="unitNumber"
                        value={contact.unitNumber ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            unitNumber: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="unitPrice" className="mb-2">
                        Unit Price
                      </Label>
                      <Input
                        id="unitPrice"
                        type="number"
                        value={contact.unitPrice ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            unitPrice: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="paidAmtIncluding" className="mb-2">
                        Paid Amount Including
                      </Label>
                      <Input
                        id="paidAmtIncluding"
                        type="number"
                        value={contact.paidAmtIncluding ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            paidAmtIncluding: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Payment Details */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="totalPayableAmount" className="mb-2">
                        Total Payable Amount
                      </Label>
                      <Input
                        id="totalPayableAmount"
                        type="number"
                        value={contact.totalPayableAmount ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            totalPayableAmount: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="pendingPayableAmount" className="mb-2">
                        Pending Payable Amount
                      </Label>
                      <Input
                        id="pendingPayableAmount"
                        type="number"
                        value={contact.pendingPayableAmount ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            pendingPayableAmount: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="dueDate" className="mb-2">
                        Due Date
                      </Label>
                      <Input
                        id="dueDate"
                        type="date"
                        value={
                          contact.dueDate
                            ? formatDate(contact.dueDate)
                            : ""
                        }
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            dueDate: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="paymentType" className="mb-2">
                        Payment Type
                      </Label>
                      <Input
                        id="paymentType"
                        value={contact.paymentType ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            paymentType: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Installment Details */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="totalInstallments" className="mb-2">
                        Total Installments
                      </Label>
                      <Input
                        id="totalInstallments"
                        type="number"
                        value={contact.totalInstallments ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            totalInstallments: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="pendingInstallments" className="mb-2">
                        Pending Installments
                      </Label>
                      <Input
                        id="pendingInstallments"
                        type="number"
                        value={contact.pendingInstallments ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            pendingInstallments: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="collectionBucket" className="mb-2">
                        Collection Bucket
                      </Label>
                      <Input
                        id="collectionBucket"
                        value={contact.collectionBucket ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            collectionBucket: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Last Payment Details */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="lastPaymentDate" className="mb-2">
                        Last Payment Date
                      </Label>
                      <Input
                        id="lastPaymentDate"
                        type="date"
                        value={
                          contact.lastPaymentDate
                            ? formatDate(contact.lastPaymentDate)
                            : ""
                        }
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            lastPaymentDate: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="lastPaymentAmount" className="mb-2">
                        Last Payment Amount
                      </Label>
                      <Input
                        id="lastPaymentAmount"
                        type="number"
                        value={contact.lastPaymentAmount ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            lastPaymentAmount: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastPaymentType" className="mb-2">
                        Last Payment Type
                      </Label>
                      <Input
                        id="lastPaymentType"
                        value={contact.lastPaymentType ?? ""}
                        onChange={(e) =>
                          setContact((prev: ContactState) => ({
                            ...prev,
                            lastPaymentType: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm">
                {error}
              </div>
            )}

            <div className="flex justify-end gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/contacts")}
              >
                Cancel
              </Button>
              <Button onClick={handleUpdate} disabled={loading}>
                {loading ? "Updating..." : "Update Contact"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Success</DialogTitle>
            <DialogDescription>Contact updated successfully.</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => router.push("/contacts")}>
              <Check className="mr-2 h-4 w-4" /> Back to Contacts
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
