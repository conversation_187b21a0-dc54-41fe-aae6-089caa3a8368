"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { EmptyState } from "./EmptyState";

type Spreadsheet = {
  id: string;
  title: string;
  filename: string;
  rows: number;
  columns: number;
  lastUpdated: Date;
};

type SpreadsheetsViewProps = {
  spreadsheets: Spreadsheet[];
  isEmpty: boolean;
  onAddClick: () => void;
  onDelete: (id: string) => void;
};

export function SpreadsheetsView({ spreadsheets, isEmpty, onAddClick, onDelete }: SpreadsheetsViewProps) {
  if (isEmpty) {
    return <EmptyState type="spreadsheet" onAdd={onAddClick} />;
  }

  return (
    <div className="rounded-md border dark:border-gray-700">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Filename</TableHead>
            <TableHead>Size</TableHead>
            <TableHead>Last Updated</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {spreadsheets.map((sheet) => (
            <TableRow key={sheet.id}>
              <TableCell className="font-medium">{sheet.title}</TableCell>
              <TableCell>{sheet.filename}</TableCell>
              <TableCell>{sheet.rows} rows × {sheet.columns} columns</TableCell>
              <TableCell className="text-gray-500 dark:text-gray-400">
                {sheet.lastUpdated.toLocaleDateString()}
              </TableCell>
              <TableCell>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-red-500 hover:text-red-700 dark:text-red-400 hover:dark:text-red-300"
                  onClick={() => onDelete(sheet.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}