import { Model } from "mongoose";
import { LoggerService } from "./logger.service";
import { LogDocument } from "./interfaces/log.interface";
export declare class LogsController {
    private readonly loggerService;
    private readonly logModel;
    constructor(loggerService: LoggerService, logModel: Model<LogDocument>);
    getAllLogs(level?: string, search?: string, startDate?: string, endDate?: string, page?: number, limit?: number): Promise<{
        logs: LogDocument[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    cleanupOldLogs(): Promise<{
        message: string;
        deletedCount: number;
    }>;
    addLog(body: {
        level: string;
        message: string;
        trace?: any;
    }): Promise<{
        message: string;
    }>;
}
