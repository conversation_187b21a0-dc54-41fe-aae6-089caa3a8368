import { Controller, Post, Get, Param, Body, UseGuards, Delete, Query } from '@nestjs/common';
import { HistoryService } from './history.service';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';

@ApiTags('History')
@Controller('history')
export class HistoryController {
  constructor(private readonly historyService: HistoryService) {}


  @Get()
  @UseGuards(JwtAuthGuard,RolesGuard)
  @ApiOperation({ summary: 'Get all history records with pagination' })
  @ApiResponse({ status: 200, description: 'List of history records' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for fullName or agent' })
  @ApiQuery({ name: 'filterType', required: false, description: 'Filter type: "all", "name", or "agent"' })
  findAll(@Query('page') page?: string, @Query('limit') limit?: string, @Query('search') search?: string, @Query('filterType') filterType: 'all' | 'name' | 'agent' = 'all') {
    // If limit is provided, use it; otherwise pass undefined to get all records
    const pageNum = page ? +page : undefined;
    const limitNum = limit ? +limit : undefined;
    return this.historyService.findAll(pageNum, limitNum, search, filterType);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard,RolesGuard)
  @ApiOperation({ summary: 'Get a history record by ID' })
  @ApiResponse({ status: 200, description: 'The requested history record' })
  findById(@Param('id') id: string) {
    return this.historyService.findById(id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: 'Delete a history record' })
  @ApiResponse({ status: 200, description: 'History record successfully deleted' })
  @ApiResponse({ status: 404, description: 'History record not found' })
  async delete(@Param('id') id: string) {
    return this.historyService.delete(id);
  }

}
