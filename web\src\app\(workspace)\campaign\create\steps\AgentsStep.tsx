/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */

import { useEffect, useMemo, useState } from "react";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import Image from "next/image";
import { Agent } from "@/app/(workspace)/agents/AgentsContent";

// Import agent avatar images
import agent<PERSON><PERSON> from "@/assets/img/Binghatti-Lisa.jpeg";

interface AgentsStepProps {
    data: {
      agents?: Agent[];
      agentId?: string;
    };
    updateData: (newData: Partial<{ agents?: Agent[], agentId?: string }>) => void;
    loading?: boolean;
    userRole?: string | null;
  }


export default function AgentsStep({ data, updateData, loading, userRole }: AgentsStepProps) {
  const [selectedAgent, setSelectedAgent] = useState<string | undefined>(data.agentId);


  // Handle agent selection
  const handleAgentSelection = (agentId: string) => {
    setSelectedAgent(agentId);
    updateData({ agentId });
  };
 
    // Filter agents based on user role
    const displayedAgents = useMemo(() => {
      if (!data.agents) return [];
      
      // If superadmin, show all agents
      if (userRole === 'superadmin') {
        return data.agents;
      }
      
      // Otherwise, only show active agents
      return data.agents.filter(agent => agent.status === 'active');
    }, [data.agents, userRole]);


  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Select an agent for this campaign</Label>
        <p className="text-sm text-muted-foreground">
          Choose one agent who will handle all the calls in this campaign
        </p>
      </div>

      {loading ? (
        <div className="text-center py-8">Loading agents...</div>
      ) : !data.agents || data.agents.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No agents available. Please create agents first.
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {displayedAgents.map((agent) => (
            <Card
              key={agent.id}
              onClick={() => handleAgentSelection(agent.id)}
              className={`border rounded-lg p-4 flex items-center gap-3 cursor-pointer transition-all ${
                selectedAgent === agent.id
                  ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                  : "border-gray-200 dark:border-gray-700"
              } hover:border-green-300 dark:hover:border-green-600`}
            >
              <Avatar className="h-12 w-12 flex-shrink-0">
              {agent.avatar ? (
                  <img 
                    src={agent.avatar}
                    alt={`${agent.name} avatar`}
                    className="object-cover h-full w-full"
                  />
                ) : (
                  <Image 
                    src={agentLisa}
                    alt={`${agent.name} avatar`}
                    width={64}
                    height={64}
                    className="object-cover h-full w-full"
                  />
                )}
                <AvatarFallback className="bg-gray-100">
                  {agent.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">
                  {agent.name}
                </p>
                <p className="text-xs text-muted-foreground">
                  {agent.role}
                </p>
              </div>
              {selectedAgent === agent.id && (
                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
              )}
            </Card>
          ))}
        </div>
      )}

      {selectedAgent && data.agents && (
        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md border border-green-200 dark:border-green-800">
          <p className="text-sm font-medium text-green-800 dark:text-green-400">
            Selected agent: {data.agents.find(a => a.id === selectedAgent)?.name}
          </p>
        </div>
      )}
    </div>
  );
}