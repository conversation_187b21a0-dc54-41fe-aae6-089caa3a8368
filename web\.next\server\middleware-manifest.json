{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tEM6TKGLe8j5cQrCriCePVJhjSUUbfs7lZlYOl4PwKc=", "__NEXT_PREVIEW_MODE_ID": "d7ad623b577ac5a5b0452e5ef382ffad", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "df248f1a40e59d972033b67dd69b7358b8521fc0ffcd80097ef617e2792b3628", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7cfc732824a3a03cfed202dd9d6547f4200ba2b6b34331465b51c06c166f5469"}}}, "sortedMiddleware": ["/"], "functions": {}}