import { Model } from "mongoose";
import { CampaignDto, UpdateCampaignStatusDto } from "./dto/campaign.dto";
import { CampaignDocument } from "./interfaces/campaign.interface";
import { ContactDocument } from "../contacts/interfaces/contact.interface";
import { ScheduledCallService } from "../scheduled-call/scheduled-call.service";
export declare class CampaignService {
    private readonly campaignModel;
    private readonly contactModel;
    private readonly scheduledCallService;
    private readonly logger;
    constructor(campaignModel: Model<CampaignDocument>, contactModel: Model<ContactDocument>, scheduledCallService: ScheduledCallService);
    create(createCampaignDto: CampaignDto): Promise<CampaignDocument>;
    findAll(statusFilter?: string): Promise<CampaignDocument[]>;
    findById(id: string): Promise<CampaignDocument>;
    update(id: string, updateCampaignDto: any): Promise<CampaignDocument>;
    updateStatus(id: string, statusDto: UpdateCampaignStatusDto): Promise<CampaignDocument>;
    remove(id: string): Promise<void>;
    search(term: string): Promise<CampaignDocument[]>;
    calculatePerformanceMetrics(id: string): Promise<any>;
    private scheduleCallsForCampaign;
    scheduleCallsForContactInCampaign(campaign: CampaignDocument, contact: ContactDocument): Promise<void>;
    scheduleCallsForContacts(campaign: CampaignDocument, contacts: ContactDocument[]): Promise<void>;
}
