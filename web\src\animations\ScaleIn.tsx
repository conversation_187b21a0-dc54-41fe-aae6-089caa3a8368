"use client";

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface ScaleInProps {
  children: ReactNode;
  duration?: number;
  delay?: number;
  className?: string;
  initialScale?: number;
  once?: boolean;
  viewOffset?: number;
}

export default function ScaleIn({
  children,
  duration = 0.5,
  delay = 0,
  initialScale = 0.9,
  className = "",
  once = true,
  viewOffset = 0.1,
}: ScaleInProps) {
  return (
    <motion.div
      initial={{
        scale: initialScale,
        opacity: 0,
      }}
      whileInView={{
        scale: 1,
        opacity: 1,
      }}
      transition={{
        duration: duration,
        delay: delay,
        ease: "easeOut",
      }}
      viewport={{ 
        once, 
        amount: viewOffset 
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}