"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhoneNumberController = void 0;
const common_1 = require("@nestjs/common");
const phone_number_service_1 = require("./phone-number.service");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
let PhoneNumberController = class PhoneNumberController {
    constructor(phoneNumberService) {
        this.phoneNumberService = phoneNumberService;
    }
    findAll() {
        return this.phoneNumberService.findAll();
    }
    findById(id) {
        return this.phoneNumberService.findById(id);
    }
};
exports.PhoneNumberController = PhoneNumberController;
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all phone numbers (syncs with VAPI)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns all phone numbers' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PhoneNumberController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get a phone number by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns a phone number' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Phone number not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PhoneNumberController.prototype, "findById", null);
exports.PhoneNumberController = PhoneNumberController = __decorate([
    (0, swagger_1.ApiTags)('Phone Numbers'),
    (0, common_1.Controller)('phone-numbers'),
    __metadata("design:paramtypes", [phone_number_service_1.PhoneNumberService])
], PhoneNumberController);
//# sourceMappingURL=phone-number.controller.js.map