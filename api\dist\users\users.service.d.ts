import { Model } from 'mongoose';
import { User, UserDocument } from './interfaces/user.interface';
import { UpdateUserDto } from './dto/update-user.dto';
import { GlobalSettingsService } from '../global-settings/global-settings.service';
import { OrganizationsService } from '../organizations/organizations.service';
export declare class UsersService {
    private userModel;
    private globalSettingsService;
    private organizationsService;
    constructor(userModel: Model<UserDocument>, globalSettingsService: GlobalSettingsService, organizationsService: OrganizationsService);
    create(userDto: any): Promise<User>;
    findAll(): Promise<User[]>;
    findOne(email: string): Promise<UserDocument | undefined>;
    findById(id: string): Promise<UserDocument>;
    findByName(fullName: string): Promise<UserDocument | null>;
    updateUser(id: string, updateDto: UpdateUserDto): Promise<UserDocument>;
    approveUser(id: string): Promise<UserDocument>;
    deleteUser(id: string): Promise<void>;
    hasSufficientCredits(userId: string, threshold?: number): Promise<boolean>;
    deductCredits(userId: string, amount: number): Promise<UserDocument>;
    getUserOrganization(userId: string): Promise<import("../organizations/interfaces/organization.interface").Organization>;
    getOrganizationsService(): OrganizationsService;
    getGlobalSettingsService(): GlobalSettingsService;
}
