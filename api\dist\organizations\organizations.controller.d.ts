import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto, UpdateOrganizationBillingDto } from './dto/organization.dto';
import { UpdateOrganizationSettingsDto } from './dto/update-organization-settings.dto';
import { UsersService } from '../users/users.service';
import { Model } from 'mongoose';
import { Organization } from './interfaces/organization.interface';
import { User } from '../users/interfaces/user.interface';
interface RequestWithUser {
    user: {
        userId: string;
        email: string;
        role: string;
    };
}
export declare class OrganizationsController {
    private readonly organizationsService;
    private readonly usersService;
    private organizationModel;
    private userModel;
    constructor(organizationsService: OrganizationsService, usersService: UsersService, organizationModel: Model<Organization>, userModel: Model<User>);
    create(createOrganizationDto: CreateOrganizationDto): Promise<Organization>;
    findAll(): Promise<Organization[]>;
    findMyOrganizations(req: RequestWithUser): Promise<Organization[]>;
    findOne(id: string, req: RequestWithUser): Promise<Organization>;
    getAvailableCredits(id: string, req: RequestWithUser): Promise<{
        freeCreditsRemaining: number;
        paidCredits: number;
        totalAvailable: number;
        usingFreeCredits: boolean;
    }>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto, req: RequestWithUser): Promise<Organization>;
    updateBilling(id: string, updateBillingDto: UpdateOrganizationBillingDto): Promise<Organization>;
    updateSettings(id: string, updateSettingsDto: UpdateOrganizationSettingsDto, req: RequestWithUser): Promise<Organization>;
    remove(id: string): Promise<{
        message: string;
    }>;
    addUser(id: string, userId: string, isAdmin: boolean, req: RequestWithUser): Promise<Organization>;
    removeUser(id: string, userId: string, req: RequestWithUser): Promise<Organization>;
    initializeMonthlyCredits(): Promise<{
        message: string;
        results: any[];
    }>;
    fixOrganizationUsers(): Promise<{
        message: string;
        results: any[];
    }>;
}
export {};
