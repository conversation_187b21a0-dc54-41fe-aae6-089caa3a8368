"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const path_1 = require("path");
const fs = __importStar(require("fs"));
let AgentService = class AgentService {
    constructor(agentModel) {
        this.agentModel = agentModel;
        this.VAPI_API_TOKEN = process.env.VAPI_API_TOKEN || '';
    }
    async create(createAgentDto) {
        try {
            const systemMessage = createAgentDto.model?.messages?.find(m => m.role === 'system');
            const systemContent = systemMessage?.content || '';
            const vapiPayload = {
                name: createAgentDto.name || '',
                firstMessage: createAgentDto.firstMessage || '',
                voicemailMessage: createAgentDto.voicemailMessage || '',
                model: {
                    provider: createAgentDto.model?.provider || 'openai',
                    model: createAgentDto.model?.model || 'gpt-4o',
                    maxTokens: createAgentDto.model?.maxTokens,
                    temperature: createAgentDto.model?.temperature,
                    messages: [
                        {
                            role: 'system',
                            content: systemContent
                        }
                    ]
                },
                server: {
                    url: createAgentDto?.server?.url || 'https://testurl.com',
                },
                backgroundDenoisingEnabled: createAgentDto.backgroundDenoisingEnabled || false,
            };
            let vapiAgent;
            try {
                const response = await fetch('https://api.vapi.ai/assistant', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(vapiPayload)
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`VAPI API request failed with status ${response.status}: ${errorText}`);
                    throw new Error(`Failed to create agent: ${errorText}`);
                }
                vapiAgent = await response.json();
            }
            catch (vapiError) {
                console.error('Error creating agent:', vapiError);
                throw vapiError;
            }
            const newAgent = new this.agentModel({
                ...createAgentDto,
                id: vapiAgent.id,
            });
            const savedAgent = await newAgent.save();
            return savedAgent;
        }
        catch (error) {
            console.error('Error in create method:', error);
            throw error;
        }
    }
    async findAll() {
        try {
            const response = await fetch('https://api.vapi.ai/assistant', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }
            const vapiAgents = await response.json();
            const existingAgents = await this.agentModel.find().exec();
            const vapiAgentIds = new Set(vapiAgents.map(agent => agent.id));
            for (const dbAgent of existingAgents) {
                if (!vapiAgentIds.has(dbAgent.id)) {
                    await this.agentModel.findByIdAndDelete(dbAgent._id).exec();
                }
            }
            for (const vapiAgent of vapiAgents) {
                const existingAgent = await this.agentModel.findOne({ id: vapiAgent.id }).exec();
                if (!existingAgent) {
                    const newAgent = new this.agentModel({
                        ...vapiAgent,
                        role: 'assistant',
                        avatar: null,
                        status: 'active'
                    });
                    await newAgent.save();
                }
                else {
                    await this.agentModel.findOneAndUpdate({ id: vapiAgent.id }, {
                        ...vapiAgent,
                        role: existingAgent.role || 'assistant',
                        avatar: existingAgent.avatar,
                        status: existingAgent.status || 'active'
                    }, { new: true }).exec();
                }
            }
            return this.agentModel.find().exec();
        }
        catch (error) {
            console.error('Error syncing agents:', error.message);
            return this.agentModel.find().exec();
        }
    }
    async findById(id) {
        let agent;
        try {
            agent = await this.agentModel.findById(id).exec();
        }
        catch (error) {
            agent = await this.agentModel.findOne({ id: id }).exec();
        }
        if (!agent) {
            throw new common_1.NotFoundException('Agent not found');
        }
        return agent;
    }
    async update(id, updateAgentDto) {
        try {
            const existingAgent = await this.agentModel.findOne({ id }).exec() ||
                await this.agentModel.findById(id).exec();
            if (!existingAgent) {
                throw new common_1.NotFoundException('Agent not found');
            }
            const vapiPayload = {
                name: updateAgentDto.name,
                firstMessage: updateAgentDto.firstMessage,
                voicemailMessage: updateAgentDto.voicemailMessage,
                model: {
                    provider: updateAgentDto.model?.provider,
                    model: updateAgentDto.model?.model,
                    maxTokens: updateAgentDto.model?.maxTokens,
                    temperature: updateAgentDto.model?.temperature,
                    messages: updateAgentDto.model?.messages
                },
                server: {
                    url: updateAgentDto.server?.url,
                },
                backgroundDenoisingEnabled: updateAgentDto.backgroundDenoisingEnabled,
            };
            if (existingAgent && existingAgent.id) {
                const response = await fetch(`https://api.vapi.ai/assistant/${existingAgent.id}`, {
                    method: 'PATCH',
                    headers: {
                        'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(vapiPayload)
                });
                if (!response.ok) {
                    console.error(`VAPI API request failed with status ${response.status}`);
                }
            }
            const updatedAgent = await this.agentModel
                .findByIdAndUpdate(id, updateAgentDto, { new: true })
                .exec();
            if (updatedAgent)
                return updatedAgent;
            const agentByUuid = await this.agentModel
                .findOneAndUpdate({ id: id }, updateAgentDto, { new: true })
                .exec();
            if (!agentByUuid)
                throw new common_1.NotFoundException('Agent not found');
            return agentByUuid;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException)
                throw error;
            try {
                const agentByUuid = await this.agentModel
                    .findOneAndUpdate({ id: id }, updateAgentDto, { new: true })
                    .exec();
                if (!agentByUuid)
                    throw new common_1.NotFoundException('Agent not found');
                return agentByUuid;
            }
            catch (secondError) {
                throw new common_1.NotFoundException('Agent not found');
            }
        }
    }
    async remove(id) {
        try {
            const response = await fetch(`https://api.vapi.ai/assistant/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.VAPI_API_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`VAPI API request failed with status ${response.status}`);
            }
            let result = await this.agentModel.findOneAndDelete({ id: id }).exec();
            if (!result) {
                result = await this.agentModel.findByIdAndDelete(id).exec();
            }
            if (!result) {
                throw new common_1.NotFoundException('Agent not found');
            }
        }
        catch (error) {
            console.error('Error deleting agent:', error);
            throw error;
        }
    }
    async getUploadedFiles() {
        try {
            const distUploadDir = (0, path_1.join)(__dirname, '../uploads');
            const rootUploadDir = (0, path_1.join)(__dirname, '../../uploads');
            let allFiles = [];
            if (fs.existsSync(distUploadDir)) {
                const distFiles = fs.readdirSync(distUploadDir)
                    .filter(file => {
                    const filePath = (0, path_1.join)(distUploadDir, file);
                    return fs.statSync(filePath).isFile() && !file.startsWith('.');
                })
                    .map(file => `/api/uploads/${file}`);
                allFiles = [...allFiles, ...distFiles];
            }
            if (fs.existsSync(rootUploadDir)) {
                const rootFiles = fs.readdirSync(rootUploadDir)
                    .filter(file => {
                    const filePath = (0, path_1.join)(rootUploadDir, file);
                    return fs.statSync(filePath).isFile() && !file.startsWith('.');
                })
                    .map(file => `/api/uploads/${file}`);
                rootFiles.forEach(file => {
                    if (!allFiles.includes(file)) {
                        allFiles.push(file);
                    }
                });
            }
            return {
                files: allFiles,
                count: allFiles.length
            };
        }
        catch (error) {
            console.error('Error listing uploaded files:', error);
            throw error;
        }
    }
    async getVolumeFiles() {
        try {
            const volumePath = '/usr/src/app/uploads';
            let volumeFiles = [];
            if (fs.existsSync(volumePath)) {
                volumeFiles = fs.readdirSync(volumePath)
                    .filter(file => {
                    const filePath = (0, path_1.join)(volumePath, file);
                    return fs.statSync(filePath).isFile() && !file.startsWith('.');
                })
                    .map(file => `/api/uploads/${file}`);
            }
            const accessibleFiles = await Promise.all(volumeFiles.map(async (file) => {
                try {
                    await fs.promises.access((0, path_1.join)(volumePath, file.split('/').pop()));
                    return file;
                }
                catch {
                    return null;
                }
            }));
            const validFiles = accessibleFiles.filter(file => file !== null);
            return {
                files: validFiles,
                count: validFiles.length,
                volumePath,
                isVolumeAccessible: fs.existsSync(volumePath)
            };
        }
        catch (error) {
            console.error('Error listing volume files:', error);
            return {
                files: [],
                count: 0,
                error: error.message,
                volumePath: '/usr/src/app/uploads',
                isVolumeAccessible: false
            };
        }
    }
};
exports.AgentService = AgentService;
exports.AgentService = AgentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Agent')),
    __metadata("design:paramtypes", [mongoose_2.Model])
], AgentService);
//# sourceMappingURL=agent.service.js.map