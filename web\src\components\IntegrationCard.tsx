"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader
} from "@/components/ui/card";
import { ExternalLink, Plus, RefreshCw, Trash2 } from "lucide-react";
import Image from "next/image";

type IntegrationProps = {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  status: "installed" | "not_installed" | "coming_soon";
  website?: string;
};

export function IntegrationCard({
  name,
  description,
  category,
  icon,
  status,
  website,
}: IntegrationProps) {
  return (
    <>
    <Card className="overflow-hidden">
      <CardHeader className="p-4 pb-0">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 relative rounded overflow-hidden">
              <Image 
                src={icon} 
                alt={`${name} icon`} 
                layout="fill" 
                objectFit="contain"
                unoptimized // For external URLs
              />
            </div>
            <div>
              <h3 className="font-medium">{name}</h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">{category}</p>
            </div>
          </div>
          {renderStatusBadge(status)}
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <p className="text-sm text-gray-600 dark:text-gray-300">{description}</p>
      </CardContent>
      <CardFooter className="p-4 pt-0 flex justify-between">
        {renderActionButton(status)}
        
        {website && (
          <Button variant="ghost" size="sm" asChild>
            <a href={website} target="_blank" rel="noreferrer" className="flex items-center">
              <ExternalLink className="h-4 w-4 mr-1" />
              Docs
            </a>
          </Button>
        )}
      </CardFooter>
    </Card>
    </>
  );
}

function renderStatusBadge(status: IntegrationProps["status"]) {
  if (status === "installed") {
    return (
      <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800">
        Installed
      </Badge>
    );
  }
  
  if (status === "coming_soon") {
    return (
      <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-800">
        Coming Soon
      </Badge>
    );
  }
  
  return null;
}

function renderActionButton(status: IntegrationProps["status"]) {
  if (status === "installed") {
    return (
      <div className="flex gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-1" />
          Update
        </Button>
        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/30">
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    );
  }
  
  if (status === "not_installed") {
    return (
      <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
        <Plus className="h-4 w-4 mr-1" />
        Install
      </Button>
    );
  }
  
  return (
    <Button size="sm" className="bg-gray-300 text-gray-600 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400" disabled>
      <Plus className="h-4 w-4 mr-1" />
      Install
    </Button>
  );
}