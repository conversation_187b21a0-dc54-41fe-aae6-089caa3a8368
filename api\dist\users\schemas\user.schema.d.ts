import { Schema, Types } from 'mongoose';
export declare const UserSchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
    email: string;
    password: string;
    fullName: string;
    isApproved: boolean;
    createdAt: NativeDate;
    updatedAt: NativeDate;
    role: "user" | "admin" | "superadmin";
    organizationId?: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    email: string;
    password: string;
    fullName: string;
    isApproved: boolean;
    createdAt: NativeDate;
    updatedAt: NativeDate;
    role: "user" | "admin" | "superadmin";
    organizationId?: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
}>> & import("mongoose").FlatRecord<{
    email: string;
    password: string;
    fullName: string;
    isApproved: boolean;
    createdAt: NativeDate;
    updatedAt: NativeDate;
    role: "user" | "admin" | "superadmin";
    organizationId?: {
        prototype?: Types.ObjectId;
        isValid?: {};
        cacheHexString?: unknown;
        generate?: {};
        createFromTime?: {};
        createFromHexString?: {};
        createFromBase64?: {};
    };
}> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
