"use client";

import { useTheme } from 'next-themes';
import Image from 'next/image'
import Link from 'next/link';
import React, { useEffect, useState } from 'react'
import { Button } from './ui/button';
import LogoBlack from '../assets/img/OROVA-PURPLE.png';
import LogoWhite from '../assets/img/OROVA-WHITE.png';
import { Moon, Sun } from 'lucide-react';



export default function Navigation() {

    const { theme, setTheme } = useTheme();
    const [mounted, setMounted] = useState(false);
  
    // Prevent hydration mismatch by rendering only after mount
    useEffect(() => {
      setMounted(true);
    }, []);
  
    // Toggle theme function
    const toggleTheme = () => {
      setTheme(theme === "dark" ? "light" : "dark");
    };
  
    if (!mounted) return null;

  return (
    <nav className="fixed w-full bg-white/80 dark:bg-gray-800 backdrop-blur-sm z-50 border-b dark:border-gray-800  duration-300">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between h-16">
        <div className="flex items-center">
          <div className="flex items-center gap-2">
            <Image 
              src={theme === "dark" ? LogoWhite : LogoBlack} 
              alt="Orova AI" 
              className="h-6.5 w-auto"
            />
          </div>
        </div>
        <div className="flex items-center space-x-4">
        <button 
              onClick={toggleTheme}
              className="p-2 rounded-lg text-gray-800 dark:text-white"
            >
              {theme === "dark" ? <Sun size={18} /> : <Moon size={18} />}
            </button>
          <Link href="/dashboard">
          <Button className=' hover:bg-[#383D73] hover:text-white dark:hover:bg-[#312E56] transition-all duration-200 hover:scale-110'>
              Dashboard</Button>
          </Link>
          <Link href="/login">
            <Button className="bg-gradient-to-r from-[#4157ea] to-[#a855f7] text-white cursor-pointer transition-all duration-200 hover:scale-110  ">
              Get Started
            </Button>
          </Link>
        </div>
      </div>
    </div>
  </nav>
  )
}
