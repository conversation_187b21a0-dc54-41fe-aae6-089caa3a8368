# FROM node:18

# WORKDIR /usr/src/app

# COPY package*.json ./

# RUN npm install --force

# COPY . .

# RUN npm run build

# CMD ["npm", "start"]


# Build stage
FROM node:18 AS builder

WORKDIR /app

# Copy package files and npmrc
COPY package*.json .npmrc ./

# Install system dependencies for Sharp
RUN apt-get update && apt-get install -y --no-install-recommends \
    libvips-dev \
    && rm -rf /var/lib/apt/lists/*

# Install dependencies with explicit platform binaries
RUN npm install --force && \
    # Install platform-specific dependencies for Tailwind CSS
    npm install --platform=linux --arch=x64 @tailwindcss/oxide-linux-x64-gnu && \
    # Install LightningCSS for Linux
    npm install --platform=linux --arch=x64 lightningcss-linux-x64-gnu --force  && \
    # Install Sharp with proper options
    npm install sharp --platform=linux --arch=x64 --force

# Copy the rest of the application
COPY . .

# Set environment variables for native modules
ENV SHARP_IGNORE_GLOBAL_LIBVIPS=1
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NEXT_TELEMETRY_DISABLED=1

# Build the application
RUN npm run build

# Production stage - using the same image to avoid binary compatibility issues
FROM node:18 AS runner

WORKDIR /app

# Copy necessary files from builder
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.ts ./

# Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Start the application
CMD ["npm", "start"]

