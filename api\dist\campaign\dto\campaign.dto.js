"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCampaignStatusDto = exports.CampaignDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CampaignDto {
}
exports.CampaignDto = CampaignDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Name of the user who created the campaign", required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CampaignDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Campaign name" }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CampaignDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CampaignDto.prototype, "contacts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Number of concurrent calls" }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CampaignDto.prototype, "concurrentCalls", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Daily cost of the campaign" }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CampaignDto.prototype, "dailyCost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Campaign start date" }),
    __metadata("design:type", Date)
], CampaignDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Campaign end date" }),
    __metadata("design:type", Date)
], CampaignDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Success rate percentage", required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CampaignDto.prototype, "successRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Overall sentiment of the campaign",
        enum: ["positive", "neutral", "negative"],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(["positive", "neutral", "negative"]),
    __metadata("design:type", String)
], CampaignDto.prototype, "sentiment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Campaign status",
        enum: ["active", "paused", "completed"],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(["active", "paused", "completed"]),
    __metadata("design:type", String)
], CampaignDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Interval in minutes between batches of scheduled calls",
        required: false,
        default: 3
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(60),
    __metadata("design:type", Number)
], CampaignDto.prototype, "batchIntervalMinutes", void 0);
class UpdateCampaignStatusDto {
}
exports.UpdateCampaignStatusDto = UpdateCampaignStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Campaign status",
        enum: ["active", "paused", "completed"],
    }),
    (0, class_validator_1.IsEnum)(["active", "paused", "completed"]),
    __metadata("design:type", String)
], UpdateCampaignStatusDto.prototype, "status", void 0);
//# sourceMappingURL=campaign.dto.js.map