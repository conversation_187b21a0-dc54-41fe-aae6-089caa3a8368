/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { useEffect, useState, useRef, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon, Search, X, Filter, RefreshCw } from "lucide-react";

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  trace?: string;
  _id: string;
}

interface LogsResponse {
  logs: LogEntry[];
  total: number;
  page: number;
  totalPages: number;
}

export default function LogsSettings() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [totalLogs, setTotalLogs] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [isFiltering, setIsFiltering] = useState<boolean>(false);
  const observer = useRef<IntersectionObserver | null>(null);
  const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

  const fetchLogs = async (pageNum = 1, reset = false) => {
    try {
      setIsFiltering(true);
      const token = localStorage.getItem("access_token");
      if (!token) {
        setError("No access token available");
        setLoading(false);
        setIsFiltering(false);
        return;
      }

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', pageNum.toString());
      params.append('limit', '20');

      if (levelFilter && levelFilter !== 'all') {
        params.append('level', levelFilter);
      }

      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }

      if (startDate) {
        params.append('startDate', startDate.toISOString().split('T')[0]);
      }

      if (endDate) {
        params.append('endDate', endDate.toISOString().split('T')[0]);
      }

      const response = await fetch(`${API_BASE_URL}/api/logs?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch logs");
      }

      const data: LogsResponse = await response.json();

      if (reset) {
        setLogs(data.logs);
      } else {
        setLogs(prev => [...prev, ...data.logs]);
      }

      setTotalLogs(data.total);
      setTotalPages(data.totalPages);
      setHasMore(pageNum < data.totalPages);
    } catch (err) {
      console.error("Error fetching logs:", err);
      setError("Failed to fetch logs. Please try again.");
    } finally {
      setLoading(false);
      setIsFiltering(false);
    }
  };

  const handleSearch = () => {
    setPage(1);
    fetchLogs(1, true);
  };

  const resetFilters = () => {
    setSearchQuery("");
    setLevelFilter("all");
    setStartDate(undefined);
    setEndDate(undefined);
    setPage(1);
    fetchLogs(1, true);
  };

  const cleanupOldLogs = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("access_token");
      if (!token) {
        setError("No access token available");
        setLoading(false);
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/logs/cleanup`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to cleanup logs");
      }

      const data = await response.json();
      alert(`Successfully deleted ${data.deletedCount} logs older than 4 days`);

      // Refresh logs after cleanup
      setPage(1);
      fetchLogs(1, true);
    } catch (err) {
      console.error("Error cleaning up logs:", err);
      setError("Failed to cleanup logs. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Setup intersection observer for infinite scrolling
  const lastLogElementRef = useCallback((node: HTMLTableRowElement) => {
    if (loading) return;

    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore && !isFiltering) {
        setPage(prevPage => prevPage + 1);
      }
    });

    if (node) observer.current.observe(node);
  }, [loading, hasMore, isFiltering]);

  useEffect(() => {
    fetchLogs(1, true);
  }, []);

  // Fetch more logs when page changes
  useEffect(() => {
    if (page > 1) {
      fetchLogs(page);
    }
  }, [page]);

  // Determine text color based on log level
  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "error":
        return "text-red-500";
      case "warn":
        return "text-yellow-500";
      case "info":
        return "text-green-500";
      default:
        return "text-gray-900";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Logs</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={cleanupOldLogs}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Clean Old Logs
        </Button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search logs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          />
        </div>

        <Select value={levelFilter || "all"} onValueChange={setLevelFilter}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            <SelectItem value="INFO">Info</SelectItem>
            <SelectItem value="WARN">Warning</SelectItem>
            <SelectItem value="ERROR">Error</SelectItem>
          </SelectContent>
        </Select>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="justify-start text-left font-normal">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {startDate ? format(startDate, 'PPP') : 'Start date'}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={startDate}
              onSelect={setStartDate}
              initialFocus
            />
          </PopoverContent>
        </Popover>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="justify-start text-left font-normal">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {endDate ? format(endDate, 'PPP') : 'End date'}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={endDate}
              onSelect={setEndDate}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="flex gap-2">
        <Button onClick={handleSearch} disabled={loading || isFiltering}>
          <Filter className="h-4 w-4 mr-2" />
          Apply Filters
        </Button>
        <Button variant="outline" onClick={resetFilters} disabled={loading || isFiltering}>
          <X className="h-4 w-4 mr-2" />
          Reset
        </Button>
      </div>

      {/* Log count summary */}
      {!loading && logs.length > 0 && (
        <div className="text-sm text-gray-500">
          Showing {logs.length} of {totalLogs} logs {levelFilter && levelFilter !== 'all' && `(filtered by ${levelFilter})`}
          {totalPages > 1 && ` - Page ${page} of ${totalPages}`}
        </div>
      )}

      {/* Logs Table */}
      {loading && page === 1 ? (
        <div className="flex justify-center items-center h-40">
          <p>Loading logs...</p>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>Error: {error}</p>
        </div>
      ) : logs.length === 0 ? (
        <div className="bg-gray-50 border border-gray-200 text-gray-700 px-4 py-8 rounded text-center">
          <p>No logs found.</p>
        </div>
      ) : (
        <div className="overflow-x-auto border rounded-md">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Level
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Message
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trace
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {logs.map((log, index) => (
                <tr
                  key={log._id || index}
                  ref={index === logs.length - 5 ? lastLogElementRef : null}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {new Date(log.timestamp).toLocaleString()}
                  </td>
                  <td className={`px-4 py-3 text-sm ${getLevelColor(log.level)}`}>
                    <span className="px-2 py-1 rounded-full text-xs font-medium">
                      {log.level}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900 max-w-md truncate">
                    {log.message}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate">
                    {log.trace || "-"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Loading indicator for infinite scroll */}
          {(loading && page > 1) && (
            <div className="py-4 text-center text-gray-500">
              Loading more logs...
            </div>
          )}

          {/* End of results message */}
          {!hasMore && logs.length > 0 && (
            <div className="py-4 text-center text-gray-500">
              End of logs
            </div>
          )}
        </div>
      )}
    </div>
  );
}
