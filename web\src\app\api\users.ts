const API_BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

export type User = {
  _id: string;
  email: string;
  fullName: string;
  isApproved: boolean;
  organizationId?: string;
  credits?: number;
  autoRechargeEnabled?: boolean;
  autoRechargeThreshold?: number;
  autoRechargeAmount?: number;
  createdAt: string;
  role?: string;
};

export type NewUser = {
  fullName: string;
  email: string;
  password: string;
};

export type UserUpdateData = {
  fullName?: string;
  email?: string;
  password?: string;
  isApproved?: boolean;
  credits?: number;
  organizationId?: string;
};

export type AutoRechargeSettings = {
  autoRechargeEnabled: boolean;
  autoRechargeThreshold: number;
  autoRechargeAmount: number;
};

/**
 * Fetch all users
 */
export async function fetchUsers(): Promise<User[]> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users`, {
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });

    if (!response.ok) throw new Error('Failed to fetch users');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
}

/**
 * Get all users (simplified function for components)
 */
export const getUsers = async (): Promise<User[]> => {
  const token = localStorage.getItem('access_token');

  if (!token) {
    throw new Error("No access token available");
  }

  const response = await fetch(`${API_BASE_URL}/api/users`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch users');
  }

  return response.json();
};

/**
 * Create a new user
 */
export async function addUser(userData: NewUser): Promise<void> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(errorText || 'Failed to add user');
    }

    return;
  } catch (error) {
    console.error('Error adding user:', error);
    throw error;
  }
}

/**
 * Update existing user
 */
export async function updateUser(userId: string, updateData: UserUpdateData): Promise<User> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) throw new Error('Failed to update user');

    return await response.json();
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
}

/**
 * Delete a user
 */
export async function deleteUser(userId: string): Promise<void> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) throw new Error('Failed to delete user');
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
}

/**
 * Approve a user
 */
export async function approveUser(userId: string): Promise<User> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/approve/${userId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) throw new Error('Failed to approve user');

    return await response.json();
  } catch (error) {
    console.error('Error approving user:', error);
    throw error;
  }
}

/**
 * Revoke user access
 */
export async function revokeUserAccess(userId: string): Promise<User> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/${userId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ isApproved: false }),
    });

    if (!response.ok) throw new Error('Failed to revoke user access');

    return await response.json();
  } catch (error) {
    console.error('Error revoking user access:', error);
    throw error;
  }
}

/**
 * User credits response interface
 */
export interface UserCreditsResponse {
  // Legacy fields for backward compatibility
  credits: number;
  minutes: number;
  // New monthly credits fields
  freeCreditsRemaining: number;
  paidCredits: number;
  totalAvailable: number;
  usingFreeCredits: boolean;
  freeMinutesRemaining: number;
  paidMinutes: number;
  totalMinutesAvailable: number;
  // Common fields
  callPricePerMinute: number;
  monthlyResetDate: number;
  monthlyAllowance: number;
  minimumCreditsThreshold: number;
}

/**
 * Get current user credits
 */
export async function getUserCredits(): Promise<UserCreditsResponse> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/me/credits`, {
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });

    if (!response.ok) throw new Error('Failed to fetch user credits');
    const data = await response.json();
    return {
      // Legacy fields for backward compatibility
      credits: data.credits || 0,
      minutes: data.minutes || 0,
      // New monthly credits fields
      freeCreditsRemaining: data.freeCreditsRemaining || 0,
      paidCredits: data.paidCredits || 0,
      totalAvailable: data.totalAvailable || 0,
      usingFreeCredits: data.usingFreeCredits || false,
      freeMinutesRemaining: data.freeMinutesRemaining || 0,
      paidMinutes: data.paidMinutes || 0,
      totalMinutesAvailable: data.totalMinutesAvailable || 0,
      // Common fields
      callPricePerMinute: data.callPricePerMinute || 0.1,
      monthlyResetDate: data.monthlyResetDate || 1,
      monthlyAllowance: data.monthlyAllowance || 0,
      minimumCreditsThreshold: data.minimumCreditsThreshold || 1.0
    };
  } catch (error) {
    console.error('Error fetching user credits:', error);
    return {
      // Legacy fields for backward compatibility
      credits: 0,
      minutes: 0,
      // New monthly credits fields
      freeCreditsRemaining: 0,
      paidCredits: 0,
      totalAvailable: 0,
      usingFreeCredits: false,
      freeMinutesRemaining: 0,
      paidMinutes: 0,
      totalMinutesAvailable: 0,
      // Common fields
      callPricePerMinute: 0.1,
      monthlyResetDate: 1,
      monthlyAllowance: 0,
      minimumCreditsThreshold: 1.0
    }; // Return defaults if there's an error
  }
}

/**
 * Update user credits
 */
export async function updateUserCredits(amount: number): Promise<number> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/me/credits`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ amount }),
    });

    if (!response.ok) throw new Error('Failed to update user credits');

    const data = await response.json();
    return data.credits;
  } catch (error) {
    console.error('Error updating user credits:', error);
    throw error;
  }
}

/**
 * Get auto-recharge settings
 */
export async function getAutoRechargeSettings(): Promise<AutoRechargeSettings> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/me/auto-recharge`, {
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });

    if (!response.ok) throw new Error('Failed to fetch auto-recharge settings');
    return await response.json();
  } catch (error) {
    console.error('Error fetching auto-recharge settings:', error);
    // Return default settings if there's an error
    return {
      autoRechargeEnabled: false,
      autoRechargeThreshold: 1.0,
      autoRechargeAmount: 0
    };
  }
}

/**
 * Update auto-recharge settings
 */
export async function updateAutoRechargeSettings(settings: Partial<AutoRechargeSettings>): Promise<AutoRechargeSettings> {
  try {
    // Get token from localStorage
    const token = localStorage.getItem('access_token');

    if (!token) {
      console.error("No access token available");
      throw new Error("No access token available");
    }

    const response = await fetch(`${API_BASE_URL}/api/users/me/auto-recharge`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(settings),
    });

    if (!response.ok) throw new Error('Failed to update auto-recharge settings');

    const data = await response.json();
    return data.settings;
  } catch (error) {
    console.error('Error updating auto-recharge settings:', error);
    throw error;
  }
}

