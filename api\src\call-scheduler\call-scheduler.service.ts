import { Injectable, Logger } from "@nestjs/common";
import { Cron, CronExpression } from "@nestjs/schedule";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { ScheduledCallService } from "src/scheduled-call/scheduled-call.service";
import { VapiService } from "src/vapi/vapi.service";
import { ContactDocument } from "src/contacts/interfaces/contact.interface";
import { CampaignDocument } from "src/campaign/interfaces/campaign.interface";
import { UsersService } from "src/users/users.service";
@Injectable()
export class CallSchedulerService {
  private readonly logger = new Logger(CallSchedulerService.name);
  // Map to track recently processed contacts to prevent duplicate calls
  private recentlyProcessedContacts = new Map<string, number>();
  // Flag to prevent concurrent execution of the cron job
  private isProcessingCalls = false;

  constructor(
    private readonly scheduledCallService: ScheduledCallService,
    private readonly vapiService: VapiService,
    private readonly usersService: UsersService,
    @InjectModel("Contact")
    private readonly contactModel: Model<ContactDocument>,
    @InjectModel("Campaign")
    private readonly campaignModel: Model<CampaignDocument>
  ) {}

  private async checkCampaignStatus(
    contactName: string,
    phoneNumber: string
  ): Promise<{
    isActive: boolean;
    campaignName?: string;
    campaignId?: string;
  }> {
    try {
      const contact = await this.contactModel
        .findOne({
          contactName: contactName,
          phoneNumber: phoneNumber,
        })
        .exec();

      if (!contact || !contact.campaigns || contact.campaigns.length === 0) {
        this.logger.warn(`No campaigns found for contact ${contactName}`);
        return { isActive: true };
      }

      // Get the first campaign associated with the contact
      const campaignId = contact.campaigns[0];
      const campaign = await this.campaignModel.findById(campaignId).exec();

      if (!campaign) {
        this.logger.warn(
          `Campaign ${campaignId} not found for contact ${contactName}`
        );
        return { isActive: true }; // Default to active if campaign not found
      }

      // Check if the campaign is active
      const isActive = campaign.status === "active";

      if (!isActive) {
        this.logger.log(
          `Campaign ${campaign.name} (${campaignId}) is ${campaign.status}, not initiating call for ${contactName}`
        );
      }

      return {
        isActive,
        campaignName: campaign.name,
        campaignId: campaign._id.toString(),
      };
    } catch (error) {
      this.logger.error(
        `Error checking campaign status for contact ${contactName}:`,
        error.message
      );
      return { isActive: true }; // Default to active on error
    }
  }

  /**
   * Clean up the recently processed contacts map to prevent memory leaks
   * Removes entries older than 30 seconds
   */
  private cleanupRecentlyProcessedContacts() {
    const now = Date.now();
    const ONE_MINUTES = 30 * 1000; // 30 seconds

    for (const [key, timestamp] of this.recentlyProcessedContacts.entries()) {
      if (now - timestamp > ONE_MINUTES) {
        this.recentlyProcessedContacts.delete(key);
      }
    }
  }

  /**
   * Check for and recover any calls that got stuck in 'processing' status
   * This runs every 10 minutes to find and reset stuck calls
   */
  @Cron(CronExpression.EVERY_10_MINUTES)
  async recoverStuckCalls() {
    this.logger.log("Checking for stuck calls in processing status...");

    try {
      // Get calls that have been stuck in processing for more than 10 minutes
      const stuckCalls = await this.scheduledCallService.getStuckProcessingCalls(10);

      if (!stuckCalls || stuckCalls.length === 0) {
        this.logger.log("No stuck calls found");
        return;
      }

      this.logger.log(`Found ${stuckCalls.length} calls stuck in processing status`);

      // Reset these calls back to pending status with incremented retry count
      for (const call of stuckCalls) {
        const currentRetryCount = call.retryCount || 0;

        // If we've tried too many times, mark as failed
        if (currentRetryCount >= 3) {
          await this.scheduledCallService.updateScheduledCall(
            call._id as string,
            {
              status: "failed",
              retryCount: currentRetryCount + 1
            }
          );
          this.logger.log(`Marked call ${call._id} as failed after ${currentRetryCount + 1} attempts`);
        } else {
          // Otherwise, reset to pending for another try
          await this.scheduledCallService.updateScheduledCall(
            call._id as string,
            {
              status: "pending",
              retryCount: currentRetryCount + 1
            }
          );
          this.logger.log(`Reset stuck call ${call._id} to pending (attempt ${currentRetryCount + 1})`);
        }
      }
    } catch (error) {
      this.logger.error("Error recovering stuck calls:", error.message);
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleScheduledCalls() {
    // Prevent concurrent execution of the cron job
    if (this.isProcessingCalls) {
      this.logger.log("Previous call processing job still running, skipping this execution");
      return;
    }

    this.isProcessingCalls = true;
    console.log("Checking for scheduled calls due for execution...");
    const now = new Date();

    try {
      // Clean up old entries from the recently processed contacts map
      this.cleanupRecentlyProcessedContacts();

      const scheduledCalls =
        await this.scheduledCallService.getScheduledCallsByStatusAndTime(
          "pending",
          now
        );

      // If no calls are scheduled, return early
      if (!scheduledCalls || scheduledCalls.length === 0) {
        this.isProcessingCalls = false;
        return;
      }

      this.logger.log(`Found ${scheduledCalls.length} scheduled calls due for execution`);

      // Process calls in batches of 5 with a 5-second delay between batches
      const BATCH_SIZE = 5;
      const BATCH_DELAY_MS = 5000; // 5 seconds

      // Deduplicate scheduled calls by contact
      const uniqueCallsMap = new Map();

      for (const call of scheduledCalls) {
        if (call.contacts && call.contacts.length > 0) {
          const contact = call.contacts[0];
          const contactKey = `${contact.Name}:${contact.MobileNumber}`;

          // Skip if we've already processed this contact recently (within the last 30 seconds)
          if (this.recentlyProcessedContacts.has(contactKey)) {
            this.logger.log(`Skipping duplicate call for ${contact.Name} - already processed recently`);

            // Mark as executed to prevent future processing
            await this.scheduledCallService.updateScheduledCall(
              call._id as string,
              { status: "executed" }
            );
            continue;
          }

          // If we have multiple scheduled calls for the same contact, keep only the earliest one
          if (!uniqueCallsMap.has(contactKey) ||
              new Date(call.scheduledTime) < new Date(uniqueCallsMap.get(contactKey).scheduledTime)) {
            uniqueCallsMap.set(contactKey, call);
          }
        } else {
          // If there are no contacts, just use the call ID as the key
          uniqueCallsMap.set(call._id.toString(), call);
        }
      }

      // Convert back to array
      const uniqueScheduledCalls = Array.from(uniqueCallsMap.values());

      this.logger.log(`After deduplication: ${uniqueScheduledCalls.length} unique calls to process`);

      // Create batches of scheduled calls
      const batches = [];
      for (let i = 0; i < uniqueScheduledCalls.length; i += BATCH_SIZE) {
        batches.push(uniqueScheduledCalls.slice(i, i + BATCH_SIZE));
      }

      this.logger.log(`Processing calls in ${batches.length} batches of up to ${BATCH_SIZE} calls each`);

      // Process each batch with a delay between batches
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        this.logger.log(`Processing batch ${batchIndex + 1} of ${batches.length} with ${batch.length} calls`);

        // Process all calls in this batch
        for (const scheduledCall of batch) {
          try {
            if (scheduledCall.contacts && scheduledCall.contacts.length > 0) {
              const firstContact = scheduledCall.contacts[0];
              const contactKey = `${firstContact.Name}:${firstContact.MobileNumber}`;

              // Double-check that this contact hasn't been processed while we were working on other batches
              if (this.recentlyProcessedContacts.has(contactKey)) {
                this.logger.log(`Skipping call for ${firstContact.Name} - processed by another batch`);

                // Mark as executed to prevent future processing
                await this.scheduledCallService.updateScheduledCall(
                  scheduledCall._id as string,
                  { status: "executed" }
                );
                continue;
              }

              const { isActive, campaignName } = await this.checkCampaignStatus(
                firstContact.Name,
                firstContact.MobileNumber
              );

              if (!isActive) {
                await this.scheduledCallService.updateScheduledCall(
                  scheduledCall._id as string,
                  { status: "cancelled" }
                );
                this.logger.log(
                  `Scheduled call for ${firstContact.Name} cancelled because campaign ${campaignName} is not active`
                );
                continue;
              }

              // Check if there are sufficient credits before making the call
              const hasSufficientCredits = await this.usersService.hasSufficientCredits('system', 1);

              if (!hasSufficientCredits) {
                await this.scheduledCallService.updateScheduledCall(
                  scheduledCall._id as string,
                  { status: "failed" }
                );
                this.logger.error(
                  `Scheduled call for ${firstContact.Name} failed due to insufficient credits`
                );
                continue;
              }

              // Mark this contact as recently processed BEFORE making the call
              this.recentlyProcessedContacts.set(contactKey, Date.now());

              // Log that we're about to make the call
              this.logger.log(`Initiating call for ${firstContact.Name} (${contactKey})`);
            }

            // First update status to "processing" to prevent race conditions
            // Also record the time we started processing
            await this.scheduledCallService.updateScheduledCall(
              scheduledCall._id as string,
              {
                status: "processing",
                lastProcessedAt: new Date()
              }
            );

            try {
              await this.vapiService.callContacts(
                scheduledCall.contacts,
                scheduledCall.agentId,
                scheduledCall.region
              );

              // Deduct credits for the call (1 credit per call)
              await this.usersService.deductCredits('system', 1);

              // Only update to executed if the call was successful
              await this.scheduledCallService.updateScheduledCall(
                scheduledCall._id as string,
                { status: "executed" }
              );
            } catch (error) {
              // If the call failed, update the status back to "pending" with a retry count
              // This prevents the call from being lost if it failed due to temporary issues
              await this.scheduledCallService.updateScheduledCall(
                scheduledCall._id as string,
                { status: "failed" }
              );
              throw error; // Re-throw to be caught by the outer try-catch
            }

            if (scheduledCall.contacts && scheduledCall.contacts.length > 0) {
              this.logger.log(
                `Successfully executed scheduled call for ${scheduledCall.contacts[0].Name}`
              );
            } else {
              this.logger.log(
                `Successfully executed scheduled call for agent ${scheduledCall.agentId}`
              );
            }
          } catch (error) {
            await this.scheduledCallService.updateScheduledCall(
              scheduledCall._id as string,
              { status: "failed" }
            );

            if (scheduledCall.contacts && scheduledCall.contacts.length > 0) {
              this.logger.error(
                `Failed to initiate call for ${scheduledCall.contacts[0].Name}`,
                error.message
              );
            } else {
              this.logger.error(
                `Failed to initiate call for agent ${scheduledCall.agentId}`,
                error.message
              );
            }
          }
        }

        // If this is not the last batch, wait before processing the next batch
        if (batchIndex < batches.length - 1) {
          this.logger.log(`Waiting ${BATCH_DELAY_MS}ms before processing next batch`);
          await new Promise(resolve => setTimeout(resolve, BATCH_DELAY_MS));
        }
      }
    } catch (error) {
      this.logger.error("Error checking scheduled calls:", error.message);
    } finally {
      // Always reset the processing flag when done
      this.isProcessingCalls = false;
    }
  }
}
