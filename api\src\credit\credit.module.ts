import { Module, NestModule, MiddlewareConsumer, forwardRef } from '@nestjs/common';
import { CreditCheckMiddleware } from './credit.middleware';
import { UsersModule } from 'src/users/users.module';
import { GlobalSettingsModule } from 'src/global-settings/global-settings.module';
import { BillingModule } from 'src/billing/billing.module';
import { MongooseModule } from '@nestjs/mongoose';
import { UserSchema } from 'src/users/schemas/user.schema';
import { AutoRechargeService } from './auto-recharge.service';
import { OrganizationsModule } from 'src/organizations/organizations.module';
import { CreditGateway } from './credit.gateway';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    GlobalSettingsModule,
    forwardRef(() => BillingModule),
    forwardRef(() => OrganizationsModule),
    MongooseModule.forFeature([{ name: 'User', schema: UserSchema }]),
  ],
  providers: [CreditCheckMiddleware, AutoRechargeService, CreditGateway],
  exports: [CreditCheckMiddleware, AutoRechargeService, CreditGateway],
})
export class CreditModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CreditCheckMiddleware)
      .forRoutes('*'); // Apply to all routes
  }
}
