import { Schema } from "mongoose";
export declare const HistorySchema: Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
    id: false;
}, {
    fullName: string;
    createdAt: NativeDate;
    updatedAt: NativeDate;
    mobileNumber: string;
    timezone: string;
    callRoute: string;
    callPurpose: string;
    bookedStatus: string;
    confirmedStatus: string;
    additionalQuestions: string;
    emotions: "Positive" | "Neutral" | "Slightly Positive" | "Slightly Negative" | "Negative";
    interest?: string;
    callTranscript?: string;
    callSummary?: string;
    callStartTime?: NativeDate;
    callEndTime?: NativeDate;
    callDuration?: string;
    callEndReason?: string;
    callCost?: string;
    recordingUrl?: string;
    preferredProject?: string;
    preferredLocation?: string;
    preferredUnitType?: string;
    projectType?: string;
    investmentType?: string;
    budget?: string;
    recentContact?: boolean;
    agent?: string;
    brokenPromise?: string;
    callBackLanguage?: string;
    callBackRequest?: string;
    claimedPaidAwaitingPOP?: string;
    doNotCall?: string;
    followingPaymentPlan?: string;
    fullyPaid?: string;
    fullyPaidByPDC?: string;
    incorrectContactDetails?: string;
    mortgage?: string;
    notResponding?: string;
    notRespondingSOASent?: string;
    notWillingToPay?: string;
    popRaised?: string;
    promiseToPay?: string;
    promiseToPayPartial?: string;
    refuseToPay?: string;
    thirdParty?: string;
    willingToPay?: string;
    Response?: string;
    Channel?: string;
    GuestRequest?: string;
    Notes?: string;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    fullName: string;
    createdAt: NativeDate;
    updatedAt: NativeDate;
    mobileNumber: string;
    timezone: string;
    callRoute: string;
    callPurpose: string;
    bookedStatus: string;
    confirmedStatus: string;
    additionalQuestions: string;
    emotions: "Positive" | "Neutral" | "Slightly Positive" | "Slightly Negative" | "Negative";
    interest?: string;
    callTranscript?: string;
    callSummary?: string;
    callStartTime?: NativeDate;
    callEndTime?: NativeDate;
    callDuration?: string;
    callEndReason?: string;
    callCost?: string;
    recordingUrl?: string;
    preferredProject?: string;
    preferredLocation?: string;
    preferredUnitType?: string;
    projectType?: string;
    investmentType?: string;
    budget?: string;
    recentContact?: boolean;
    agent?: string;
    brokenPromise?: string;
    callBackLanguage?: string;
    callBackRequest?: string;
    claimedPaidAwaitingPOP?: string;
    doNotCall?: string;
    followingPaymentPlan?: string;
    fullyPaid?: string;
    fullyPaidByPDC?: string;
    incorrectContactDetails?: string;
    mortgage?: string;
    notResponding?: string;
    notRespondingSOASent?: string;
    notWillingToPay?: string;
    popRaised?: string;
    promiseToPay?: string;
    promiseToPayPartial?: string;
    refuseToPay?: string;
    thirdParty?: string;
    willingToPay?: string;
    Response?: string;
    Channel?: string;
    GuestRequest?: string;
    Notes?: string;
}>> & import("mongoose").FlatRecord<{
    fullName: string;
    createdAt: NativeDate;
    updatedAt: NativeDate;
    mobileNumber: string;
    timezone: string;
    callRoute: string;
    callPurpose: string;
    bookedStatus: string;
    confirmedStatus: string;
    additionalQuestions: string;
    emotions: "Positive" | "Neutral" | "Slightly Positive" | "Slightly Negative" | "Negative";
    interest?: string;
    callTranscript?: string;
    callSummary?: string;
    callStartTime?: NativeDate;
    callEndTime?: NativeDate;
    callDuration?: string;
    callEndReason?: string;
    callCost?: string;
    recordingUrl?: string;
    preferredProject?: string;
    preferredLocation?: string;
    preferredUnitType?: string;
    projectType?: string;
    investmentType?: string;
    budget?: string;
    recentContact?: boolean;
    agent?: string;
    brokenPromise?: string;
    callBackLanguage?: string;
    callBackRequest?: string;
    claimedPaidAwaitingPOP?: string;
    doNotCall?: string;
    followingPaymentPlan?: string;
    fullyPaid?: string;
    fullyPaidByPDC?: string;
    incorrectContactDetails?: string;
    mortgage?: string;
    notResponding?: string;
    notRespondingSOASent?: string;
    notWillingToPay?: string;
    popRaised?: string;
    promiseToPay?: string;
    promiseToPayPartial?: string;
    refuseToPay?: string;
    thirdParty?: string;
    willingToPay?: string;
    Response?: string;
    Channel?: string;
    GuestRequest?: string;
    Notes?: string;
}> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
