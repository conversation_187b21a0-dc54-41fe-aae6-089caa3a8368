"use client";

import React from "react";
import { useCredits } from "@/contexts/CreditContext";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import ScaleIn from "@/animations/ScaleIn";

export function CreditDisplay() {
  const {
    totalMinutesAvailable,
    monthlyAllowance,
    isLoading,
    totalAvailable,
    organizationCreditThreshold,
    isConnected,
    hasValidData,
    lastSuccessfulFetch
  } = useCredits();

  // Determine if credits are low (only if we have valid data)
  const isLowCredit = hasValidData && totalAvailable < organizationCreditThreshold;
  const isWarningCredit = hasValidData && totalAvailable < (organizationCreditThreshold * 2) && totalAvailable >= organizationCreditThreshold;

  // Check if data is stale
  const isDataStale = lastSuccessfulFetch && (Date.now() - lastSuccessfulFetch > 2 * 60 * 1000); // 2 minutes
  const showConnectionWarning = !isConnected || isDataStale;

  return (
    <ScaleIn delay={0.2}>
      <div className="mx-1 my-3 transition-all duration-800 ease-in-out delay-1000">
        <div className={`bg-white dark:bg-gray-800 border rounded-lg p-4 transform transition-all duration-500 ease-in-out ${
          isLowCredit
            ? 'border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/10'
            : isWarningCredit
              ? 'border-yellow-300 dark:border-yellow-700 bg-white dark:bg-yellow-900/10'
              : 'border-gray-200 dark:border-gray-700'
        }`}>
          {/* Header */}
          <div className="flex justify-between items-center mb-3">
            <div className="flex items-center gap-2">
              <h3 className="text-xs font-medium">Credits</h3>
              {showConnectionWarning && (
                <div className="w-2 h-2 rounded-full bg-orange-400 animate-pulse" title="Connection issue - data may be outdated" />
              )}
            </div>
            {(isLowCredit || isWarningCredit) && (
              <p className={` text-xs font-medium ${
                isLowCredit ? 'text-red-500' : 'text-yellow-500'
              }`} >
                {
                isLowCredit ? 'Low Credits' : 'Warning'
              }
              </p>
            )}
          </div>
          <div className="mb-3">
            <div className="flex items-center justify-between mb-1">
              <span className={`text-xs ${
                isLowCredit
                  ? 'text-red-600 dark:text-red-400'
                  : isWarningCredit
                    ? 'text-yellow-600 dark:text-yellow-400'
                    : 'text-green-600 dark:text-green-400'
              }`}>
                Minutes Remaining
              </span>
              <span className={`text-sm font-semibold ${
                isLowCredit
                  ? 'text-red-600 dark:text-red-400'
                  : isWarningCredit
                    ? 'text-yellow-600 dark:text-yellow-400'
                    : 'text-green-600 dark:text-green-400'
              }`}>
                {isLoading ? "..." : `${totalMinutesAvailable.toFixed(0)} min`}
              </span>
            </div>
            <div className="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full ${
                  isLowCredit
                    ? 'bg-red-500'
                    : isWarningCredit
                      ? 'bg-yellow-500'
                      : 'bg-green-500'
                }`}
                style={{
                  width: `${Math.min(
                    100,
                    (totalMinutesAvailable / Math.max(monthlyAllowance, 1)) *
                      100
                  )}%`,
                }}
              ></div>
            </div>
          </div>

          <Link href="/billing" className="block">
            <Button
              variant="default"
              className="w-full bg-black hover:bg-gray-800 dark:bg-gray-900 dark:hover:bg-gray-800 text-white text-xs py-1 h-8"
            >
              Recharge
            </Button>
          </Link>
        </div>
      </div>
    </ScaleIn>
  );
}
