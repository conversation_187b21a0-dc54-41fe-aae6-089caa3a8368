/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

// API functions
const API_URL = process.env.NEXT_PUBLIC_SERVER_URL || "";

interface AutoRechargeSettings {
  autoRechargeEnabled: boolean;
  autoRechargeThreshold: number;
  autoRechargeAmount: number;
}

async function getAutoRechargeSettings(): Promise<AutoRechargeSettings> {
  const response = await fetch(`${API_URL}/api/users/me/auto-recharge`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${localStorage.getItem("access_token")}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to fetch auto-recharge settings");
  }

  return response.json();
}

async function updateAutoRechargeSettings(
  settings: Partial<AutoRechargeSettings>
): Promise<{ message: string; settings: AutoRechargeSettings }> {
  const response = await fetch(`${API_URL}/api/users/me/auto-recharge`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${localStorage.getItem("access_token")}`,
    },
    body: JSON.stringify(settings),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to update auto-recharge settings");
  }

  return response.json();
}

export function AutoRechargeSettings() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [autoRechargeEnabled, setAutoRechargeEnabled] = useState(false);
  const [autoRechargeThreshold, setAutoRechargeThreshold] = useState(1.0);
  const [autoRechargeAmount, setAutoRechargeAmount] = useState(0);

  // Fetch auto-recharge settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const data = await getAutoRechargeSettings();
        setAutoRechargeEnabled(data.autoRechargeEnabled);
        setAutoRechargeThreshold(data.autoRechargeThreshold);
        setAutoRechargeAmount(data.autoRechargeAmount);
      } catch (error) {
        console.error("Failed to fetch auto-recharge settings:", error);
        toast.error("Failed to load auto-recharge settings. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleSave = async () => {
    try {
      setIsSaving(true);

      // Validate inputs
      if (autoRechargeThreshold < 0) {
        throw new Error("Threshold cannot be negative");
      }

      // Update settings
      const response = await updateAutoRechargeSettings({
        autoRechargeEnabled,
        autoRechargeThreshold,
      });

      toast.success("Auto-recharge settings updated successfully");
    } catch (error: any) {
      console.error("Failed to update auto-recharge settings:", error);
      toast.error(error.message || "Failed to update auto-recharge settings");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Auto-Recharge Settings</CardTitle>
        <CardDescription>
          Automatically add funds when your balance falls below a threshold
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-6">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Auto-Recharge</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically add funds when your balance is low
                </p>
              </div>
              <Switch
                checked={autoRechargeEnabled}
                onCheckedChange={setAutoRechargeEnabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount to reload ($)</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="10"
                value={autoRechargeAmount}
                onChange={(e) =>
                  setAutoRechargeAmount(parseFloat(e.target.value))
                }
                placeholder="10.00"
              />
              <p className="text-sm text-muted-foreground">
                Amount to add when auto-recharge is triggered (minimum $10)
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="threshold">When threshold reaches ($)</Label>
              <Input
                id="threshold"
                type="number"
                step="0.01"
                min="0"
                value={autoRechargeThreshold}
                onChange={(e) =>
                  setAutoRechargeThreshold(parseFloat(e.target.value))
                }
                placeholder="1.00"
                disabled={!autoRechargeEnabled}
              />
              <p className="text-sm text-muted-foreground">
                Auto-recharge when your balance falls below this amount
              </p>
            </div>

            <Button
              onClick={handleSave}
              disabled={isSaving || !autoRechargeEnabled}
              className="w-full"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Settings"
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
