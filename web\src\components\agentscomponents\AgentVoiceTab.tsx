/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { AgentTabProps } from "@/types/agent.types";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Card } from "@/components/ui/card";
import { useRef, useState } from "react";
import 'react-image-crop/dist/ReactCrop.css';



export default function AgentVoiceTab( { agent, setAgent }: AgentTabProps) {

    const [transcriberSettings, setTranscriberSettings] = useState({
    provider: agent?.transcriber?.provider || '',
    language: agent?.transcriber?.language || '',
    model: agent?.transcriber?.model || '',
    confidenceThreshold: agent?.transcriber?.confidenceThreshold || 0.4
  });

  const [voiceSettings, setVoiceSettings] = useState({
    provider: agent?.voice?.provider || "11labs",
    model: agent?.voice?.model || "eleven_turbo_v2_5",
    voiceId: agent?.voice?.voiceId || "",
    stability: agent?.voice?.stability || 0.5,
  });

  const handleTranscriberChange = (field: string, value: string | number) => {
    const updated = { ...transcriberSettings, [field]: value }; 
    // Set default model when provider changes
  if (field === 'provider') {
    switch (value) {
      case 'deepgram':
        updated.model = 'nova-3';
        break;
      case 'elevenlabs':
        updated.model = 'scribe';
        break;
      case 'gladia':
        updated.model = 'fast';
        break;
      case 'google':
        updated.model = 'gemini-2.0-flash';
        break;
      case 'openai':
        updated.model = 'gpt-4o-transcribe';
        break;
      case 'speechmatics':
        updated.model = 'default';
        break;
      case 'talkscriber':
        updated.model = 'whisper';
        break;
      case 'assemblyai':
        updated.model = 'none'; // No model for Assembly AI
        break;
      default:
        updated.model = '';
    }
  }

  setTranscriberSettings(updated);

  setAgent({
    ...agent,
    transcriber: {
      ...agent.transcriber,
      provider: updated.provider,
      model: updated.model,
      language: updated.language,
      confidenceThreshold: updated.confidenceThreshold
    }
  });
  };

  const handleVoiceChange = (field: string, value: string | number) => {
    const updated = { ...voiceSettings, [field]: value };
    
    // Set default model when provider changes
    if (field === 'provider') {
      switch (value) {
        case '11labs':
          updated.model = 'eleven_turbo_v2_5';
          updated.voiceId = 'N2lVS1w4EtoT3dr4eOWO';
          break;
        case 'cartesia':
          updated.model = 'sonic_2';
          updated.voiceId = '3b554273-4299-48b9-9aaf-eefd438e3941';
          break;
        case 'rime':
          updated.model = 'mist';
          updated.voiceId = 'gerald';
          break;
        case 'playht':
          updated.model = 'playht2_turbo';
          updated.voiceId = 'melissa';
          break;
        case 'openai':
          updated.model = 'tts-1';
          updated.voiceId = 'alloy';
          break;
        case 'deepgram':
          updated.model = 'aura';
          updated.voiceId = 'asteria';
          break;
        default:
          updated.model = '';
      }
    }
    
    setVoiceSettings(updated);
    setAgent({
      ...agent,
      voice: {
        ...agent.voice,
        [field]: value
      }
    });
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
        <h3 className="text-lg font-semibold">Voice Configuration</h3>
      </div>

 <div className="space-y-4 mt-6">
    <h3 className="text-lg font-semibold">Transcriber Settings</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div className="space-y-2">
    <Label htmlFor="transcriber-provider">Provider</Label>
    <Select 
      value={transcriberSettings?.provider || ''} 
      onValueChange={(value) => handleTranscriberChange('provider', value)}
    >
      <SelectTrigger id="transcriber-provider" className="w-full">
        <SelectValue placeholder="Select provider" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="assemblyai">Assembly AI</SelectItem>
        <SelectItem value="deepgram">Deepgram</SelectItem>
        <SelectItem value="elevenlabs">Elevenlabs</SelectItem>
        <SelectItem value="gladia">Gladia</SelectItem>
        <SelectItem value="google">Google</SelectItem>
        <SelectItem value="openai">OpenAI</SelectItem>
        <SelectItem value="speechmatics">Speechmatics</SelectItem>
        <SelectItem value="talkscriber">Talkscriber</SelectItem>
      </SelectContent>
    </Select>
  </div>


      <div className="space-y-2">
        <Label htmlFor="transcriber-language">Language</Label>
        <Select 
          value={transcriberSettings?.language || 'en'} 
          onValueChange={(value) => handleTranscriberChange('language', value)}
        >
          <SelectTrigger id="transcriber-language" className="w-full">
            <SelectValue placeholder="Select language" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="en">English</SelectItem>
            <SelectItem value="en-US">English (US)</SelectItem>
            <SelectItem value="multi">Multi-language</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  
    <div className="space-y-2">
      <Label htmlFor="transcriber-model">Model</Label>
      <Select 
        value={transcriberSettings?.model || 'none'} 
        onValueChange={(value) => handleTranscriberChange('model', value)}
        disabled={transcriberSettings.provider === 'assemblyai'}
      >
        <SelectTrigger id="transcriber-model" className="w-full">
          <SelectValue placeholder="Select model" />
        </SelectTrigger>
        <SelectContent>
          {transcriberSettings.provider === 'deepgram' && (
            <>
              <SelectItem value="nova-3">Nova 3</SelectItem>
              <SelectItem value="nova-3-general">Nova 3 General</SelectItem>
              <SelectItem value="nova-3-medical">Nova 3 Medical</SelectItem>
              <SelectItem value="nova-2">Nova 2</SelectItem>
              <SelectItem value="nova-2-general">Nova 2 General</SelectItem>
              <SelectItem value="nova-2-meeting">Nova 2 Meeting</SelectItem>
              <SelectItem value="nova-2-phonecall">Nova 2 Phonecall</SelectItem>
              <SelectItem value="nova-2-finance">Nova 2 Finance</SelectItem>
              <SelectItem value="nova-2-conversational-ai">Nova 2 Conversational AI</SelectItem>
              <SelectItem value="nova-2-voicemail">Nova 2 Voicemail</SelectItem>
              <SelectItem value="nova-2-video">Nova 2 Video</SelectItem>
              <SelectItem value="nova-2-medical">Nova 2 Medical</SelectItem>
              <SelectItem value="nova-2-drive-thru">Nova 2 Drive Thru</SelectItem>
              <SelectItem value="nova-2-automotive">Nova 2 Automotive</SelectItem>
            </>
          )}
          {transcriberSettings.provider === 'elevenlabs' && (
            <SelectItem value="scribe">Scribe</SelectItem>
          )}
          {transcriberSettings.provider === 'gladia' && (
            <>
              <SelectItem value="fast">Fast</SelectItem>
              <SelectItem value="accurate">Accurate</SelectItem>
            </>
          )}
          {transcriberSettings.provider === 'google' && (
            <>
              <SelectItem value="gemini-2.0-flash">Gemini 2.0 Flash</SelectItem>
              <SelectItem value="gemini-2.0-flash-lite">Gemini 2.0 Flash Lite</SelectItem>
              <SelectItem value="gemini-1.5-flash-lite">Gemini 1.5 Flash Lite</SelectItem>
              <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
            </>
          )}
          {transcriberSettings.provider === 'openai' && (
            <>
              <SelectItem value="gpt-4o-transcribe">GPT-4o Transcribe</SelectItem>
              <SelectItem value="gpt-4o-mini-transcribe">GPT-4o Mini Transcribe</SelectItem>
            </>
          )}
          {transcriberSettings.provider === 'speechmatics' && (
            <SelectItem value="default">Default</SelectItem>
          )}
          {transcriberSettings.provider === 'talkscriber' && (
            <SelectItem value="whisper">Whisper</SelectItem>
          )}
          {transcriberSettings.provider === 'assemblyai' && (
          <SelectItem value="none">No models available</SelectItem>
        )}
        </SelectContent>
      </Select>
    </div>
  

      <div className="space-y-2">
        <div className="flex justify-between">
          <Label htmlFor="confidence-threshold">Confidence Threshold</Label>
          <span className="text-sm text-muted-foreground mt-3">
            {transcriberSettings.confidenceThreshold.toFixed(1)}
          </span>
        </div>
        <Slider
          id="confidence-threshold"
          value={[transcriberSettings.confidenceThreshold]}
          min={0}
          max={1}
          step={0.1}
          className="w-full"
          onValueChange={(value) => handleTranscriberChange('confidenceThreshold', value[0])}
        />
      </div>
    </div>

      {/* Voice Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Voice Configuration</h3>
        <Card className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="voice-provider">Voice Provider</Label>
              <Select 
                value={voiceSettings?.provider || ''} 
                onValueChange={(value) => handleVoiceChange('provider', value)}
              >
                <SelectTrigger id="voice-provider" className="w-full">
                  <SelectValue placeholder="Select provider" />
                </SelectTrigger>
                <SelectContent>
              <SelectItem value="11labs">ElevenLabs</SelectItem>
              <SelectItem value="cartesia">Cartesia</SelectItem>
              <SelectItem value="rime">Rime AI</SelectItem>
              <SelectItem value="playht">PlayHT</SelectItem>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="deepgram">Deepgram</SelectItem>
            </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
          <Label htmlFor="voice-model">Voice Model</Label>
          <Select 
            value={voiceSettings?.model || ''} 
            onValueChange={(value) => handleVoiceChange('model', value)}
          >
            <SelectTrigger id="voice-model" className="w-full">
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {voiceSettings.provider === '11labs' && (
                <>
                  <SelectItem value="eleven_turbo_v2_5">Eleven Turbo v2.5</SelectItem>
                  <SelectItem value="eleven_flash_v2_5">Eleven Flash 2.5</SelectItem>
                  <SelectItem value="eleven_flash_v2">Eleven Flash V2</SelectItem>
                  <SelectItem value="eleven_multilingual_v2">Eleven Multilingual v2</SelectItem>
                  <SelectItem value="eleven_english_v1">Eleven English v1</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'cartesia' && (
                <>
                  <SelectItem value="sonic_2">Sonic 2</SelectItem>
                  <SelectItem value="sonic_english">Sonic English</SelectItem>
                  <SelectItem value="sonic_multilingual">Sonic Multilingual</SelectItem>
                  <SelectItem value="sonic_preview">Sonic Preview</SelectItem>
                  <SelectItem value="sonic">Sonic</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'rime' && (
                <>
                  <SelectItem value="mist">Mist</SelectItem>
                  <SelectItem value="mistv2">Mist v2</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'playht' && (
                <>
                  <SelectItem value="playht2_turbo">PlayHT 2.0 Turbo</SelectItem>
                  <SelectItem value="playht2">PlayHT 2.0</SelectItem>
                  <SelectItem value="play3_mini">Play 3.0 Mini</SelectItem>
                  <SelectItem value="playdialog">PlayDialog</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'openai' && (
                <>
                  <SelectItem value="tts-1">TTS-1</SelectItem>
                  <SelectItem value="tts-1-hd">TTS-1-HD</SelectItem>
                  <SelectItem value="gpt-4o-mini-tts">GPT-4o Mini TTS</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'deepgram' && (
                <>
                  <SelectItem value="aura">Aura</SelectItem>
                  <SelectItem value="aura_2">Aura 2</SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="voice-id">Voice ID</Label>
          <Select 
            value={voiceSettings?.voiceId || ''} 
            onValueChange={(value) => handleVoiceChange('voiceId', value)}
          >
            <SelectTrigger id="voice-id" className="w-full">
              <SelectValue placeholder="Select voice" />
            </SelectTrigger>
            <SelectContent>
              {voiceSettings.provider === '11labs' && (
                <>
                  <SelectItem value="N2lVS1w4EtoT3dr4eOWO">Callum</SelectItem>
                  <SelectItem value="piTKgcLEGmPE4e6mEKli">Nicole</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'cartesia' && (
                <>
                  <SelectItem value="3b554273-4299-48b9-9aaf-eefd438e3941">Indian Lady</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'rime' && (
                <>
                  <SelectItem value="gerald">Gerald</SelectItem>
                  <SelectItem value="gultch">Gultch</SelectItem>
                  <SelectItem value="rob">Rob</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'playht' && (
                <>
                  <SelectItem value="melissa">Melissa</SelectItem>
                  <SelectItem value="davis">Davis</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'openai' && (
                <>
                  <SelectItem value="alloy">Alloy</SelectItem>
                  <SelectItem value="shimmer">Shimmer</SelectItem>
                </>
              )}
              {voiceSettings.provider === 'deepgram' && (
                <>
                  <SelectItem value="asteria">Asteria</SelectItem>
                  <SelectItem value="perseus">Perseus</SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        </div>

            <div className="space-y-6">
              <Label htmlFor="stability">Stability: {voiceSettings.stability}</Label>
              <Slider
                id="stability"
                min={0}
                max={1}
                step={0.1}
                value={[voiceSettings.stability]}
                onValueChange={(value) => handleVoiceChange('stability', value[0])}
              />
            </div>
          </div>
        </Card>
      </div>

  </div>
    </div>
  );
}