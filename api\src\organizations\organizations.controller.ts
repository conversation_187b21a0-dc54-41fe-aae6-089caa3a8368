import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto, UpdateOrganizationBillingDto } from './dto/organization.dto';
import { UpdateOrganizationSettingsDto } from './dto/update-organization-settings.dto';
import { UsersService } from '../users/users.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Organization } from './interfaces/organization.interface';
import { User } from '../users/interfaces/user.interface';

interface RequestWithUser {
  user: {
    userId: string;
    email: string;
    role: string;
  };
}

@ApiTags('organizations')
@Controller('organizations')
export class OrganizationsController {
  constructor(
    private readonly organizationsService: OrganizationsService,
    private readonly usersService: UsersService,
    @InjectModel('Organization') private organizationModel: Model<Organization>,
    @InjectModel('User') private userModel: Model<User>
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new organization' })
  @ApiResponse({ status: 201, description: 'Organization created successfully' })
  @ApiBody({ type: CreateOrganizationDto })
  async create(@Body() createOrganizationDto: CreateOrganizationDto) {
    try {
      return await this.organizationsService.create(createOrganizationDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create organization',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all organizations' })
  @ApiResponse({ status: 200, description: 'Returns all organizations' })
  async findAll() {
    try {
      return await this.organizationsService.findAll();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch organizations',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('my-organizations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get organizations for the current user' })
  @ApiResponse({ status: 200, description: 'Returns organizations for the current user' })
  async findMyOrganizations(@Req() req: RequestWithUser) {
    try {
      const userId = req.user.userId;
      return await this.organizationsService.findByUser(userId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch organizations',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get an organization by ID' })
  @ApiResponse({ status: 200, description: 'Returns the organization' })
  async findOne(@Param('id') id: string, @Req() req: RequestWithUser) {
    try {
      const organization = await this.organizationsService.findOne(id);

      // Check if user has access to this organization
      if (req.user.role !== 'superadmin') {
        const userOrgs = await this.organizationsService.findByUser(req.user.userId);
        const hasAccess = userOrgs.some(org => org._id.toString() === id);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      return organization;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch organization',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/credits')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get available credits information for an organization' })
  @ApiResponse({ status: 200, description: 'Returns credits information' })
  async getAvailableCredits(@Param('id') id: string, @Req() req: RequestWithUser) {
    try {
      // Check if user has access to this organization
      if (req.user.role !== 'superadmin') {
        const userOrgs = await this.organizationsService.findByUser(req.user.userId);
        const hasAccess = userOrgs.some(org => org._id.toString() === id);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      return await this.organizationsService.getAvailableCredits(id);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch credits information',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an organization' })
  @ApiResponse({ status: 200, description: 'Organization updated successfully' })
  @ApiBody({ type: UpdateOrganizationDto })
  async update(
    @Param('id') id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Req() req: RequestWithUser
  ) {
    try {
      // Check if user has admin access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      return await this.organizationsService.update(id, updateOrganizationDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update organization',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id/billing')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update organization billing settings' })
  @ApiResponse({ status: 200, description: 'Organization billing updated successfully' })
  @ApiBody({ type: UpdateOrganizationBillingDto })
  async updateBilling(
    @Param('id') id: string,
    @Body() updateBillingDto: UpdateOrganizationBillingDto
  ) {
    try {
      return await this.organizationsService.updateBilling(id, updateBillingDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update organization billing',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id/settings')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update organization settings' })
  @ApiResponse({ status: 200, description: 'Organization settings updated successfully' })
  @ApiBody({ type: UpdateOrganizationSettingsDto })
  async updateSettings(
    @Param('id') id: string,
    @Body() updateSettingsDto: UpdateOrganizationSettingsDto,
    @Req() req: RequestWithUser
  ) {
    try {
      // Check if user has admin access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      return await this.organizationsService.updateSettings(id, updateSettingsDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update organization settings',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an organization' })
  @ApiResponse({ status: 200, description: 'Organization deleted successfully' })
  async remove(@Param('id') id: string) {
    try {
      await this.organizationsService.delete(id);
      return { message: 'Organization deleted successfully' };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete organization',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/users/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Add a user to an organization' })
  @ApiResponse({ status: 200, description: 'User added to organization successfully' })
  async addUser(
    @Param('id') id: string,
    @Param('userId') userId: string,
    @Body('isAdmin') isAdmin: boolean,
    @Req() req: RequestWithUser
  ) {
    try {
      // Check if user has admin access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      return await this.organizationsService.addUserToOrganization(id, userId, isAdmin);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to add user to organization',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id/users/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove a user from an organization' })
  @ApiResponse({ status: 200, description: 'User removed from organization successfully' })
  async removeUser(
    @Param('id') id: string,
    @Param('userId') userId: string,
    @Req() req: RequestWithUser
  ) {
    try {
      // Check if user has admin access to this organization
      if (req.user.role !== 'superadmin') {
        const userAdminOrgs = await this.organizationsService.findByAdmin(req.user.userId);
        const hasAccess = userAdminOrgs.some(org => org._id.toString() === id);

        if (!hasAccess) {
          throw new HttpException('Unauthorized access to organization', HttpStatus.FORBIDDEN);
        }
      }

      return await this.organizationsService.removeUserFromOrganization(id, userId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to remove user from organization',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('initialize-monthly-credits')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Initialize monthly credits for all existing organizations' })
  @ApiResponse({ status: 200, description: 'Monthly credits initialized successfully' })
  async initializeMonthlyCredits() {
    try {
      // Get all organizations
      const organizations = await this.organizationModel.find().exec();
      const results = [];

      // Process each organization
      for (const organization of organizations) {
        const orgId = organization._id.toString();

        try {
          // Initialize monthly credits for this organization
          await this.organizationsService.initializeMonthlyCreditsForOrganization(orgId);
          console.log(`Initialized monthly credits for organization ${orgId}`);
          results.push({ organizationId: orgId, status: 'success' });
        } catch (error) {
          console.error(`Error initializing monthly credits for organization ${orgId}:`, error);
          results.push({ organizationId: orgId, status: 'error', message: error.message });
        }
      }

      return {
        message: 'Monthly credits initialization completed',
        results
      };
    } catch (error) {
      console.error('Error initializing monthly credits:', error);
      throw new HttpException(
        error.message || 'Failed to initialize monthly credits',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('fix-organization-users')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superadmin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fix organization users by updating their organizationId field' })
  @ApiResponse({ status: 200, description: 'Organization users fixed successfully' })
  async fixOrganizationUsers() {
    try {
      // Get all organizations
      const organizations = await this.organizationModel.find().exec();
      const results = [];

      // Process each organization
      for (const organization of organizations) {
        const orgId = organization._id.toString();
        const allUsers = [...new Set([
          ...organization.adminUsers.map(id => id.toString()),
          ...organization.users.map(id => id.toString())
        ])];

        console.log(`Processing organization ${orgId} with ${allUsers.length} users`);

        // Update each user's organizationId
        for (const userId of allUsers) {
          try {
            const user = await this.userModel.findById(userId).exec();
            if (!user) {
              console.error(`User ${userId} not found`);
              results.push({ userId, status: 'error', message: 'User not found' });
              continue;
            }

            // Update user's organizationId
            await this.usersService.updateUser(userId, { organizationId: organization._id.toString() });
            console.log(`Updated organizationId for user ${userId} to ${orgId}`);
            results.push({ userId, status: 'success', organizationId: orgId });
          } catch (error) {
            console.error(`Error updating user ${userId}:`, error);
            results.push({ userId, status: 'error', message: error.message });
          }
        }
      }

      return {
        message: 'Organization users fixed successfully',
        results
      };
    } catch (error) {
      console.error('Error fixing organization users:', error);
      throw new HttpException(
        error.message || 'Failed to fix organization users',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
