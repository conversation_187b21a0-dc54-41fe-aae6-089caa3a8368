import { ConfigService } from "@nestjs/config";
export interface EmailTemplate {
    subject: string;
    htmlContent: string;
    textContent: string;
}
export declare class EmailService {
    private configService;
    private mailjet;
    constructor(configService: ConfigService);
    sendEmail(to: string, subject: string, htmlContent: string, textContent?: string): Promise<boolean>;
    generateCreditRunoutEmail(clientName: string, currentCredit: number, fundingUrl: string): EmailTemplate;
    generateCreditWarningEmail(clientName: string, currentCredit: number, fundingUrl: string): EmailTemplate;
    sendCreditRunoutNotification(clientName: string, email: string, currentCredit: number, fundingUrl: string): Promise<boolean>;
    sendCreditWarningNotification(clientName: string, email: string, currentCredit: number, fundingUrl: string): Promise<boolean>;
}
