"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus } from "lucide-react";
import { KnowledgeBaseSidebar, sidebarItems } from "@/components/braincomponents/KnowledgeBaseSidebar";
import { AddParagraphModal } from "@/components/braincomponents/AddParagraphModal";
import { AddWebLinkModal } from "@/components/braincomponents/AddWebLinkModal";
import { FileUploadModal } from "@/components/braincomponents/FileUploadModal";
import { ParagraphsView } from "@/components/braincomponents/ParagraphsView";
import { WebLinksView } from "@/components/braincomponents/WebLinksView";
import { FilesView } from "@/components/braincomponents/FilesView";
import { SpreadsheetsView } from "@/components/braincomponents/SpreadsheetsView";

// Types
type Paragraph = {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
};

type WebLink = {
  id: string;
  url: string;
  title: string;
  description: string;
  status: "active" | "broken";
  lastChecked: Date;
};

type FileItem = {
  id: string;
  title: string;
  filename: string;
  size: number;
  type: string;
  uploadedAt: Date;
};

type Spreadsheet = {
  id: string;
  title: string;
  filename: string;
  rows: number;
  columns: number;
  lastUpdated: Date;
};

export default function BrainContent() {
  const [currentPath, setCurrentPath] = useState("paragraphs");
  const [searchQuery, setSearchQuery] = useState("");
  
  // Modal states
  const [isParagraphModalOpen, setIsParagraphModalOpen] = useState(false);
  const [isWebLinkModalOpen, setIsWebLinkModalOpen] = useState(false);
  const [isFileModalOpen, setIsFileModalOpen] = useState(false);
  const [isSpreadsheetModalOpen, setIsSpreadsheetModalOpen] = useState(false);

  // Content states
  const [paragraphs, setParagraphs] = useState<Paragraph[]>([]);
  const [webLinks, setWebLinks] = useState<WebLink[]>([]);
  const [files, setFiles] = useState<FileItem[]>([]);
  const [spreadsheets, setSpreadsheets] = useState<Spreadsheet[]>([]);

  const handleAddParagraph = (title: string, content: string, tags: string[]) => {
    setParagraphs([...paragraphs, {
      id: Math.random().toString(36).substr(2, 9),
      title,
      content,
      tags,
      createdAt: new Date(),
    }]);
  };

  const handleAddWebLink = (url: string, title: string, description: string) => {
    setWebLinks([...webLinks, {
      id: Math.random().toString(36).substr(2, 9),
      url,
      title,
      description,
      status: "active",
      lastChecked: new Date(),
    }]);
  };

  const handleUploadFile = (file: File, title: string) => {
    setFiles([...files, {
      id: Math.random().toString(36).substr(2, 9),
      title,
      filename: file.name,
      size: file.size,
      type: file.type,
      uploadedAt: new Date(),
    }]);
  };

  const handleUploadSpreadsheet = (file: File, title: string) => {
    setSpreadsheets([...spreadsheets, {
      id: Math.random().toString(36).substr(2, 9),
      title,
      filename: file.name,
      rows: 0, // This would come from actual file parsing
      columns: 0, // This would come from actual file parsing
      lastUpdated: new Date(),
    }]);
  };

  const handleDeleteParagraph = (id: string) => {
    setParagraphs(paragraphs.filter(p => p.id !== id));
  };

  const handleDeleteWebLink = (id: string) => {
    setWebLinks(webLinks.filter(l => l.id !== id));
  };

  const handleDeleteFile = (id: string) => {
    setFiles(files.filter(f => f.id !== id));
  };

  const handleDeleteSpreadsheet = (id: string) => {
    setSpreadsheets(spreadsheets.filter(s => s.id !== id));
  };

  const handleNavigate = (path: string) => {
    setCurrentPath(path);
  };

  return (
    <div className="flex h-[calc(100vh-4rem)]">
      {/* Sidebar */}
      <div className="w-64 border-r p-4 bg-white dark:bg-gray-800 dark:border-gray-700">
        <h2 className="text-xl font-semibold mb-4">Knowledge Base</h2>
        <KnowledgeBaseSidebar
          items={sidebarItems}
          currentPath={currentPath}
          onNavigate={handleNavigate}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold capitalize">
            {currentPath.replace("-", " ")}
          </h1>
          <Button
            className=" text-white"
            onClick={() => {
              if (currentPath === "paragraphs") setIsParagraphModalOpen(true);
              else if (currentPath === "web-links") setIsWebLinkModalOpen(true);
              else if (currentPath === "files") setIsFileModalOpen(true);
              else setIsSpreadsheetModalOpen(true);
            }}
          >
            <Plus className="h-5 w-5 " />
            Add {currentPath === "paragraphs" ? "Paragraph" : 
                 currentPath === "web-links" ? "Link" :
                 currentPath === "files" ? "File" : "Spreadsheet"}
          </Button>
        </div>

        <div className="mb-8">
          <Input
            placeholder={`Search for the exact phrases you have in your ${currentPath}...`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-2xl"
          />
        </div>

        {/* Content Areas */}
        {currentPath === "paragraphs" && 
          <ParagraphsView 
            paragraphs={paragraphs} 
            isEmpty={paragraphs.length === 0}
            onAddClick={() => setIsParagraphModalOpen(true)}
            onDelete={handleDeleteParagraph}
          />
        }
        
        {currentPath === "web-links" && 
          <WebLinksView 
            webLinks={webLinks} 
            isEmpty={webLinks.length === 0}
            onAddClick={() => setIsWebLinkModalOpen(true)}
            onDelete={handleDeleteWebLink}
          />
        }
        
        {currentPath === "files" && 
          <FilesView 
            files={files} 
            isEmpty={files.length === 0}
            onAddClick={() => setIsFileModalOpen(true)}
            onDelete={handleDeleteFile}
          />
        }
        
        {currentPath === "spreadsheets" && 
          <SpreadsheetsView 
            spreadsheets={spreadsheets} 
            isEmpty={spreadsheets.length === 0}
            onAddClick={() => setIsSpreadsheetModalOpen(true)}
            onDelete={handleDeleteSpreadsheet}
          />
        }

        {/* Modals */}
        <AddParagraphModal
          isOpen={isParagraphModalOpen}
          onClose={() => setIsParagraphModalOpen(false)}
          onAdd={handleAddParagraph}
        />

        <AddWebLinkModal
          isOpen={isWebLinkModalOpen}
          onClose={() => setIsWebLinkModalOpen(false)}
          onAdd={handleAddWebLink}
        />

        <FileUploadModal
          type="document"
          isOpen={isFileModalOpen}
          onClose={() => setIsFileModalOpen(false)}
          onUpload={handleUploadFile}
        /> 

         <FileUploadModal
          type="spreadsheet"
          isOpen={isSpreadsheetModalOpen}
          onClose={() => setIsSpreadsheetModalOpen(false)}
          onUpload={handleUploadSpreadsheet}
        />
      </div>
    </div>
  );
}