"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsController = void 0;
const common_1 = require("@nestjs/common");
const contacts_service_1 = require("./contacts.service");
const contact_dto_1 = require("./dto/contact.dto");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const logger_service_1 = require("../logger/logger.service");
const platform_express_1 = require("@nestjs/platform-express");
const XLSX = __importStar(require("xlsx"));
let ContactsController = class ContactsController {
    constructor(contactsService, loggerService) {
        this.contactsService = contactsService;
        this.loggerService = loggerService;
    }
    normalizePhoneNumber(value) {
        if (value.includes('E+')) {
            const [base, exponent] = value.split('E+');
            const number = parseFloat(base) * Math.pow(10, parseInt(exponent));
            return number.toString();
        }
        return value.replace(/[^\d+]/g, '');
    }
    async callData(body) {
        try {
            const toolCall = body.message.toolCalls[0];
            const toolCallId = toolCall.id;
            const { number, name } = toolCall.function.arguments;
            const data = await this.contactsService.callData(number, name);
            return {
                results: [
                    {
                        toolCallId,
                        result: data,
                    },
                ],
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error retrieving call data", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    create(createContactDto, req) {
        const userName = req.user.fullName || req.user.email.split('@')[0];
        return this.contactsService.create(createContactDto, userName);
    }
    async importContacts(req) {
        try {
            const userName = req.user.fullName || req.user.email.split('@')[0];
            const contacts = await this.contactsService.importContacts(userName);
            return contacts;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || "Error importing contacts", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async webhookContacts(payload) {
        try {
            await this.loggerService.log("Received webhook for contacts.");
            const contacts = await this.contactsService.processContacts(payload.value);
            await this.loggerService.log(`Webhook processing completed. ${contacts.length} new contacts imported.`);
            return {
                message: "Contacts processed successfully",
                contacts,
            };
        }
        catch (error) {
            await this.loggerService.error("Error processing webhook contacts", error);
            throw new common_1.HttpException(error.message || "Error processing webhook contacts", common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadContacts(file, req) {
        try {
            const userName = req.user.fullName || req.user.email.split('@')[0];
            await this.loggerService.log('Processing contact upload...');
            if (!file) {
                throw new common_1.BadRequestException('No file uploaded');
            }
            const extension = file.originalname.split('.').pop()?.toLowerCase();
            if (!['csv', 'xlsx', 'xls'].includes(extension)) {
                throw new common_1.BadRequestException('Invalid file type. Only CSV and Excel files are allowed');
            }
            let contacts = [];
            const fileName = file.originalname;
            if (extension === 'csv') {
                const content = file.buffer.toString();
                const lines = content.split('\n');
                const firstLine = lines[0];
                const delimiter = firstLine.includes(';') ? ';' : ',';
                const headers = lines[0].toLowerCase().split(delimiter).map(h => h.trim());
                const nameIndex = headers.findIndex(h => ['name', 'contactname', 'contact name', 'fullname'].includes(h));
                const phoneIndex = headers.findIndex(h => ['phone', 'phonenumber', 'phone number', 'mobile', 'number'].includes(h));
                const customerIdIndex = headers.findIndex(h => ['customerid', 'customer id', 'id', 'client id', 'contact id', 'clientid', 'contactid'].includes(h));
                const campaignIndex = headers.findIndex(h => ['campaign', 'campaigns'].includes(h));
                const regionIndex = headers.findIndex(h => ['region', 'timezone', 'time zone', 'zone'].includes(h));
                if (nameIndex === -1 || phoneIndex === -1) {
                    throw new common_1.BadRequestException('CSV must contain name and phone number columns');
                }
                for (let i = 1; i < lines.length; i++) {
                    if (!lines[i].trim())
                        continue;
                    const values = lines[i].split(delimiter).map(v => v.trim());
                    const phoneNumber = this.normalizePhoneNumber(values[phoneIndex]);
                    contacts.push({
                        fileName,
                        contactName: values[nameIndex],
                        phoneNumber: phoneNumber,
                        customerId: customerIdIndex !== -1 ? values[customerIdIndex] : null,
                        campaignNames: campaignIndex !== -1 ? values[campaignIndex].split(',').map(c => c.trim()).filter(Boolean) : [],
                        region: regionIndex !== -1 ? values[regionIndex] : ""
                    });
                }
            }
            else {
                try {
                    const workbook = XLSX.read(file.buffer, { type: 'buffer' });
                    const sheet = workbook.Sheets[workbook.SheetNames[0]];
                    const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
                    if (!Array.isArray(rows[0])) {
                        throw new common_1.BadRequestException('Invalid Excel format: headers row is missing');
                    }
                    const headers = rows[0].map((h) => String(h).toLowerCase().trim());
                    const nameIndex = headers.findIndex((h) => ['name', 'contactname', 'contact name', 'fullname'].includes(h));
                    const phoneIndex = headers.findIndex((h) => ['phone', 'phonenumber', 'phone number', 'mobile'].includes(h));
                    const customerIdIndex = headers.findIndex((h) => ['customerid', 'customer id', 'id', 'client id', 'contact id', 'clientid', 'contactid'].includes(h));
                    const campaignIndex = headers.findIndex((h) => ['campaign', 'campaigns'].includes(h));
                    const regionIndex = headers.findIndex((h) => ['region', 'timezone', 'time zone', 'zone'].includes(h));
                    if (nameIndex === -1 || phoneIndex === -1) {
                        throw new common_1.BadRequestException('Excel file must contain name and phone number columns');
                    }
                    for (let i = 1; i < rows.length; i++) {
                        const row = rows[i];
                        if (!row || !row[nameIndex] || !row[phoneIndex])
                            continue;
                        contacts.push({
                            contactName: String(row[nameIndex]).trim(),
                            phoneNumber: String(row[phoneIndex]).trim(),
                            customerId: customerIdIndex !== -1 ? String(row[customerIdIndex]).trim() : null,
                            campaignNames: campaignIndex !== -1 ? String(row[campaignIndex]).split(',').map(c => c.trim()).filter(Boolean) : [],
                            region: regionIndex !== -1 ? String(row[regionIndex]).trim() : ""
                        });
                    }
                }
                catch (error) {
                    throw new common_1.BadRequestException(`Error parsing Excel file: ${error.message}`);
                }
            }
            contacts = contacts.filter(contact => contact.contactName && contact.phoneNumber &&
                contact.contactName.trim() !== '' &&
                contact.phoneNumber.trim() !== '');
            if (contacts.length === 0) {
                throw new common_1.BadRequestException('No valid contacts found in file');
            }
            const result = await this.contactsService.uploadContacts(contacts, userName);
            return {
                ...result,
                fileName
            };
        }
        catch (error) {
            await this.loggerService.error('Error processing contact upload:', error.message);
            throw new common_1.HttpException(error.message || 'Error processing contact upload', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(page, limit, search, filterType = 'name', campaignId, noCampaign) {
        try {
            if (!page && !limit) {
                const contacts = await this.contactsService.findAll(undefined, undefined, search, filterType, campaignId, noCampaign === 'true');
                return contacts;
            }
            const pageNum = Number(page) || 1;
            const limitNum = Number(limit) || 20;
            if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
                throw new common_1.BadRequestException('Invalid pagination parameters');
            }
            const result = await this.contactsService.findAll(pageNum, limitNum, search, filterType, campaignId, noCampaign === 'true');
            return result;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Error retrieving paginated contacts', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    findById(id) {
        return this.contactsService.findById(id);
    }
    update(id, updateContactDto) {
        return this.contactsService.update(id, updateContactDto);
    }
    remove(id) {
        return this.contactsService.remove(id);
    }
};
exports.ContactsController = ContactsController;
__decorate([
    (0, common_1.Post)("call-data"),
    (0, swagger_1.ApiOperation)({ summary: "Get contact call data by phone number and name" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "callData", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: "Create a new contact" }),
    (0, swagger_1.ApiResponse)({ status: 201, description: "Contact successfully created" }),
    (0, swagger_1.ApiBody)({ type: contact_dto_1.ContactDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [contact_dto_1.ContactDto, Object]),
    __metadata("design:returntype", void 0)
], ContactsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)("import-contacts"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: "Import contacts from external API and save to DB" }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "importContacts", null);
__decorate([
    (0, common_1.Post)("webhook-contacts"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: "Receive contacts via webhook and save to DB" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "webhookContacts", null);
__decorate([
    (0, common_1.Post)('upload'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Upload contacts from CSV/Excel file' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "uploadContacts", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: "Get all contacts" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "List of contacts" }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Items per page (default: 20)' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term for contacts' }),
    (0, swagger_1.ApiQuery)({ name: 'campaign', required: false, description: 'Filter contacts by campaign ID' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('filterType')),
    __param(4, (0, common_1.Query)('campaignId')),
    __param(5, (0, common_1.Query)('noCampaign')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, String]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: "Get a contact by ID" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "The requested contact" }),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ContactsController.prototype, "findById", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: "Update a contact by ID" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Contact updated" }),
    (0, swagger_1.ApiBody)({ type: contact_dto_1.ContactDto }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, contact_dto_1.ContactDto]),
    __metadata("design:returntype", void 0)
], ContactsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiOperation)({ summary: "Delete a contact by ID" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Contact deleted" }),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ContactsController.prototype, "remove", null);
exports.ContactsController = ContactsController = __decorate([
    (0, swagger_1.ApiTags)("Contacts"),
    (0, common_1.Controller)("contacts"),
    __metadata("design:paramtypes", [contacts_service_1.ContactsService,
        logger_service_1.LoggerService])
], ContactsController);
//# sourceMappingURL=contacts.controller.js.map