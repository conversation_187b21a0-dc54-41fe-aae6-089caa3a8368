import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  <PERSON><PERSON>umber,
  IsDate,
  IsEnum,
  Min,
  <PERSON>,
  IsOptional,
  IsArray,
} from "class-validator";

export class CampaignDto {
  @ApiProperty({ description: "Name of the user who created the campaign", required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;
  @ApiProperty({ description: "Campaign name" })
  @IsString()
  name: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  contacts?: string[];

  @ApiProperty({ description: "Number of concurrent calls" })
  @IsNumber()
  @Min(1)
  concurrentCalls: number;

  @ApiProperty({ description: "Daily cost of the campaign" })
  @IsNumber()
  @Min(0)
  dailyCost: number;

  @ApiProperty({ description: "Campaign start date" })
  startDate: Date;

  @ApiProperty({ description: "Campaign end date" })
  endDate: Date;

  @ApiProperty({ description: "Success rate percentage", required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  successRate?: number;

  @ApiProperty({
    description: "Overall sentiment of the campaign",
    enum: ["positive", "neutral", "negative"],
    required: false,
  })
  @IsOptional()
  @IsEnum(["positive", "neutral", "negative"])
  sentiment?: "positive" | "neutral" | "negative";

  @ApiProperty({
    description: "Campaign status",
    enum: ["active", "paused", "completed"],
    required: false,
  })
  @IsOptional()
  @IsEnum(["active", "paused", "completed"])
  status?: "active" | "paused" | "completed";

  @ApiProperty({
    description: "Interval in minutes between batches of scheduled calls",
    required: false,
    default: 3
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(60)
  batchIntervalMinutes?: number;
}

export class UpdateCampaignStatusDto {
  @ApiProperty({
    description: "Campaign status",
    enum: ["active", "paused", "completed"],
  })
  @IsEnum(["active", "paused", "completed"])
  status: "active" | "paused" | "completed";
}
