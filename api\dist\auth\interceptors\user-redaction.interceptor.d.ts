import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
export interface FullUserType {
    userId?: string;
    email?: string;
    role?: string;
    fullName?: string;
    password?: string;
    isApproved?: boolean;
    refreshToken?: string;
}
export declare class UserRedactionInterceptor implements NestInterceptor {
    intercept(_context: ExecutionContext, next: CallHandler): Observable<any>;
}
