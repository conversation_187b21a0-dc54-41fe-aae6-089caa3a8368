"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditModule = void 0;
const common_1 = require("@nestjs/common");
const credit_middleware_1 = require("./credit.middleware");
const users_module_1 = require("../users/users.module");
const global_settings_module_1 = require("../global-settings/global-settings.module");
const billing_module_1 = require("../billing/billing.module");
const mongoose_1 = require("@nestjs/mongoose");
const user_schema_1 = require("../users/schemas/user.schema");
const auto_recharge_service_1 = require("./auto-recharge.service");
const organizations_module_1 = require("../organizations/organizations.module");
const credit_gateway_1 = require("./credit.gateway");
let CreditModule = class CreditModule {
    configure(consumer) {
        consumer
            .apply(credit_middleware_1.CreditCheckMiddleware)
            .forRoutes('*');
    }
};
exports.CreditModule = CreditModule;
exports.CreditModule = CreditModule = __decorate([
    (0, common_1.Module)({
        imports: [
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            global_settings_module_1.GlobalSettingsModule,
            (0, common_1.forwardRef)(() => billing_module_1.BillingModule),
            (0, common_1.forwardRef)(() => organizations_module_1.OrganizationsModule),
            mongoose_1.MongooseModule.forFeature([{ name: 'User', schema: user_schema_1.UserSchema }]),
        ],
        providers: [credit_middleware_1.CreditCheckMiddleware, auto_recharge_service_1.AutoRechargeService, credit_gateway_1.CreditGateway],
        exports: [credit_middleware_1.CreditCheckMiddleware, auto_recharge_service_1.AutoRechargeService, credit_gateway_1.CreditGateway],
    })
], CreditModule);
//# sourceMappingURL=credit.module.js.map