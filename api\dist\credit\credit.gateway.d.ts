import { OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
export declare class CreditGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
    server: Server;
    private logger;
    private userSocketMap;
    afterInit(): void;
    handleConnection(client: Socket): void;
    handleDisconnect(client: Socket): void;
    registerUserSocket(userId: string, socketId: string): void;
    handleRegisterUser(client: Socket, payload: {
        userId: string;
    }): {
        success: boolean;
        message: string;
    };
    handlePing(client: Socket, payload: any): {
        success: boolean;
        timestamp: string;
    };
    sendCreditUpdate(userId: string, credits: number): boolean;
    sendOrganizationCreditUpdate(organizationId: string, credits: number): void;
}
