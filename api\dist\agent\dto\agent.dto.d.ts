export declare class ActionDto {
    name: string;
    type: 'sendSMS' | 'sendEmail' | 'transferCall' | 'endCall' | 'extractInfo' | 'calendar';
    callType: 'inbound' | 'outbound' | 'both';
    trigger?: string;
    phoneNumber?: string;
    smsContent?: string;
}
export declare class ModelDto {
    provider: string;
    model: string;
    temperature: number;
    messages: {
        role: string;
        content: string;
    }[];
    maxTokens?: number;
    knowledgeBaseId?: string;
    emotionRecognitionEnabled: boolean;
}
export declare class VoiceDto {
    model: string;
    style: number;
    voiceId: string;
    provider: string;
    stability: number;
    similarityBoost: number;
    useSpeakerBoost: boolean;
    inputMinCharacters: number;
    inputPunctuationBoundaries: string[];
}
export declare class AgentDto {
    id: string;
    orgId: string;
    name: string;
    voice: VoiceDto;
    createdAt: Date;
    updatedAt: Date;
    model: ModelDto;
    recordingEnabled: boolean;
    firstMessage: string;
    voicemailMessage: string;
    endCallFunctionEnabled: boolean;
    endCallMessage: string;
    transcriber: any;
    clientMessages: string[];
    serverMessages: string[];
    serverUrl: string;
    endCallPhrases: string[];
    hipaaEnabled: boolean;
    maxDurationSeconds: number;
    backgroundSound: string;
    backchannelingEnabled: boolean;
    analysisPlan: any;
    voicemailDetection: any;
    backgroundDenoisingEnabled: boolean;
    messagePlan: any;
    startSpeakingPlan: any;
    stopSpeakingPlan: any;
    compliancePlan: any;
    isServerUrlSecretSet: boolean;
    actions?: ActionDto[];
}
