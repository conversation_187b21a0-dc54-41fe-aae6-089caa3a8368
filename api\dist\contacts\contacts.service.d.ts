import { OnModuleInit } from "@nestjs/common";
import { Model } from "mongoose";
import { ContactDto, UploadContactDto } from "./dto/contact.dto";
import { ContactDocument } from "./interfaces/contact.interface";
import { CampaignDocument } from "../campaign/interfaces/campaign.interface";
import { CampaignService } from "../campaign/campaign.service";
import { LoggerService } from "src/logger/logger.service";
import { ScheduledCallService } from "src/scheduled-call/scheduled-call.service";
export declare class ContactsService implements OnModuleInit {
    private readonly contactModel;
    private readonly campaignModel;
    private readonly loggerService;
    private readonly scheduledCallService;
    private readonly campaignService;
    constructor(contactModel: Model<ContactDocument>, campaignModel: Model<CampaignDocument>, loggerService: LoggerService, scheduledCallService: ScheduledCallService, campaignService: CampaignService);
    onModuleInit(): Promise<void>;
    private normalizePhoneNumber;
    private getRegionFromPhoneNumber;
    create(createContactDto: ContactDto, userName: string): Promise<ContactDocument>;
    update(id: string, updateContactDto: ContactDto): Promise<ContactDocument>;
    findAll(page?: number, limit?: number, search?: string, filterType?: 'name' | 'campaign', campaignId?: string, noCampaign?: boolean): Promise<ContactDocument[]>;
    findById(id: string): Promise<ContactDocument>;
    remove(id: string): Promise<void>;
    searchContacts(query: string): Promise<ContactDocument[]>;
    filterBycampaign(campaign: string[]): Promise<ContactDocument[]>;
    getToken(): Promise<string>;
    processContacts(externalContacts: any[]): Promise<ContactDocument[]>;
    callData(number: string, name: string): Promise<any>;
    importContacts(userName: string): Promise<ContactDocument[]>;
    uploadContacts(contacts: UploadContactDto[], userName: string): Promise<any>;
}
