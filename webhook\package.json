{"name": "webhook", "version": "1.0.0", "description": ".", "main": "src/app.ts", "scripts": {"start": "ts-node src/app.ts", "build": "tsc", "dev": "ts-node-dev src/app.ts"}, "dependencies": {"axios": "^1.8.3", "cors": "^2.8.5", "express": "^4.17.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.13", "ts-node": "^10.4.0", "ts-node-dev": "^1.1.8", "typescript": "^4.4.3"}, "author": "", "license": "ISC"}