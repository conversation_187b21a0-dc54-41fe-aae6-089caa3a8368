"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalSettingsSchema = void 0;
const mongoose_1 = require("mongoose");
exports.GlobalSettingsSchema = new mongoose_1.Schema({
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    lastUpdatedBy: { type: String },
});
exports.GlobalSettingsSchema.pre("save", function (next) {
    this.updatedAt = new Date();
    next();
});
//# sourceMappingURL=global-settings.schema.js.map